import type { FieldConfig } from "@/components/common/DynamicForm/DynamicForm.types";
import DynamicSearch from "@/components/common/DynamicSearch/DynamicSearch";
import type { DynamicDataGridColumn } from "@/components/common/DynamicTable/DynamicDataGrid";
import DynamicDataGrid from "@/components/common/DynamicTable/DynamicDataGrid";
import ActionMenu from "@/components/feature/UserMaster/UserList/ActionMenu";
import { getTempDepartmentList } from "@/features/department/state/departmentSelector";
import { getOrganizationList } from "@/features/organization/state/organizationSelector";
import { getRoleList } from "@/features/role/state/roleSelector";
import { searchUser } from "@/features/users/api/userMasterApis";
import { selectUserProfile } from "@/features/users/state/usersSelectors";
import { UserMasterInfo, UserMasterSearchRequest } from "@/features/users/types/userMasterModels";
import { useTempDepartmentToStore } from "@/hooks/useDepartmentToStore";
import { ROLE_CODE, RoleCode, type User } from "@/types/auth";
import { Box, Typography } from "@mui/material";
import type { GridSortModel } from "@mui/x-data-grid";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

// Module-level constants
const PAGE_SIZE = 20;
const LOCK_STATUS = {
  LOCKED: 1,
  NO_LOCK: 0,
};

interface UserListProps {
  searchValue: UserMasterSearchRequest;
}

const UserList: React.FC<UserListProps> = ({ searchValue }) => {
  const { t } = useTranslation("common"); // 'common' là namespace

  const route = useRouter();
  const isSorting = useRef(false);
  const isPageChanging = useRef(false);

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(PAGE_SIZE);
  const [selectedRows, setSelectedRows] = useState<(string | number)[]>([]);
  const [data, setData] = useState<UserMasterInfo[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [loadingClear, setLoadingClear] = useState(false);
  const [loadingRedirect, setLoadingRedirect] = useState(false);
  // Lấy danh sách từ redux
  const departmentList = useSelector(getTempDepartmentList);
  const roleList = useSelector(getRoleList);
  const user: User | null = useSelector(selectUserProfile);
  const organizationList = useSelector(getOrganizationList);

  const [orgValue, setOrgValue] = useState("");
  useTempDepartmentToStore(orgValue);

  const fields: FieldConfig[] = useMemo(() => {
    const ADMIN_CODES: RoleCode[] = [ROLE_CODE.C_ADMIN, ROLE_CODE.M_ADMIN];
    const isAdmin = !!user && ADMIN_CODES.includes(user.roleCode);
    const userOrg = user?.organizationCode;
    const hasUserOrg = !!organizationList?.some((o) => o.organizationCode === userOrg);

    const defaultOrgCodeLocal = !isAdmin && hasUserOrg ? userOrg : undefined;

    return [
      {
        name: "userName",
        label: "User Name",
        type: "text",
        placeholder: "Enter user name",
        hiddenLabelNormal: true,
      },
      {
        name: "userId",
        label: "User ID",
        type: "email",
        placeholder: "Enter user ID",
        hiddenLabelNormal: true,
      },
      {
        name: "organizationCode",
        label: "Company",
        type: "autocomplete",
        placeholder: "Select company",
        hiddenLabelNormal: true,
        disabled: !!defaultOrgCodeLocal,
        options: organizationList?.map((o) => ({
          value: o.organizationCode,
          label: o.organizationName,
        })),
      },
      {
        name: "departmentCode",
        label: "Department",
        type: "autocomplete",
        placeholder: "Select department",
        hiddenLabelNormal: true,
        disabled: !orgValue && !defaultOrgCodeLocal,
        options: (departmentList || []).map((d) => ({
          value: d.departmentCode,
          label: d.departmentName,
        })),
      },
      {
        name: "roleCode",
        label: "Role",
        type: "autocomplete",
        placeholder: "Select role",
        hiddenLabelNormal: true,
        options: roleList.map((r) => ({
          value: r.roleCode,
          label: r.roleName,
        })),
      },
      {
        name: "beforeLastLoginDateTime",
        label: "Last Login",
        type: "date",
        helperText: "Before",
        placeholder: "Enter last login",
        disableFuture: true,
        hiddenLabelNormal: true,
      },
      {
        name: "lockStatus",
        label: "Status",
        type: "select",
        placeholder: "Select status",
        options: [
          { label: "Active", value: 0 },
          { label: "Inactive", value: 1 },
        ],
        hiddenLabelNormal: true,
      },
    ];
  }, [organizationList, departmentList, roleList, user, orgValue]);
  // Determine default affiliation value
  // let defaultOrgCode = undefined;
  // if (user?.organizationCode && Array.isArray(organizationList)) {
  //   const foundOrg = organizationList.find((org) => org.organizationCode === user.organizationCode);
  //   const ADMIN_CODES: RoleCode[] = [ROLE_CODE.C_ADMIN, ROLE_CODE.M_ADMIN];
  //   if (foundOrg && !ADMIN_CODES.includes(user.roleCode)) {
  //     defaultOrgCode = user.organizationCode;
  //     fields.find((f) => f.name === "organizationCode")!.disabled = true;
  //   }
  // }

  const defaultOrgCode = React.useMemo(() => {
    if (user?.organizationCode && Array.isArray(organizationList)) {
      const ADMIN_CODES: RoleCode[] = [ROLE_CODE.C_ADMIN, ROLE_CODE.M_ADMIN];
      const foundOrg = organizationList.find((org) => org.organizationCode === user.organizationCode);
      if (foundOrg && !ADMIN_CODES.includes(user.roleCode)) {
        return user.organizationCode;
      }
    }
    return undefined;
  }, [user?.organizationCode, user?.roleCode, organizationList]);

  const [sortModel, setSortModel] = useState<GridSortModel>([]);
  const [searchParams, setSearchParams] = useState<UserMasterSearchRequest>({
    ...searchValue,
    condition: {
      ...searchValue.condition,
      countryCode: user?.countryCode,
      organizationCode: defaultOrgCode,
    },
  });
  const handleSearch = useCallback(
    (data: any) => {
      setLoadingClear(false);
      setLoadingSearch(true);
      const { beforeLastLoginDateTime, ...dataSearch } = data;
      setPage(1);
      setSearchParams((prev) => ({
        ...prev,
        condition: {
          ...prev.condition,
          ...dataSearch,
          countryCode: user?.countryCode,
          beforeLastLoginDateTime: beforeLastLoginDateTime ? dayjs(beforeLastLoginDateTime).format("DD/MM/YYYY") : "",
        },
      }));
    },
    [user?.countryCode]
  );

  const handleClear = useCallback(() => {
    setLoadingSearch(false);
    setLoadingClear(true);
    setPage(1);
    setOrgValue("");
    setSearchParams((prev) => ({
      ...prev,
      condition: {
        // ...prev.condition,
        countryCode: user?.countryCode,
        organizationCode: defaultOrgCode ?? undefined,
      },
    }));
  }, [defaultOrgCode, user]);

  useEffect(() => {
    setSearchParams((prev) => ({
      ...prev,
      condition: {
        ...prev.condition,
        organizationCode: defaultOrgCode,
      },
    }));
  }, [defaultOrgCode]);

  const handlePageChange = useCallback((newPage: number) => {
    if (isPageChanging.current) return;
    setPage((prev) => {
      if (prev !== newPage) {
        isPageChanging.current = true;
        return newPage;
      }
      return prev;
    });
  }, []);

  const handlePageSizeChange = useCallback((newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(1);
  }, []);

  const handleSelectRow = useCallback((rowIds: (string | number)[]) => {
    setSelectedRows(rowIds);
  }, []);

  const handleRowClick = useCallback(
    (row: any) => {
      const clientId = row.clientId;
      setLoadingRedirect(true);
      route.push(`/user-master/detail?id=${clientId}`);
    },
    [route]
  );

  const resetData = useCallback(() => {
    setData([]);
    setTotal(0);
    setPage(1);
    setPageSize(PAGE_SIZE);
  }, []);

  // Utility: convert camelCase to snake_case
  const toSnakeCase = useCallback((str: string) => str.replace(/([A-Z])/g, "_$1").toLowerCase(), []);

  // Memoized columns to avoid re-creating render functions
  const columns: DynamicDataGridColumn[] = useMemo(
    () => [
      {
        key: "lastName",
        label: "User Name",
        headerAlign: "center",
        align: "left",
        render: (row) => `${row.lastName || ""} ${row.firstName || ""}`.trim(),
        flex: 1,
      },
      { key: "userId", label: "User ID", headerAlign: "center", align: "left", flex: 1 },
      { key: "organizationName", label: "Company", headerAlign: "center", align: "left", flex: 0.7 },
      { key: "departmentName", label: "Department", headerAlign: "center", align: "left", flex: 0.6 },
      { key: "roleName", label: "Role", headerAlign: "center", align: "center", flex: 0.45 },
      { key: "countryName", label: "Country", headerAlign: "center", align: "center", flex: 0.4 },
      { key: "lastLoginDateTime", label: "Last Login", headerAlign: "center", align: "center", flex: 0.5 },
      {
        key: "lockStatus",
        label: "Status",
        headerAlign: "center",
        align: "center",
        flex: 0.4,
        render: (row) => (
          <Typography
            sx={{
              width: "72px",
              height: "24px",
              display: "inline-block",
              borderRadius: "4px",
              fontSize: "12px",
              color: row.lockStatus === LOCK_STATUS.LOCKED ? "#fff" : "#4CAF50",
              backgroundColor: row.lockStatus === LOCK_STATUS.LOCKED ? "#BFC4CC" : "#3fc28a33",
            }}
          >
            {row.lockStatus === LOCK_STATUS.LOCKED ? "Inactive" : "Active"}
          </Typography>
        ),
      },
    ],
    []
  );

  // Helper to build request payload with the requested shape
  const buildPayload = useCallback(
    (pageNumber: number, pageSizeNumber: number, params: UserMasterSearchRequest, sortModelLocal: GridSortModel) => {
      if (!params) {
        return {
          condition: {
            organizationCode: defaultOrgCode ?? undefined,
          },
          sort: [],
          pagination: { pageSize: pageSizeNumber, pageNumber },
        } as UserMasterSearchRequest;
      }

      // Extract known structural props
      const {
        condition: rawCondition = {} as Record<string, any>,
        sortColumn: sortColumnFromParams,
        sortDirection: sortDirectionFromParams,
        ...maybeFlattenedFilters
      } = params as any;

      // Recover any filter fields that were (incorrectly) stored at root level earlier
      const recovered: Record<string, any> = {};
      Object.entries(maybeFlattenedFilters).forEach(([k, v]) => {
        if (v !== undefined && v !== "" && !["pagination", "sort", "pageSize", "pageNumber"].includes(k)) {
          recovered[k] = v;
        }
      });

      const mergedCondition = {
        ...rawCondition,
        ...recovered,
      };

      // Sorting
      const sort: Array<{ sortColumn: string; sortDirection: string }> = [];
      if (sortModelLocal && sortModelLocal.length > 0) {
        const { field, sort: direction } = sortModelLocal[0];
        if (field) {
          sort.push({
            sortColumn: toSnakeCase(field),
            sortDirection: (direction || "asc").toLowerCase(),
          });
        }
      } else if (sortColumnFromParams) {
        sort.push({
          sortColumn: sortColumnFromParams,
          sortDirection: (sortDirectionFromParams || "asc").toLowerCase(),
        });
      }

      // Clean + normalize
      const cleanedCondition = Object.fromEntries(Object.entries(mergedCondition).filter(([, v]) => v !== undefined && v !== ""));
      // Force lockStatus to number if present
      if ("lockStatus" in cleanedCondition) {
        cleanedCondition.lockStatus = Number(cleanedCondition.lockStatus);
      }
      return {
        condition: {
          ...cleanedCondition,
        },
        sort,
        pagination: {
          pageSize: pageSizeNumber,
          pageNumber,
        },
      };
    },
    [toSnakeCase, defaultOrgCode]
  );

  const fetchData = useCallback(
    async (pageParam = page, pageSizeParam = pageSize, params = searchParams) => {
      try {
        setLoading(true);
        const payload = buildPayload(pageParam, pageSizeParam, params, sortModel);
        const res = await searchUser(payload as any);
        setData(res.data.items);
        setTotal(res.data.totalItem);
      } catch (error) {
        setLoadingSearch(false);
        setLoadingClear(false);
        setLoading(false);
        resetData();
      } finally {
        setLoadingSearch(false);
        setLoadingClear(false);
        setLoading(false);
        isPageChanging.current = false;
        isSorting.current = false;
      }
    },
    [page, pageSize, searchParams, resetData, buildPayload, sortModel]
  );

  useEffect(() => {
    fetchData(page, pageSize, searchParams);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, pageSize, searchParams, buildPayload, fetchData]);

  // Handle sort change from DataGrid
  const handleSortModelChange = useCallback((model: GridSortModel) => {
    if (isSorting.current) {
      return;
    }
    isSorting.current = true;
    setSortModel(model);
    setPage(1);
    // We keep searchParams unchanged here because payload uses sortModel directly when fetching
  }, []);
  const searchFormKey = `${user?.clientId || "nouser"}-${defaultOrgCode || "none"}`;
  return (
    <>
      <DynamicSearch
        key={searchFormKey}
        fields={fields}
        onSubmit={handleSearch}
        onClear={handleClear}
        searchLabel={t("search")}
        clearLabel={"Clear Filter"}
        advancedLabel={"Hide Advanced Filters"}
        loadingSearch={loadingSearch}
        loadingClear={loadingClear}
        onValuesChange={({ name, values, setValue, prevValues }) => {
          if (name === "organizationCode") {
            const changed = prevValues.organizationCode !== values.organizationCode;
            const hasOrg = !!values.organizationCode;
            setOrgValue(values.organizationCode || "");
            if (!hasOrg || changed) {
              setValue("departmentCode", "");
            }
          }
        }}
        defaultValues={{
          ...(defaultOrgCode ? { organizationCode: defaultOrgCode } : {}),
        }}
      />
      <Box sx={{ height: 24 }} />
      <DynamicDataGrid
        columns={columns}
        data={data}
        page={page}
        pageSize={pageSize}
        total={total}
        loading={loading}
        loadingRedirect={loadingRedirect}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        showCheckbox={false}
        selectedRows={selectedRows}
        onSelectRow={handleSelectRow}
        renderActions={useCallback(
          (row: UserMasterInfo) => {
            const isSelf = user?.clientId === row.clientId;
            return <ActionMenu isSelf={isSelf} row={row} onReload={() => fetchData()} />;
          },
          [fetchData, user?.clientId]
        )}
        rowKey="id"
        showPointer={true}
        onRowClick={handleRowClick}
        sortModel={sortModel}
        onSortModelChange={handleSortModelChange}
        resultPerPageOptions={[10, 20, 50, 100]}
        itemLabel={"users"}
      />
    </>
  );
};

export default UserList;
