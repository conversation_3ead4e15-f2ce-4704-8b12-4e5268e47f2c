import type { FieldConfig } from "@/components/common/DynamicForm/DynamicForm.types";
import DynamicSearch from "@/components/common/DynamicSearch/DynamicSearch";
import type { DynamicDataGridColumn } from "@/components/common/DynamicTable/DynamicDataGrid";
import DynamicDataGrid from "@/components/common/DynamicTable/DynamicDataGrid";
import ActionMenu from "@/components/feature/UserMaster/UserList/ActionMenu";
import { getDepartmentList } from "@/features/department/state/departmentSelector";
import { getOrganizationList } from "@/features/organization/state/organizationSelector";
import { getRoleList } from "@/features/role/state/roleSelector";
import { searchUser } from "@/features/users/api/userMasterApis";
import { selectUserProfile } from "@/features/users/state/usersSelectors";
import { UserMasterInfo, UserMasterSearchRequest } from "@/features/users/types/userMasterModels";
import { ROLE_CODE, RoleCode, type User } from "@/types/auth";
import { Box, Typography } from "@mui/material";
import type { GridSortModel } from "@mui/x-data-grid";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

// Module-level constants
const PAGE_SIZE = 50;
const LOCK_STATUS = {
  LOCKED: 1,
  NO_LOCK: 0,
};

interface UserListProps {
  searchValue: UserMasterSearchRequest;
}

const UserList: React.FC<UserListProps> = ({ searchValue }) => {
  const { t } = useTranslation("common"); // 'common' là namespace

  const route = useRouter();

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(PAGE_SIZE);
  const [selectedRows, setSelectedRows] = useState<(string | number)[]>([]);
  const [data, setData] = useState<UserMasterInfo[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  // Lấy danh sách từ redux
  const departmentList = useSelector(getDepartmentList);
  const roleList = useSelector(getRoleList);
  const user: User | null = useSelector(selectUserProfile);
  const organizationList = useSelector(getOrganizationList);

  const fields: FieldConfig[] = useMemo(() => {
    const ADMIN_CODES: RoleCode[] = [ROLE_CODE.C_ADMIN, ROLE_CODE.M_ADMIN];
    const isAdmin = !!user && ADMIN_CODES.includes(user.roleCode);
    const userOrg = user?.organizationCode;
    const hasUserOrg = !!organizationList?.some((o) => o.organizationCode === userOrg);

    const defaultOrgCodeLocal = !isAdmin && hasUserOrg ? userOrg : undefined;

    return [
      {
        name: "userName",
        label: "User Name",
        type: "text",
        placeholder: "Enter user name",
        hiddenLabelNormal: true,
      },
      {
        name: "userId",
        label: "User ID",
        type: "email",
        placeholder: "Enter user ID",
        hiddenLabelNormal: true,
      },
      {
        name: "organizationCode",
        label: "Company",
        type: "autocomplete",
        placeholder: "Select company",
        hiddenLabelNormal: true,
        disabled: !!defaultOrgCodeLocal,
        options: organizationList?.map((o) => ({
          value: o.organizationCode,
          label: o.organizationName,
        })),
      },
      {
        name: "departmentCode",
        label: "Department",
        type: "autocomplete",
        placeholder: "Select department",
        hiddenLabelNormal: true,
        options: departmentList.map((d) => ({
          value: d.departmentCode,
          label: d.departmentName,
        })),
      },
      {
        name: "roleCode",
        label: "Role",
        type: "autocomplete",
        placeholder: "Select role",
        hiddenLabelNormal: true,
        options: roleList.map((r) => ({
          value: r.roleCode,
          label: r.roleName,
        })),
      },
      {
        name: "beforeLastLoginDateTime",
        label: "Last Login",
        type: "date",
        helperText: "Before",
        placeholder: "Enter last login",
        disableFuture: true,
        hiddenLabelNormal: true,
      },
      {
        name: "lockStatus",
        label: "Status",
        type: "select",
        placeholder: "Select status",
        options: [
          { label: "Active", value: 0 },
          { label: "Inactive", value: 1 },
        ],
        hiddenLabelNormal: true,
      },
    ];
  }, [organizationList, departmentList, roleList, user]);

  // Determine default affiliation value
  // let defaultOrgCode = undefined;
  // if (user?.organizationCode && Array.isArray(organizationList)) {
  //   const foundOrg = organizationList.find((org) => org.organizationCode === user.organizationCode);
  //   const ADMIN_CODES: RoleCode[] = [ROLE_CODE.C_ADMIN, ROLE_CODE.M_ADMIN];
  //   if (foundOrg && !ADMIN_CODES.includes(user.roleCode)) {
  //     defaultOrgCode = user.organizationCode;
  //     fields.find((f) => f.name === "organizationCode")!.disabled = true;
  //   }
  // }

  const defaultOrgCode = React.useMemo(() => {
    if (user?.organizationCode && Array.isArray(organizationList)) {
      const ADMIN_CODES: RoleCode[] = [ROLE_CODE.C_ADMIN, ROLE_CODE.M_ADMIN];
      const foundOrg = organizationList.find((org) => org.organizationCode === user.organizationCode);
      if (foundOrg && !ADMIN_CODES.includes(user.roleCode)) {
        return user.organizationCode;
      }
    }
    return undefined;
  }, [user?.organizationCode, user?.roleCode, organizationList]);

  const [sortModel, setSortModel] = useState<GridSortModel>([]);
  const [searchParams, setSearchParams] = useState<UserMasterSearchRequest>({
    ...searchValue,
    condition: {
      ...searchValue.condition,
      countryCode: user?.countryCode,
      organizationCode: defaultOrgCode,
    },
  });
  const handleSearch = useCallback(
    (data: any) => {
      const { beforeLastLoginDateTime, ...dataSearch } = data;
      setPage(1);
      setSearchParams((prev) => ({
        ...prev,
        condition: {
          ...prev.condition,
          ...dataSearch,
          countryCode: user?.countryCode,
          beforeLastLoginDateTime: beforeLastLoginDateTime ? dayjs(beforeLastLoginDateTime).format("DD/MM/YYYY") : "",
        },
      }));
    },
    [user?.countryCode]
  );

  const handleClear = useCallback(() => {
    setPage(1);
    setSearchParams((prev) => ({
      ...prev,
      condition: {
        // ...prev.condition,
        countryCode: user?.countryCode,
        organizationCode: defaultOrgCode ?? undefined,
      },
    }));
  }, [defaultOrgCode, user]);

  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handlePageSizeChange = useCallback((newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(1);
  }, []);

  const handleSelectRow = useCallback((rowIds: (string | number)[]) => {
    setSelectedRows(rowIds);
  }, []);

  const handleRowClick = useCallback(
    (row: any) => {
      const clientId = row.clientId;
      route.push(`/user-master/detail?id=${clientId}`);
    },
    [route]
  );

  const resetData = useCallback(() => {
    setData([]);
    setTotal(0);
    setPage(1);
    setPageSize(PAGE_SIZE);
  }, []);

  // Utility: convert camelCase to snake_case
  const toSnakeCase = useCallback((str: string) => str.replace(/([A-Z])/g, "_$1").toLowerCase(), []);

  // Memoized columns to avoid re-creating render functions
  const columns: DynamicDataGridColumn[] = useMemo(
    () => [
      {
        key: "lastName",
        label: "User Name",
        headerAlign: "center",
        align: "left",
        render: (row) => `${row.firstName || ""} ${row.lastName || ""}`.trim(),
      },
      { key: "userId", label: "User ID", headerAlign: "center", align: "left" },
      { key: "organizationName", label: "Company", headerAlign: "center", align: "left" },
      { key: "departmentName", label: "Department", headerAlign: "center", align: "left" },
      { key: "roleName", label: "Role", headerAlign: "center", align: "center" },
      { key: "countryName", label: "Country", headerAlign: "center", align: "center" },
      { key: "lastLoginDateTime", label: "Last Login", headerAlign: "center", align: "center" },
      {
        key: "lockStatus",
        label: "Status",
        headerAlign: "center",
        align: "center",
        render: (row) => (
          <Typography
            sx={{
              width: "72px",
              height: "24px",
              display: "inline-block",
              borderRadius: "4px",
              fontSize: "12px",
              color: row.lockStatus === LOCK_STATUS.LOCKED ? "#fff" : "#4CAF50",
              backgroundColor: row.lockStatus === LOCK_STATUS.LOCKED ? "#BFC4CC" : "#3fc28a33",
            }}
          >
            {row.lockStatus === LOCK_STATUS.LOCKED ? "Inactive" : "Active"}
          </Typography>
        ),
      },
    ],
    []
  );

  // Helper to build request payload with the requested shape
  const buildPayload = useCallback(
    (pageNumber: number, pageSizeNumber: number, params: UserMasterSearchRequest, sortModelLocal: GridSortModel) => {
      if (!params) {
        return {
          condition: {
            organizationCode: defaultOrgCode ?? undefined,
          },
          sort: [],
          pagination: { pageSize: pageSizeNumber, pageNumber },
        } as UserMasterSearchRequest;
      }

      // Extract known structural props
      const {
        condition: rawCondition = {} as Record<string, any>,
        sortColumn: sortColumnFromParams,
        sortDirection: sortDirectionFromParams,
        ...maybeFlattenedFilters
      } = params as any;

      // Recover any filter fields that were (incorrectly) stored at root level earlier
      const recovered: Record<string, any> = {};
      Object.entries(maybeFlattenedFilters).forEach(([k, v]) => {
        if (v !== undefined && v !== "" && !["pagination", "sort", "pageSize", "pageNumber"].includes(k)) {
          recovered[k] = v;
        }
      });

      const mergedCondition = {
        ...rawCondition,
        ...recovered,
      };

      // Sorting
      const sort: Array<{ sortColumn: string; sortDirection: string }> = [];
      if (sortModelLocal && sortModelLocal.length > 0) {
        const { field, sort: direction } = sortModelLocal[0];
        if (field) {
          sort.push({
            sortColumn: toSnakeCase(field),
            sortDirection: (direction || "asc").toLowerCase(),
          });
        }
      } else if (sortColumnFromParams) {
        sort.push({
          sortColumn: sortColumnFromParams,
          sortDirection: (sortDirectionFromParams || "asc").toLowerCase(),
        });
      }

      // Clean + normalize
      const cleanedCondition = Object.fromEntries(
        Object.entries(mergedCondition).filter(([, v]) => v !== undefined && v !== "")
      );
      // Force lockStatus to number if present
      if ("lockStatus" in cleanedCondition) {
        cleanedCondition.lockStatus = Number(cleanedCondition.lockStatus);
      }
      return {
        condition: {
          ...cleanedCondition,
        },
        sort,
        pagination: {
          pageSize: pageSizeNumber,
          pageNumber,
        },
      };
    },
    [toSnakeCase]
  );

  const fetchData = useCallback(
    async (pageParam = page, pageSizeParam = pageSize, params = searchParams) => {
      try {
        setLoading(true);
        const payload = buildPayload(pageParam, pageSizeParam, params, sortModel);
        const res = await searchUser(payload as any);
        setData(res.data.items);
        setTotal(res.data.totalItem);
      } catch (error) {
        setLoading(false);
        resetData();
      } finally {
        setLoading(false);
      }
    },
    [page, pageSize, searchParams, resetData, buildPayload, sortModel]
  );

  useEffect(() => {
    fetchData(page, pageSize, searchParams);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, pageSize, searchParams, fetchData]);

  // Handle sort change from DataGrid
  const handleSortModelChange = useCallback((model: GridSortModel) => {
    setSortModel(model);
    setPage(1);
    // We keep searchParams unchanged here because payload uses sortModel directly when fetching
  }, []);
  const searchFormKey = `${user?.clientId || "nouser"}-${defaultOrgCode || "none"}`;
  return (
    <>
      <DynamicSearch
        key={searchFormKey}
        fields={fields}
        onSubmit={handleSearch}
        onClear={handleClear}
        searchLabel={t("search")}
        clearLabel={"Clear Filter"}
        advancedLabel={"Hide Advanced Filters"}
        defaultValues={{
          ...(defaultOrgCode ? { organizationCode: defaultOrgCode } : {}),
        }}
      />
      <Box sx={{ height: 24 }} />
      <DynamicDataGrid
        columns={columns}
        data={data}
        page={page}
        pageSize={pageSize}
        total={total}
        loading={loading}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        showCheckbox={false}
        selectedRows={selectedRows}
        onSelectRow={handleSelectRow}
        renderActions={useCallback(
          (row: UserMasterInfo) => {
            const isSelf = user?.clientId === row.clientId;
            return <ActionMenu isSelf={isSelf} row={row} onReload={() => fetchData()} />;
          },
          [fetchData, user?.clientId]
        )}
        rowKey="id"
        onRowClick={handleRowClick}
        sortModel={sortModel}
        onSortModelChange={handleSortModelChange}
        resultPerPageOptions={[10, 20, 50]}
      />
    </>
  );
};

export default UserList;
