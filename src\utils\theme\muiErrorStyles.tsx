import { SxProps, Theme } from "@mui/material/styles";

const MuiErrorCustomStyles: SxProps<Theme> = {
  "& .MuiOutlinedInput-root.Mui-error .MuiOutlinedInput-notchedOutline": {
    borderColor: "#CA002E",
  },
  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
    borderColor: "rgba(34, 34, 34, 1)",
  },
  "& .MuiOutlinedInput-root.Mui-error.Mui-focused .MuiOutlinedInput-notchedOutline": {
    borderColor: "#CA002E",
  },
  "& .MuiFormLabel-root.Mui-error": {
    color: "#CA002E",
  },
  "& .MuiFormLabel-root.Mui-focused": {
    color: "rgba(34, 34, 34, 1)",
  },
  "& .MuiFormLabel-root.Mui-error.Mui-focused": {
    color: "#CA002E",
  },
  "& .MuiInputBase-input.Mui-error": {
    color: "#CA002E",
  },
  "& .MuiFormHelperText-root.Mui-error": {
    color: "#CA002E",
  },
};

export default MuiErrorCustomStyles;