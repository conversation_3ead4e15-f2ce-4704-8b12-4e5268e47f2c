import MuiErrorCustomStyles from "@/utils/theme/muiErrorStyles";
import { Box, IconButton } from "@mui/material";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import { ClearIcon } from "@mui/x-date-pickers";
import React, { useState } from "react";
import { Controller } from "react-hook-form";
import { FieldConfig } from "../DynamicForm.types";

interface Props {
  field: FieldConfig;
  control: any;
  error?: string;
  disabled?: boolean;
  i18n?: (key: string) => string;
}

const SelectField: React.FC<Props> = ({
  field,
  control,
  error,
  disabled,
  i18n,
}) => {
  const [showClear, setShowClear] = useState(false);
  return (
    <Controller
      name={field.name}
      control={control}
      render={({ field: controllerField }) =>
        field.hiddenLabelNormal ? (
          <Box
            sx={{ position: "relative", width: "100%" }}
            onMouseEnter={() => setShowClear(true)}
            onMouseLeave={() => setShowClear(false)}
            onFocus={() => setShowClear(true)}
            onBlur={() => setShowClear(false)}
          >
            <Box
              sx={{
                position: "absolute",
                left: 0,
                right: 0,
                top: 2,
                color: "#222222",
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: 14,
                lineHeight: 1.5,
                zIndex: 2,
                pointerEvents: "none",
              }}
            >
              {i18n ? i18n(field.label) : field.label}
            </Box>
            <Select
              {...controllerField}
              label=""
              error={!!error}
              fullWidth
              disabled={disabled || field.disabled}
              sx={{
                ...field.style,
                ...MuiErrorCustomStyles,
                mt: 3,
                "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderColor: "rgba(0, 0, 0, 1)",
                },
              }}
              className={`hiddenLabelNormal ${field.className}`}
              value={controllerField.value || ""}
              displayEmpty
              renderValue={(selected) => {
                if (
                  selected === "" ||
                  selected === null ||
                  selected === undefined
                ) {
                  if (!field.placeholder) return "";
                  return (
                    <span style={{ color: "#A6AAB2" }}>
                      {i18n ? i18n(field.placeholder) : field.placeholder}
                    </span>
                  );
                }
                const found = field.options?.find(
                  (opt) => String(opt.value) === String(selected)
                );
                return found
                  ? i18n
                    ? i18n(found.label)
                    : found.label
                  : String(selected);
              }}
            >
              {field.options?.map((option) => (
                <MenuItem
                  key={String(option.value)}
                  value={String(option.value)}
                >
                  {i18n ? i18n(option.label) : option.label}
                </MenuItem>
              ))}
            </Select>
            {controllerField.value && showClear && (
              <Box
                sx={{
                  position: "absolute",
                  right: "30px",
                  top: "70%",
                  transform: "translateY(-50%)",
                  zIndex: 3,
                }}
              >
                <IconButton
                  className="clear-btn"
                  size="small"
                  tabIndex={-1}
                  sx={{
                    p: 0.5,
                    opacity: 1,
                  }}
                  onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    controllerField.onChange("");
                  }}
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              </Box>
            )}
          </Box>
        ) : (
          <Select
            {...controllerField}
            // select
            label={i18n ? i18n(field.label) : field.label}
            error={!!error}
            // helperText={error || field.helperText}
            fullWidth
            disabled={disabled || field.disabled}
            sx={{ ...field.style, ...MuiErrorCustomStyles }}
            className={`controlNormal ${field.className ?? ""}`}
            inputProps={{ "aria-label": field.label }}
            value={controllerField.value || ""}
            displayEmpty
            renderValue={(selected) => {
              if (
                selected === "" ||
                selected === null ||
                selected === undefined
              ) {
                if (!field.placeholder) return "";
                return (
                  <span style={{ color: "#A6AAB2" }}>
                    {i18n ? i18n(field.placeholder) : field.placeholder}
                  </span>
                );
              }
              const found = field.options?.find(
                (opt) => String(opt.value) === String(selected)
              );
              return found
                ? i18n
                  ? i18n(found.label)
                  : found.label
                : String(selected);
            }}
          >
            {/* {field.placeholder && (
            <MenuItem value="" disabled sx={{ color: '#A6AAB2' }}>
              {i18n ? i18n(field.placeholder) : field.placeholder}
            </MenuItem>
          )} */}
            {field.options?.map((option) => (
              <MenuItem key={String(option.value)} value={String(option.value)}>
                {i18n ? i18n(option.label) : option.label}
              </MenuItem>
            ))}
          </Select>
        )
      }
    />
  );
};

export default SelectField;
