import React from 'react';
import { Box, IconButton, InputAdornment, OutlinedInput } from '@mui/material';
import { Controller } from 'react-hook-form';
import MuiTextField from '@mui/material/TextField';
import MenuItem from '@mui/material/MenuItem';
import { FieldConfig } from '../DynamicForm.types';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import MuiErrorCustomStyles from '@/utils/theme/muiErrorStyles';
import { ClearIcon } from '@mui/x-date-pickers';

interface Props {
  field: FieldConfig;
  control: any;
  error?: string;
  disabled?: boolean;
  i18n?: (key: string) => string;
}

const SelectField: React.FC<Props> = ({ field, control, error, disabled, i18n }) => (
  <Controller
    name={field.name}
    control={control}
    render={({ field: controllerField }) => (
      field.hiddenLabelNormal ? (
        <Box sx={{ position: 'relative', width: '100%' }}>
          <Box
            sx={{
              position: 'absolute',
              left: 0,
              right: 0,
              top: 2,
              color: '#222222',
              fontFamily: 'Roboto',
              fontWeight: 400,
              fontSize: 14,
              lineHeight: 1.5,
              zIndex: 2,
            }}
          >
            {i18n ? i18n(field.label) : field.label}
          </Box>
          <Select
            {...controllerField}
            label=""
            error={!!error}
            // helperText={error || field.helperText}
            fullWidth
            disabled={disabled || field.disabled}
            sx={{
              ...field.style, ...MuiErrorCustomStyles, mt: 3,
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: 'rgba(0, 0, 0, 1)',
              },
            }}
            className={field.className}
            value={controllerField.value || ''}
            displayEmpty
            renderValue={(selected) => {
              if (selected === '' || selected === null || selected === undefined) {
                if (!field.placeholder) return '';
                return <span style={{ color: '#A6AAB2' }}>{i18n ? i18n(field.placeholder) : field.placeholder}</span>;
              }
              const found = field.options?.find(opt => String(opt.value) === String(selected));
              return found ? (i18n ? i18n(found.label) : found.label) : String(selected);
            }}
            endAdornment={
              controllerField.value && (<InputAdornment sx={{ marginRight: "10px" }} position="end">
                <IconButton
                sx={{marginRight: '8px'}}
                  onClick={() => controllerField.onChange('')}
                >
                  <ClearIcon fontSize="small"></ClearIcon>
                </IconButton>
              </InputAdornment>)
            }
            MenuProps={{
              PaperProps: {
                sx: {
                  '& .MuiMenuItem-root.Mui-selected': {
                    backgroundColor: '#f6f9fc !important',
                  },
                  '& .MuiMenuItem-root.Mui-selected:hover': {
                    backgroundColor: '#f6f9fc !important',
                  },
                }
              }
            }}
          >
            {/* {field.placeholder && (
              <MenuItem value="" disabled sx={{ color: '#A6AAB2' }}>
                {i18n ? i18n(field.placeholder) : field.placeholder}
              </MenuItem>
            )} */}
            {field.options?.map((option) => (
              <MenuItem key={String(option.value)} value={String(option.value)} >
                {i18n ? i18n(option.label) : option.label}
              </MenuItem>
            ))}
          </Select>
        </Box>
      ) : (
        <Select
          {...controllerField}
          // select
          label={i18n ? i18n(field.label) : field.label}
          error={!!error}
          // helperText={error || field.helperText}
          fullWidth
          disabled={disabled || field.disabled}
          sx={{ ...field.style, ...MuiErrorCustomStyles }}
          className={field.className}
          inputProps={{ 'aria-label': field.label }}
          value={controllerField.value || ''}
          displayEmpty
          renderValue={(selected) => {
            if (selected === '' || selected === null || selected === undefined) {
              if (!field.placeholder) return '';
              return <span style={{ color: '#A6AAB2' }}>{i18n ? i18n(field.placeholder) : field.placeholder}</span>;
            }
            const found = field.options?.find(opt => String(opt.value) === String(selected));
            return found ? (i18n ? i18n(found.label) : found.label) : String(selected);
          }}
          MenuProps={{
            PaperProps: {
              sx: {
                '& .MuiMenuItem-root.Mui-selected': {
                  backgroundColor: '#f6f9fc !important',
                },
                '& .MuiMenuItem-root.Mui-selected:hover': {
                  backgroundColor: '#f6f9fc !important',
                },
              }
            }
          }}
        >
          {/* {field.placeholder && (
            <MenuItem value="" disabled sx={{ color: '#A6AAB2' }}>
              {i18n ? i18n(field.placeholder) : field.placeholder}
            </MenuItem>
          )} */}
          {field.options?.map((option) => (
            <MenuItem key={String(option.value)} value={String(option.value)}>
              {i18n ? i18n(option.label) : option.label}
            </MenuItem>
          ))}
        </Select>
      )
    )}
  />
);

export default SelectField;
