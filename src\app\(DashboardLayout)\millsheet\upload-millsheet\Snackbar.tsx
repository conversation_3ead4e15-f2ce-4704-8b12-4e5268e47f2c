import React from "react";
import {
  Typography,
  Box,
  Icon,
  Snackbar,
  keyframes,
  IconButton,
} from "@mui/material";
import Image from "next/image";
import CloseIcon from "@mui/icons-material/Close";

const CheckCircleIcon = () => (
  <Icon
    sx={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      borderRadius: "4px",
      width: "28px",
      height: "28px",
    }}
  >
    <Image
      src="/images/millsheet/subtract.svg"
      alt="icon document"
      width={24}
      height={24}
    />
  </Icon>
);

const sxStyles = {
  fontFamily: "Roboto, sans-serif",
  fontWeight: 400,
  fontStyle: "normal", // 'Regular' is not a valid value
  fontSize: "14px",
  lineHeight: "150%",
  letterSpacing: 0,
  verticalAlign: "middle",
};

const sxStylesSmall = {
  fontFamily: "Roboto, sans-serif",
  fontWeight: 400,
  fontStyle: "normal", // 'Regular' is not a valid value
  fontSize: "14px",
  lineHeight: "120%",
  letterSpacing: 0,
  verticalAlign: "middle",
};

const animationDuration = 100;

const inAnimation = keyframes`
  0% {
    transform: scale(0);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
`;

const outAnimation = keyframes`
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0);
    opacity: 0;
  }
`;

interface SnackBarDetailProps {
  fileLengthSuccess: number;
  uploadSuccess: boolean;
  closeUploadSuccess: (data?: any) => void;
}

const SnackbarUpload: React.FC<SnackBarDetailProps> = ({
  fileLengthSuccess,
  uploadSuccess,
  closeUploadSuccess,
}) => {
  return (
    <>
      <Snackbar
        open={uploadSuccess}
        onClose={closeUploadSuccess}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
        autoHideDuration={5000}
        transitionDuration={animationDuration}
        sx={{
          animation: `${inAnimation} ${outAnimation}ms forwards`,
          height: "126px",
          top: "5px !important",
          borderRadius: "12px",
          borderLeft: "3px solid #4CAF50",
          boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.25)",
          width: "336px",
          backgroundColor: "#FFFFFF",
        }}
      >
        <Box
          sx={{
            p: 2,
            display: "flex",
            alignItems: "flex-start",
            justifyContent: "center",
            gap: "16px",
            position: "relative",
          }}
        >
          <CheckCircleIcon />
          <Box>
            <Typography
              sx={{
                fontFamily: "Roboto, sans-serif",
                fontWeight: 700,
                fontStyle: "normal",
                fontSize: "14px",
                lineHeight: "150%",
                letterSpacing: 0,
                verticalAlign: "middle",
              }}
              variant="subtitle1"
            >
              Upload Successful
            </Typography>
            <Typography sx={sxStyles} variant="body2">
              {fileLengthSuccess} files have been successfully uploaded.
            </Typography>
            <Typography sx={sxStyles} variant="body2">
              We&#39;ll notify you by email once the data has been fully processed.
            </Typography>
            <Typography
              sx={sxStylesSmall}
              variant="caption"
              color="text.secondary"
            >
              Just now
            </Typography>
          </Box>

          {/* Close Icon */}

          <IconButton
            size="small"
            onClick={closeUploadSuccess}
            sx={{
              position: "absolute",
              top: "5px",
              right: 10,
              color: "#000000",
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </Snackbar>
    </>
  );
};

export default SnackbarUpload;
