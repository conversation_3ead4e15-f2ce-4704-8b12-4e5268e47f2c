import React from "react";
import {
  Grid,
  Typography,
  List,
  ListItemText,
  styled,
  ListItem,
  Box,
} from "@mui/material";
import Image from "next/image";

const MAX_FILES = 10;
const MAX_SIZE_MB = 30;

const MILL_SHEET = "mill-sheet";

interface UploadDetailProps {
  type: string;
}

const StyledListItem = styled(ListItem)(({}) => ({
  backgroundColor: "#F5F6FA",
  width: "100%", // allow it to grow/shrink with parent
  maxWidth: "290px", // optional: limit max width
  // height: "80px",
  padding: "10px",
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
  borderRadius: "4px",
}));

const FilesAndFolderIcon = () => (
  <Image
    src="/images/millsheet/file-folder.svg"
    alt="icon filefolder"
    width={20}
    height={20}
  />
);

const CustomBox = styled(Box)(({}) => ({
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  flexDirection: "row",
  height: "33",
  gap: "10px",
}));

const LinkIcon = () => (
  <Image
    src="/images/millsheet/link.svg"
    alt="icon link"
    width={20}
    height={20}
  />
);

const FolderIcon = () => (
  <Image
    src="/images/millsheet/folder.svg"
    alt="icon folder"
    width={20}
    height={20}
  />
);

const FileIcon = () => (
  <Image
    src="/images/millsheet/file.svg"
    alt="icon file"
    width={20}
    height={20}
  />
);

const sxStyles = {
  "& .MuiTypography-root": {
    fontFamily: "Roboto",
    fontWeight: 400,
    fontStyle: "normal",
    fontSize: "18px",
    lineHeight: "130%",
    letterSpacing: 0,
    verticalAlign: "middle",
  },
};

const sxContentStyles = {
  "& .MuiTypography-root": {
    fontWeight: "400",
    fontStyle: "normal",
    fontSize: "14px",
    lineHeight: "150%",
    letterSpacing: 0,
    verticalAlign: "middle",
  },
  marginTop: "0px",
  marginBottom: "0px",
};

const REQUIREMENTS_MAP: Record<
  string,
  Array<{
    icon: React.ReactNode;
    title: string;
    description: string;
  }>
> = {
  [MILL_SHEET]: [
    {
      icon: <LinkIcon />,
      title: "PDF + Metadata Pairing",
      description: "Must be uploaded together with the same filename.",
    },
    {
      icon: <FileIcon />,
      title: "File Count",
      description: `Maximum ${MAX_FILES} files allowed.`,
    },
    {
      icon: <FolderIcon />,
      title: "File Size",
      description: `Maximum ${MAX_SIZE_MB}MB per file.`,
    },
    {
      icon: <FilesAndFolderIcon />,
      title: "Supported Formats",
      description: "PDF files only, with CSV/XMP metadata.",
    },
  ],
  default: [
    {
      icon: <FileIcon />,
      title: "File Count",
      description: `Maximum ${MAX_FILES} files allowed.`,
    },
    {
      icon: <FolderIcon />,
      title: "File Size",
      description: `Maximum ${MAX_SIZE_MB}MB per file.`,
    },
    {
      icon: <FilesAndFolderIcon />,
      title: "Supported Formats",
      description: "XLSX files only",
    },
  ],
};

const WithUploadRequirements: React.FC<UploadDetailProps> = ({ type }) => {
  const requirements = REQUIREMENTS_MAP[type] || REQUIREMENTS_MAP.default;
  return (
    <Grid
      size={{ xs: 12, md: 3.6 }}
      sx={{
        boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.08)",
        padding: 2,
        height: "100%",
        width: "310px !important",
        minWidth: "250px",
        margin: "0 auto",
        borderRadius: "12px",
        backgroundColor: "#fff",
      }}
    >
      <Typography variant="h3" gutterBottom>
        Upload Requirements
      </Typography>
      <List
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: "10px",
          padding: 0,
        }}
      >
        {requirements.map((req, idx) => (
          <StyledListItem key={idx}>
            <CustomBox>
              {req.icon}
              <ListItemText sx={sxStyles} primary={req.title} />
            </CustomBox>
            <ListItemText sx={sxContentStyles} secondary={req.description} />
          </StyledListItem>
        ))}
      </List>
    </Grid>
  );
};

export default WithUploadRequirements;
