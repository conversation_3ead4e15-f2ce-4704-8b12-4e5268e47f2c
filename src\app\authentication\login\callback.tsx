import { useEffect } from "react";
import { PublicClientApplication } from "@azure/msal-browser";
import { msalConfig } from "@/providers/msalConfig";

const LoginCallback = () => {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Parse hash fragment
      const hash = window.location.hash.substring(1);
      const params = new URLSearchParams(hash.replace(/&/g, '&'));
      const accessToken = params.get('access_token');
      const tokenType = params.get('token_type');
      const expiresIn = params.get('expires_in');
      const scope = params.get('scope');
      if (accessToken) {
        localStorage.setItem('accessToken', accessToken);
        localStorage.setItem('tokenType', tokenType || 'Bearer');
        localStorage.setItem('expiresIn', expiresIn || '');
        localStorage.setItem('scope', scope || '');
        // <PERSON><PERSON><PERSON><PERSON> về trang chính sau khi lưu token
        window.location.href = '/';
      }
    }
  }, []);
  return null;
};

export default LoginCallback;
