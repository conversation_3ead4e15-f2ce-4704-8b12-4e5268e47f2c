import { Configuration } from '@azure/msal-browser';

export const msalConfig: Configuration = {
  auth: {
    clientId: 'e1144a3c-4136-4819-898f-9c485e0c3e8e',
    authority: "https://devmetalxupasia.ciamlogin.com/devmetalxupasia.onmicrosoft.com",
    // authority: 'https://devmetalxupasia.ciamlogin.com/devmetalxupasia.onmicrosoft.com/oauth2/v2.0/authorize',
    // redirectUri: typeof window !== 'undefined' ? 'http://localhost:3000/callback' : '',
    // knownAuthorities: ['devmetalxupasia.ciamlogin.com'],
  },
  cache: {
    cacheLocation: 'localStorage',
    storeAuthStateInCookie: false,
  },
};
