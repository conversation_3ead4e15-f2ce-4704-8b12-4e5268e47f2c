import React, { useEffect, useState } from "react";
import PopupModal from "@/components/common/PopupModal";
import PopupChangePassword from "@/components/common/PopupChangePassword";
import { useMsal } from "@azure/msal-react";
import { signOutMsal } from "@/services/msalService";
import { isSessionExpired, getPasswordExpiryStatus } from "@/utils/sessionUtils";
import { useDispatch, useSelector } from "react-redux";
import { useUser } from "@/features/users/state/useUser";
import { RootState } from "@/store/reducers";
import {
  showSessionExpiredModal,
  hideInternalServerErrorModal,
  hideSessionExpiredModal,
  showPasswordWillExpireSoonModal,
  hidePasswordWillExpireSoonModal,
  showForceChangePasswordExpiredModal,
  hideForceChangePasswordExpiredModal,
  hideForceUserNotFoundModal,
} from "@/features/modal/modalSlice";
import store from "@/store/store";
import { useRouter } from "next/navigation";
import { PAGE_TITLES } from "@/constants/routes";

/**
 * ProfileLoader
 * Handles session expiration, password expiry warnings, and force password change modals.
 * Integrates with Redux and MSAL for authentication/session management.
 */
const ProfileLoader = () => {
  // MSAL hooks for authentication
  const { instance, accounts } = useMsal();
  // Get user profile from custom hook
  const { user } = useUser();
  // Redux dispatch
  const dispatch = useDispatch();
  // Navigation
  const router = useRouter();

  // Modal state from Redux
  const showSessionExpired = useSelector((state: RootState) => state.modal.sessionExpired);
  const showPasswordWillExpireSoon = useSelector((state: RootState) => state.modal.passwordWillExpireSoon);
  const showForceChangePasswordExpired = useSelector((state: RootState) => state.modal.forceChangePasswordExpired);
  const showInternalServerError = useSelector((state: RootState) => state.modal.internalServerError);
  const showForceUserNotFound = useSelector((state: RootState) => state.modal.forceUserNotFound);

  // Local state for change password popup and expiry date
  const [showChangePassword, setShowChangePassword] = useState(false);
  const [passwordExpiryDate, setPasswordExpiryDate] = useState<string | undefined>(undefined);

  // Effect: Check session and password expiry status on user change
  useEffect(() => {
    if (!user) return;

    // Check if session is expired
    const sessionExpired = isSessionExpired(user, localStorage.getItem("lastLogin") || undefined);

    // Check password expiry status
    const passwordStatus = getPasswordExpiryStatus({
      daysUntilPasswordExpiry: (user as any)?.daysUntilPasswordExpiry,
      passwordExpired: (user as any)?.passwordExpired,
      lastPasswordChange: (user as any)?.lastPasswordChange,
    });
    const { passwordWillExpireSoon, forceChangePasswordExpired, passwordExpiryDate } = passwordStatus;

    // Priority: Session expired > Force change password > Password will expire soon
    if (sessionExpired) {
      // Show session expired modal
      dispatch(showSessionExpiredModal());
      dispatch(hideForceChangePasswordExpiredModal());
      dispatch(hidePasswordWillExpireSoonModal());
      setPasswordExpiryDate(undefined);
      return;
    }
    if (forceChangePasswordExpired) {
      // Show force change password modal only
      dispatch(showForceChangePasswordExpiredModal());
      dispatch(hideSessionExpiredModal());
      dispatch(hidePasswordWillExpireSoonModal());
      setPasswordExpiryDate(undefined);
      return;
    }
    if (passwordWillExpireSoon) {
      // Show password will expire soon modal
      dispatch(showPasswordWillExpireSoonModal());
      dispatch(hideSessionExpiredModal());
      dispatch(hideForceChangePasswordExpiredModal());
      setPasswordExpiryDate(passwordExpiryDate);
      return;
    }
    // Hide all modals if no condition met
    dispatch(hideSessionExpiredModal());
    dispatch(hidePasswordWillExpireSoonModal());
    dispatch(hideForceChangePasswordExpiredModal());
    setPasswordExpiryDate(undefined);
    dispatch(hideForceUserNotFoundModal());
  }, [user, dispatch]);

  /**
   * Handler: Session expired modal close
   * Logs out user via MSAL
   */
  const handleSessionExpiredClose = () => {
    dispatch(hideSessionExpiredModal());
    if (accounts && accounts[0]) {
      signOutMsal(instance, accounts[0]);
    }
  };

  /**
   * Handler: Password will expire soon modal close
   * Opens change password popup
   */
  const handlePasswordWillExpireSoonClose = () => {
    dispatch(hidePasswordWillExpireSoonModal());
    setShowChangePassword(true);
  };

  /**
   * Handler: Change password submit
   * TODO: Integrate with API to change password
   */
  const handleChangePasswordSubmit = (values: any) => {
    // TODO: Call API to change password
    setShowChangePassword(false);
  };

  const handleUserNotFound = (page: "list" | "home") => {
    if (page === 'list') {
      router.push('/user-master');
    }else {
      router.push('/');
    }
    dispatch(hideForceUserNotFoundModal());
  }

  return (
    <>
      {/* Session expired modal: blocks all actions until user logs in again */}
      <PopupModal
        open={showSessionExpired}
        title="You have been automatically logged out."
        description="Please login again."
        type="warning"
        isOKOnly
        hideCloseButton
        disableBackdropClick
        onSubmit={handleSessionExpiredClose}
      />

      {/* Password will expire soon modal: allows user to change password */}
      <PopupModal
        open={showPasswordWillExpireSoon}
        title="Password expiration"
        description={
          passwordExpiryDate
            ? `Your password will expire on ${passwordExpiryDate} at 23:59. Please change your password before the expiration date.`
            : "Your password will expire soon. Please change your password."
        }
        type="warning"
        onSubmit={handlePasswordWillExpireSoonClose}
      />

      {/* Force change password modal: only allows user to proceed to change password */}
      <PopupModal
        open={showForceChangePasswordExpired}
        title="Password expired."
        description="Please set a new password."
        type="error"
        isOKOnly
        hideCloseButton
        disableBackdropClick
        onSubmit={() => {
          dispatch(hideForceChangePasswordExpiredModal());
          setShowChangePassword(true);
        }}
      />
      <PopupModal
        open={showInternalServerError}
        onClose={() => dispatch(hideInternalServerErrorModal())}
        title="Error"
        description="An error occurred, please try again later."
        isOKOnly
        type="error"
      />

      {/* Change password dialog */}
      <PopupChangePassword
        open={showChangePassword}
        onClose={() => setShowChangePassword(false)}
        onSubmit={handleChangePasswordSubmit}
        errors={{}}
      />

      <PopupModal
        open={showForceUserNotFound}
        onClose={() => handleUserNotFound('list')}
        title="User Not Found"
        description="The user you are trying to edit does not exist or has been deleted."
        type="error"
        disableBackdropClick
        hideCloseButton
        labelCancel={"Back to " + PAGE_TITLES.USER_LIST}
        labelSubmit="Go to Home"
        onSubmit={() => handleUserNotFound('home')}
      />
    </>
  );
};

export default ProfileLoader;
