// src/constants/api.ts
// API base URLs and HTTP status codes

export const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000/api';

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
};

export const REQUEST_TIMEOUT = {
  DEFAULT: 30000, // 30 seconds
  AUTH: 15000, // 15 seconds for auth
};
