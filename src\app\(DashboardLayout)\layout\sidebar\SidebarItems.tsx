"use client";

import KeyboardDoubleArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import {
  Box,
  IconButton,
  List,
  ListItemButton,
  ListItemText,
  Popover,
  Tooltip,
  Typography,
} from "@mui/material";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Sidebar as MUI_Sidebar,
  Menu,
  MenuItem,
  Submenu,
} from "react-mui-sidebar";
import React, { useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { selectUserAccessMenus } from "@/features/users/state/usersSelectors";
import Menuitems from "./MenuItems";

type IconLink = {
  active: string;
  default: string;
};

type MenuItemNode = {
  id: string;
  title: string;
  href?: string;
  screenCode?: string;
  iconLink?: IconLink;
  children?: MenuItemNode[];
  subheader?: string;
};

type UserAccessMenu = {
  screenCode: string;
  canView: number;
};

const normalize = (s: string) => (s === "/" ? "/" : s.replace(/\/+$/, ""));
const isActive = (pathname: string, href?: string) => {
  if (!href) return false;
  const p = normalize(pathname);
  const h = normalize(href);
  if (h === "/") return p === "/";
  return p === h || p.startsWith(h + "/");
};

const COLORS = {
  red: "#ca002e",
  default: "#000000",
};

interface SidebarItemsProps {
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

const SidebarItems = ({
  collapsed = false,
  onToggleCollapse,
}: SidebarItemsProps) => {
  const pathname = usePathname();
  const userAccessMenus = useSelector(selectUserAccessMenus) as UserAccessMenu[] | undefined;

  // Lọc menu theo quyền xem (screenCode + canView)
  const filteredMenuItems = useMemo(() => {
    const filterMenu = (items: MenuItemNode[]): MenuItemNode[] =>
      items
        .map((item) => {
          if (item.children && item.children.length) {
            const children = filterMenu(item.children);
            if (children.length) return { ...item, children };
            return null;
          }
          if (item.screenCode) {
            const ok = userAccessMenus?.some(
              (a) => a.screenCode === item.screenCode && a.canView === 1
            );
            return ok ? item : null;
          }
          return item;
        })
        .filter(Boolean) as MenuItemNode[];

    return filterMenu(Menuitems as unknown as MenuItemNode[]);
  }, [userAccessMenus]);

  // Popover state cho sidebar (khi collapsed)
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [activeSubMenuId, setActiveSubMenuId] = useState<string | null>(null);

  const handleOpenPopover = (event: React.MouseEvent<HTMLElement>, parentId: string) => {
    setAnchorEl(event.currentTarget);
    setActiveSubMenuId(parentId);
  };
  const handleClosePopover = () => {
    setAnchorEl(null);
    setActiveSubMenuId(null);
  };

  const renderMenuItems = (
    items: MenuItemNode[],
    isChild = false
  ): React.ReactNode => {
    return items.map((item) => {
      // Subheader
      if (item.subheader) {
        if (collapsed) return null;
        return <Menu subHeading={item.subheader} key={`subheader-${item.subheader}`} />;
      }

      // Có children => render submenu
      if (item.children && item.children.length) {
        const activeMenuParent = item.children.some((child) => isActive(pathname, child.href));
        const itemIconChildren = item.iconLink ? (
          <Image
            src={activeMenuParent ? item.iconLink.active : item.iconLink.default}
            alt="icon menu"
            width={20}
            height={20}
          />
        ) : null;

        return (
          <Box
            key={item.id}
            className={activeMenuParent ? "menu-parent-child-selected" : ""}
            sx={{
              padding: collapsed ? "0 20px" : "0 30px",
              ".MuiCollapse-wrapperInner .MuiList-root .MuiBox-root": {
                "&:last-child .item-children-menu::before": {
                  height: "22%",
                },
              },
              ".MuiListItemText-root": { display: collapsed ? "none" : {} },
              "&.menu-parent-child-selected .MuiListItemButton-root:not(.item-children-menu .MuiListItemButton-root), .item-children-selected-collapsed":
                {
                  color: `${COLORS.red} !important`,
                  backgroundColor: "#e1e4ea !important",
                  ".MuiTypography-root": {
                    fontSize: "16px !important",
                    fontWeight: "700 !important",
                  },
                  "&::before": {
                    content: '""',
                    position: "absolute",
                    left: 0,
                    top: 0,
                    width: "3px",
                    height: "100%",
                    borderRadius: "10px",
                    backgroundColor: "rgba(202, 0, 46, 1)",
                  },
                },
              ".MuiListItemButton-root": {
                color: "rgb(43, 43, 43)",
                padding: "13px 20px",
                gap: collapsed ? 0.5 : 2,
                marginBottom: "4px",
                backgroundColor: "inherit",
                "&:hover": {
                  backgroundColor: "#e1e4ea !important",
                  color: COLORS.default,
                },
              },
            }}
          >
            {collapsed ? (
              <>
                <IconButton
                  className={activeMenuParent ? "item-children-selected-collapsed" : ""}
                  key={item.id}
                  onClick={(e) => handleOpenPopover(e, item.id)}
                  sx={{
                    padding: "13px 20px",
                    borderRadius: "0 10px 10px 0",
                    width: "100%",
                    ":hover": { backgroundColor: "#e1e4ea" },
                  }}
                >
                  {itemIconChildren}
                </IconButton>

                {/* Popover cho submenu khi collapsed */}
                <Popover
                  open={anchorEl !== null && activeSubMenuId === item.id}
                  anchorEl={anchorEl}
                  onClose={handleClosePopover}
                  anchorOrigin={{ vertical: "top", horizontal: "right" }}
                  transformOrigin={{ vertical: "top", horizontal: "left" }}
                  slotProps={{
                    paper: {
                      sx: {
                        ml: 1.5,
                        borderRadius: 1.5,
                        boxShadow: 4,
                        minWidth: 172,
                      },
                    },
                  }}
                >
                  <List sx={{ padding: "8px 8px 4px 8px", bgcolor: "#eff2f7" }}>
                    {item.children.map((submenu) => {
                      const selected = isActive(pathname, submenu.href);
                      return (
                        <ListItemButton
                          key={submenu.id}
                          selected={selected}
                          component={Link}
                          href={submenu.href || "#"}
                          onClick={handleClosePopover}
                          sx={{
                            mb: 0.5,
                            borderRadius: 1,
                            backgroundColor: selected
                              ? "#e1e4ea !important"
                              : "transparent !important",
                            "&:hover": { backgroundColor: "#e1e4ea !important" },
                          }}
                        >
                          <ListItemText
                            primary={
                              <Typography
                                variant="body1"
                                sx={{ color: selected ? "#ca002eff" : "", fontSize: 12 }}
                              >
                                {submenu.title}
                              </Typography>
                            }
                          />
                        </ListItemButton>
                      );
                    })}
                  </List>
                </Popover>
              </>
            ) : (
              <Submenu
                id={`submenu-${item.id}`}
                key={item.id}
                title={collapsed ? undefined : item.title}
                icon={itemIconChildren}
                borderRadius="7px"
              >
                {renderMenuItems(item.children, true)}
              </Submenu>
            )}
          </Box>
        );
      }

      // Leaf item (không có children)
      const active = isActive(pathname, item.href);
      const icon = item.iconLink ? (
        <Image
          src={active ? item.iconLink.active : item.iconLink.default}
          alt="icon menu"
          width={20}
          height={20}
        />
      ) : null;

      // Root item (không phải submenu)
      if (!isChild) {
        return (
          <Box
            px={collapsed ? 2.5 : 3.75}
            key={item.id}
            width="100%"
            sx={{
              ".MuiListItemButton-root": {
                justifyContent: "center",
                display: "flex",
                ".MuiListItemText-root": { display: collapsed ? "none" : {} },
                padding: "13px 20px",
                gap: "16px",
                "&:hover": {
                  backgroundColor: "#e1e4ea !important",
                  color: COLORS.default,
                },
                "&.Mui-selected": {
                  color: `${COLORS.red} !important`,
                  backgroundColor: "#e1e4ea !important",
                  "::before": {
                    content: '""',
                    position: "absolute",
                    left: 0,
                    top: 0,
                    width: "3px",
                    height: "100%",
                    borderRadius: "10px",
                    backgroundColor: "rgba(202, 0, 46, 1)",
                  },
                },
              },
            }}
          >
            <MenuItem
              key={item.id}
              isSelected={active}
              borderRadius="0px 10px 10px 0px"
              icon={icon}
              link={item.href}
              component={Link}
            >
              <Box
                sx={{
                  position: "relative",
                  "&:before": {
                    content: '""',
                    background: "radial-gradient(#d2f1df, #d3d7fa, #bad8f4)",
                    backgroundSize: "400% 400%",
                    animation: "gradient 15s ease infinite",
                    position: "absolute",
                    height: "100%",
                    width: "100%",
                    opacity: "0.3",
                  },
                }}
              />
              <Typography sx={{ fontWeight: active ? 700 : 400, fontSize: "16px" }}>
                {!collapsed && item.title}
              </Typography>
            </MenuItem>
          </Box>
        );
      }

      // Submenu leaf item
      const activeChildren = isActive(pathname, item.href);
      return (
        <Box
          key={item.id}
          className="item-children-menu"
          sx={{
            display: "grid",
            ml: 5,
            gap: 1,
            position: "relative",
            width: "calc(100% - 40px) !important",
            ".MuiListItemIcon-root": { display: "none" },
            ".MuiListItemButton-root": {
              backgroundColor: activeChildren ? "#e1e4ea" : "inherit",
            },
            "&::before": {
              content: '""',
              position: "absolute",
              left: -24,
              top: 0,
              bottom: 0,
              width: 2,
              background: "rgba(225, 228, 234, 1)",
              borderRadius: 2,
            },
            // Ẩn đường dọc nếu đây là phần tử cuối
            "&:last-of-type::before": {
              display: "none",
            },
            ".MuiBox-root:last-of-type::after": {
              content: '""',
              position: "absolute",
              left: -24,
              bottom: -8,
              width: 24,
              height: 24,
              borderLeft: "2px solid rgba(225, 228, 234, 1)",
              borderBottom: "2px solid rgba(225, 228, 234, 1)",
              borderBottomLeftRadius: 12,
              top: 0,
            },
          }}
        >
          <MenuItem key={item.id} borderRadius="8px" link={item.href} component={Link}>
            <Typography
              sx={{
                fontWeight: 500,
                fontSize: "12px",
                color: activeChildren ? "rgba(202, 0, 46, 1)" : "rgba(34, 34, 34, 1)",
              }}
            >
              {!collapsed && item.title}
            </Typography>
          </MenuItem>
        </Box>
      );
    });
  };

  return (
    <MUI_Sidebar width={"100%"} showProfile={false} themeColor={"#5D87FF"} themeSecondaryColor={"#49beff"}>
      {/* Logo + toggle collapse */}
      <Box
        sx={{
          position: "relative",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          px: collapsed ? 1 : 2,
          py: 1.5,
          borderBottom: "1px solid #f0f0f0",
        }}
      >
        <Link href="/" style={{ display: "inline-block" }}>
          <Image
            src="/images/logos/dark-logo.svg"
            alt="Logo"
            width={collapsed ? 52 : 100}
            height={collapsed ? 52 : 100}
            style={{ transition: "all 0.2s ease", objectFit: "contain", cursor: "pointer" }}
          />
        </Link>

        {onToggleCollapse && (
          <Tooltip title={collapsed ? "Expand sidebar" : "Collapse sidebar"} placement="right">
            <IconButton
              onClick={onToggleCollapse}
              size="small"
              sx={{
                position: "absolute",
                right: collapsed ? 3 : 12,
                top: "50%",
                transform: "translateY(-50%)",
                width: 20,
                height: 20,
                "& svg": { fontSize: "14px" },
                "&:hover": { background: "#e1eae4", color: "#ca002e" },
              }}
            >
              {collapsed ? <KeyboardDoubleArrowRightIcon /> : <KeyboardDoubleArrowLeftIcon />}
            </IconButton>
          </Tooltip>
        )}
      </Box>

      <Box
        sx={{
          gap: "10px",
          display: "flex",
          flexDirection: "column",
          alignItems: collapsed ? "center" : "start",
          ".MuiBox-root": collapsed ? {} : { width: "100%" },
        }}
      >
        {renderMenuItems(filteredMenuItems, false)}
      </Box>
    </MUI_Sidebar>
  );
};

export default SidebarItems;