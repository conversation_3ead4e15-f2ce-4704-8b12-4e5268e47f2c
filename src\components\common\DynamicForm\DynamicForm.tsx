"use client";
import Button from "@/components/common/button/Button";
import { <PERSON><PERSON>, Box, CircularProgress } from "@mui/material";
import React from "react";
import { fieldSx, formSx } from "./DynamicForm.styles";
import { DynamicFormProps } from "./DynamicForm.types";
import {
  AutocompleteField,
  CheckboxField,
  DateField,
  DateRangeField,
  FileField,
  RadioField,
  SelectField,
  TextField,
} from "./fields";
import SelectMultipleField from "./fields/SelectMultipleField";
import { useDynamicForm } from "./hooks/useDynamicForm";
import { useFormConfig } from "./hooks/useFormConfig";
import TextRangeField from "./fields/TextRageField";

const fieldComponentMap: Record<string, any> = {
  text: TextField,
  email: TextField,
  password: TextField,
  number: TextField,
  textarea: TextField,
  select: SelectField,
  autocomplete: AutocompleteField,
  checkbox: CheckboxField,
  radio: RadioField,
  date: Date<PERSON><PERSON>,
  file: <PERSON><PERSON>ield,
  dateRange: Date<PERSON><PERSON><PERSON><PERSON><PERSON>,
  selectMultiple: SelectMultipleField,
  textRange: TextRangeField
};

type SubmitResult = {
  fieldErrors?: Record<string, string | undefined>;
  formError?: string;
};

interface DynamicFormWithRenderFieldsProps extends DynamicFormProps {
  renderFields?: (
    fields: any[],
    FieldComponent: any,
    control: any,
    formState: any,
    loading: boolean,
    i18n?: (key: string) => string
  ) => React.ReactNode;
  hideCancel?: boolean;
  hideSubmit?: boolean;
  canSubmit?: boolean;
  errors?: Record<string, string | undefined>;
  skipValidationOnSubmit?: boolean;
}

const DynamicForm: React.FC<DynamicFormWithRenderFieldsProps> = ({
  fields,
  onCancel,
  onSubmit,
  defaultValues = {},
  loading,
  error,
  submitLabel = "Submit",
  formClassName,
  fieldClassName,
  i18n,
  children,
  renderFields,
  hideSubmit,
  hideCancel,
  errors,
  canSubmit,
  skipValidationOnSubmit
}) => {
  const safeFields = fields ?? [];

  const form = useDynamicForm(safeFields, defaultValues);
  const { handleSubmit, control, formState, watch, setError, clearErrors } = form;
  const values = watch();
  const visibleFields = useFormConfig(safeFields, values);

  React.useEffect(() => {
    if (!errors) return;
    Object.keys(errors).forEach((name) => {
      if (!errors[name]) clearErrors(name as any);
    });
    Object.entries(errors).forEach(([name, message]) => {
      if (message) {
        setError(name as any, { type: "server", message: String(message) }, { shouldFocus: true });
      }
    });
  }, [errors, setError, clearErrors]);

  const RenderFieldComponent = React.useCallback((fieldProps: any) => {
    const FieldComponent = fieldComponentMap[fieldProps.field.type];
    if (!FieldComponent) return null;
    return <FieldComponent {...fieldProps} />;
  }, []);

  const [serverFormError, setServerFormError] = React.useState<string | undefined>(undefined);

  const handleCancel = () => {
    if (onCancel) {
      onCancel(form.getValues ? form.getValues() : values);
    }
  };

  
  // useEffect(() => {
  //   if (errors && Object.keys(errors).length !== 0) {
  //     for (const [err, msg] of Object.entries(errors)) {
  //       if (err === "oldPassword" && hasFirstChanged.current[err]) {
  //         setError(
  //           err,
  //           {
  //             type: "manual",
  //             message: msg as string,
  //           },
  //           { shouldFocus: true }
  //         );
  //         console.log(errors);
  //       }
  //       if (err === "newPassword" && hasFirstChanged.current[err]) {
  //         setError(
  //           err,
  //           {
  //             type: "manual",
  //             message: msg as string,
  //           },
  //           { shouldFocus: true }
  //         );
  //         console.log(errors);
  //       }
  //     }
  //   }
  // }, [errors, setError]);

  // const hasFirstChanged = useRef<Record<string, boolean>>({});
  // useEffect(() => {
  //   const subscription = watch((values, { name }) => {
  //     if (name === "oldPassword") {
  //       if (values.oldPassword && values.oldPassword.length > 0) {
  //         hasFirstChanged.current["oldPassword"] = true;
  //       }
  //     }
  //     if (name === "newPassword") {
  //       if (values.newPassword && values.newPassword.length > 0) {
  //         hasFirstChanged.current["newPassword"] = true;
  //       }
  //     }
  //     if (name === "confirmNewPassword") {
  //       if (values.confirmNewPassword && values.confirmNewPassword.length > 0) {
  //         hasFirstChanged.current["confirmNewPassword"] = true;
  //       }
  //     }
  //     if (name === "newPassword" && hasFirstChanged.current["confirmNewPassword"]) {
  //       trigger("confirmNewPassword");
  //     }
  //     if (name === "oldPassword" && hasFirstChanged.current["newPassword"]) {
  //       trigger("newPassword");
  //     }
  //     clearErrors();
  //   });
  //   return () => subscription.unsubscribe();
  // }, [watch, trigger, clearErrors]);

  const onSubmitInternal = async (formData: any) => {
    console.log(formData)
    // Clear any previous form-level error banner (kept for compatibility)
    setServerFormError(undefined);
    // Helper: apply server field errors to RHF and focus the first one
    const applyFieldErrors = (errs?: Record<string, string | undefined>) => {
      if (!errs) return;
      let focused = false;
      Object.entries(errs).forEach(([name, message]) => {
        if (!message) return;
        setError(name as any, { type: "server", message: String(message) }, { shouldFocus: !focused });
        focused = true;
      });
    };
    try {
      // Call parent onSubmit; it may return a normalized result
      const result = (await (onSubmit as any)?.(formData)) as void | SubmitResult | undefined;
      // Only handle fieldErrors; ignore formError
      if (result && typeof result === "object") {
        applyFieldErrors(result.fieldErrors);
      }
    } catch (e: any) {
      // Only handle thrown fieldErrors; ignore form-level error/message
      applyFieldErrors(e?.fieldErrors as Record<string, string | undefined> | undefined);
    }
  };

    const formSubmitHandler = skipValidationOnSubmit
    ? (e: React.FormEvent) => {
        e.preventDefault();
        Object.keys(formState.errors).forEach((name) => clearErrors(name as any));
        onSubmitInternal(form.getValues());
      }
    : handleSubmit(onSubmitInternal);
  return (
    <Box sx={formSx}>
      <Box
        component="form"
        onSubmit={formSubmitHandler}
        className={formClassName}
        noValidate
        autoComplete="off"
        aria-live="polite"
      >
        {(error || serverFormError) && <Alert severity="error">{error || serverFormError}</Alert>}

        {renderFields
          ? renderFields(visibleFields, RenderFieldComponent, control, formState, loading ?? false, i18n)
          : visibleFields.map((field) => {
              const FieldComponent = fieldComponentMap[field.type];
              if (!FieldComponent) return null;
              return (
                <Box key={field.name} sx={fieldSx} className={fieldClassName + "-" + field.type}>
                  <FieldComponent
                    field={field}
                    control={control}
                    error={formState.errors[field.name]?.message}
                    disabled={loading}
                    i18n={i18n}
                  />
                </Box>
              );
            })}
        {children}
        <Box
          className="buttonActions"
          gap={2.5}
          sx={{
            display: "flex",
            justifyContent: hideCancel ? "center" : "flex-end",
          }}
        >
          {!hideCancel && (
            <Button
              className="buttonCancle"
              sx={{
                mt: 2,
                minWidth: "91px",
                border: "1px solid rgba(162, 161, 168, 0.2)",
              }}
              type="button"
              variant="outlined"
              size="medium"
              onClick={handleCancel}
            >
              Cancel
            </Button>
          )}
          {!hideSubmit && (
            <Button
              className="buttonSubmit"
              sx={{ mt: 2, minWidth: "91px" }}
              type="submit"
              variant="contained"
              size="medium"
              // disabled={!formState.isValid || !canSubmit}
            >
              {loading ? <CircularProgress size={24} /> : submitLabel}
            </Button>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default DynamicForm;
