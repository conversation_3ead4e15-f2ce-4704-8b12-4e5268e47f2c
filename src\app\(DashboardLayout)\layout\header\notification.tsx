import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>tonB<PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  IconButton,
  <PERSON><PERSON>,
  Ty<PERSON><PERSON>,
  Skeleton,
} from "@mui/material";
import { uniqueId } from "lodash";
import Image from "next/image";
import React, { useMemo, useState, useCallback, useEffect, useRef } from "react";

type Notification = {
  id: string;
  title: string;
  category: string;
  body: string;
  createdAt: Date;
  read: boolean;
};

// Factory to create sample notifications (keeps ids stable per module load)
// Generate a larger set so pagination (PAGE_SIZE = 20) can load ~3 times
const createInitialNotifications = (): Notification[] => {
  const TOTAL = 20 * 3; // 3 pages of 20
  return Array.from({ length: TOTAL }).map((_, i) => ({
    id: uniqueId("notif_"),
    title: `Notification ${i + 1}`,
    category: i % 2 === 0 ? "User List" : "Millsheet",
    body: "This is a sample notification body for preview and testing.",
    // spread createdAt times so minutesAgo varies
    createdAt: new Date(Date.now() - (i + 1) * 60 * 1000),
    // make about a third unread to simulate realistic data
    read: i % 3 !== 0,
  }));
};

type NotificationProps = {
  externalUnreadCount?: number; // number of unread notifications managed externally (e.g. realtime push)
  onUnreadChange?: (count: number) => void; // callback to notify parent when unread count changes locally
};

const Notification: React.FC<NotificationProps> = ({
  externalUnreadCount,
  onUnreadChange,
}) => {
  // Reusable skeleton rows to avoid duplicated markup and minimize re-renders
  const SkeletonRows = React.useMemo(() => {
    const Row: React.FC<{ count?: number; rowHeight?: number }> = ({
      count = 1,
      rowHeight = 64,
    }) => (
      <>
        {Array.from({ length: count }).map((_, i) => (
          <Box
            key={i}
            sx={{
              display: "flex",
              gap: 1.5,
              py: 1.25,
              minHeight: rowHeight,
              alignItems: "center",
              opacity: 0,
              animation: "fadeIn 220ms ease forwards",
              "@keyframes fadeIn": {
                to: { opacity: 1 },
              },
            }}
          >
            <Skeleton variant="circular" width={32} height={32} />
            <Box sx={{ flex: 1 }}>
              <Skeleton width="60%" height={16} />
              <Skeleton width="80%" height={14} sx={{ mt: 0.5 }} />
              <Skeleton width="30%" height={12} sx={{ mt: 0.5 }} />
            </Box>
          </Box>
        ))}
      </>
    );

    return React.memo(Row);
  }, []);

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  // initialize once
  const initialItems = useMemo(() => createInitialNotifications(), []);
  // start empty, fetch on open (simulates API). initialItems kept for local data and later API replacement
  const [items, setItems] = useState<Notification[]>([]);
  const [initialLoaded, setInitialLoaded] = useState(false);
  const initialLoadedRef = useRef(false);
  const fetchTimerRef = useRef<number | null>(null);
  const loadMoreTimerRef = useRef<number | null>(null);

  const open = Boolean(anchorEl);

  // Derived values
  const unreadItems = useMemo(() => items.filter((it) => !it.read), [items]);
  const derivedUnreadCount = unreadItems.length;
  // If parent provides externalUnreadCount (from realtime push), prefer that. Otherwise use derived count from items
  const unreadCount = externalUnreadCount ?? derivedUnreadCount;

  const PAGE_SIZE = 20;
  const [visibleCount, setVisibleCount] = useState<number>(PAGE_SIZE);
  const [loading, setLoading] = useState(false);

  const hasMore = useMemo(
    () => visibleCount < items.length,
    [visibleCount, items.length]
  );
  const itemsDisplay = useMemo(
    () => items.slice(0, visibleCount),
    [items, visibleCount]
  );

  // Simulated fetch function — replace with real API call later
  const fetchNotifications = useCallback(async () => {
    if (initialLoadedRef.current) return;
    setLoading(true);
    // simulate network delay using a timer ref so we can cleanup
    await new Promise<void>((res) => {
      fetchTimerRef.current = window.setTimeout(() => res(), 2000) as unknown as number;
    });
    setItems(initialItems);
    setLoading(false);
    initialLoadedRef.current = true;
    setInitialLoaded(true);
  }, [initialItems]);

  // Simulated fetch for loading more items (pagination)
  const fetchMoreNotifications = useCallback(async () => {
    if (!hasMore || loading) return;
    setLoading(true);
    await new Promise<void>((res) => {
      loadMoreTimerRef.current = window.setTimeout(() => res(), 2000) as unknown as number;
    });
    // For now, there's no remote source; just increase visible count to reveal more of local items
    setVisibleCount((v) => Math.min(items.length, v + PAGE_SIZE));
    setLoading(false);
  }, [hasMore, loading, items.length]);

  // Trigger fetch when menu is opened (decoupled from click handler)
  useEffect(() => {
    if (open && !initialLoadedRef.current) {
      fetchNotifications();
    }
  }, [open, fetchNotifications]);

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      if (fetchTimerRef.current) {
        clearTimeout(fetchTimerRef.current as unknown as number);
      }
      if (loadMoreTimerRef.current) {
        clearTimeout(loadMoreTimerRef.current as unknown as number);
      }
    };
  }, []);

  // Actions
  const markAll = useCallback(() => {
    setItems((prev) => {
      const updated = prev.map((n) => ({ ...n, read: true }));
      // notify parent about new unread count if callback provided
      if (onUnreadChange) onUnreadChange(0);
      return updated;
    });
  }, [onUnreadChange]);

  const markRead = useCallback(
    (id: string) => {
      setItems((prev) => {
        const updated = prev.map((n) =>
          n.id === id ? { ...n, read: true } : n
        );
        const newLocalUnread = updated.filter((it) => !it.read).length;
        if (onUnreadChange) onUnreadChange(newLocalUnread);
        return updated;
      });
    },
    [onUnreadChange]
  );

  // Use fetchMoreNotifications which simulates an API call and 2s wait
  const loadMore = useCallback(() => {
    fetchMoreNotifications();
  }, [fetchMoreNotifications]);

  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLElement>) => {
      if (!hasMore || loading) return;
      const target = e.target as HTMLElement;
      const { scrollTop, scrollHeight, clientHeight } = target;
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        loadMore();
      }
    },
    [hasMore, loading, loadMore]
  );

  // Utility to compute minutes ago safely
  const minutesAgo = useCallback((d: Date) => {
    const minutes = Math.max(1, Math.round((Date.now() - d.getTime()) / 60000));
    return minutes;
  }, []);

  return (
    <>
      <IconButton
        sx={{
          borderRadius: "10px",
          padding: "13px",
          backgroundColor: "#ECEFF4",
          "&:hover": { backgroundColor: "#ECEFF4" },
          height: "50px",
          width: "50px",
        }}
        color="inherit"
        aria-controls="msgs-menu"
        aria-haspopup="true"
        onClick={(e) => setAnchorEl(e.currentTarget)}
      >
        <Badge
          badgeContent={unreadCount}
          max={99}
          sx={{
            ".MuiBadge-standard": {
              border: "0.5px solid #A20025",
              backgroundColor: "#CA002E",
              color: "#fff",
              height: "18px",
              width: "18px",
              minWidth: "auto",
              top: "1px",
              right: "3px  ",
              fontSize: "9px",
            },
          }}
        >
          <Image
            src={
              open
                ? "/images/header/active/notification.svg"
                : "/images/header/notification.svg"
            }
            alt="notification"
            width={24}
            height={24}
          />
        </Badge>
      </IconButton>

      <Menu
        id="notification-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={() => setAnchorEl(null)}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        transformOrigin={{ vertical: "top", horizontal: "right" }}
        PaperProps={{
          sx: {
            borderRadius: "12px",
            overflow: "hidden",
            mt: 1,
            ml: 27,
            width: "320px",
            ".MuiList-padding": {
              paddingTop: "unset",
              paddingBottom: "unset",
            },
            border: "1px solid rgba(229, 231, 235, 1)",
          },
        }}
      >
        {/* Header */}
        <Box sx={{ px: 2, py: 1.75 }}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              mb: 0.5,
            }}
          >
            <Typography
              variant="subtitle1"
              fontWeight={600}
              fontSize={18}
              lineHeight="130%"
            >
              Notification
            </Typography>

            <Box
              onClick={unreadCount ? markAll : undefined}
              sx={{
                display: "inline-flex",
                alignItems: "center",
                gap: 0.5,
                cursor: unreadCount ? "pointer" : "default",
              }}
            >
              <Image
                src="/images/notification/mark.svg"
                alt="mark"
                height={12}
                width={12}
              />
              <Typography
                fontSize={12}
                fontWeight={400}
                color="rgba(37, 99, 235, 1)"
              >
                Mark all as read
              </Typography>
            </Box>
          </Box>

          <Typography
            variant="caption"
            color="rgba(68, 86, 91, 1)"
            fontSize={14}
            lineHeight="150%"
          >
            You have {unreadCount} unread notifications
          </Typography>
        </Box>
        <Divider sx={{ border: "1.25px solid rgba(236, 239, 244, 1)" }} />

        {/* List */}
        {loading && items.length === 0 ? (
          // Reserve vertical space to prevent layout jumps when items arrive
          <Box
            sx={{
              maxHeight: 360,
              overflowY: "auto",
              px: 2,
              py: 1,
              minHeight: Math.min(PAGE_SIZE, 6) * 64,
            }}
          >
            <SkeletonRows count={Math.min(PAGE_SIZE, 6)} />
          </Box>
        ) : items.length > 0 ? (
          <Box
            sx={{ maxHeight: 360, overflowY: "auto" }}
            onScroll={handleScroll}
          >
            {itemsDisplay.map((n) => {
              const minutes = minutesAgo(n.createdAt);
              return (
                <ButtonBase
                  key={n.id}
                  onClick={() => markRead(n.id)}
                  sx={{
                    width: "100%",
                    textAlign: "left",
                    alignItems: "stretch",
                    display: "block",
                    borderBottom: "1.25px solid #eceff4ff",
                    px: 2,
                    py: 2,
                    position: "relative",
                    bgcolor: n.read ? undefined : "rgba(236, 239, 244, 1)",
                  }}
                >
                  <Box sx={{ display: "flex", gap: 1.5 }}>
                    <Avatar
                      variant="circular"
                      sx={{
                        width: 32,
                        height: 32,
                        bgcolor: "rgba(202, 0, 46, 1)",
                        fontSize: 14,
                        fontWeight: 600,
                      }}
                    >
                      {n.category === "User List" ? (
                        <Image
                          src="/images/notification/users.svg"
                          alt="users"
                          width={24}
                          height={24}
                        />
                      ) : (
                        <Image
                          src="/images/notification/list.svg"
                          alt="list"
                          width={24}
                          height={24}
                        />
                      )}
                    </Avatar>

                    <Box sx={{ flex: 1, minWidth: 0 }}>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          gap: 1.5,
                          flexWrap: "wrap",
                          justifyContent: "space-between",
                        }}
                      >
                        <Typography
                          variant="subtitle2"
                          fontWeight={700}
                          fontSize={14}
                          color="rgba(34, 34, 34, 1)"
                          lineHeight="150%"
                        >
                          {n.title}
                        </Typography>
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            gap: 1,
                          }}
                        >
                          <Chip
                            label={n.category}
                            size="small"
                            sx={{
                              lineHeight: "120%",
                              fontSize: 12,
                              bgcolor: "rgba(220, 252, 231, 1)",
                              color: "rgba(122, 0, 28, 1)",
                            }}
                          />
                          <Box
                            sx={{
                              visibility: !n.read ? "visible" : "hidden",
                              ml: "auto",
                              width: 10,
                              height: 10,
                              bgcolor: "rgba(59, 130, 246, 1)",
                              borderRadius: "50%",
                              flexShrink: 0,
                              mt: "2px",
                            }}
                          />
                        </Box>
                      </Box>

                      <Typography
                        variant="body2"
                        color="rgba(71, 74, 81, 1)"
                        mt={0.5}
                        display="-webkit-box"
                        overflow="hidden"
                        fontSize={14}
                        lineHeight="150%"
                        sx={{
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: "vertical",
                        }}
                      >
                        {n.body}
                      </Typography>

                      <Typography
                        variant="caption"
                        color="rgba(68, 86, 91, 1)"
                        fontSize={12}
                        lineHeight="16px"
                        mt={0.75}
                        display="block"
                      >
                        {minutes} minute ago
                      </Typography>
                    </Box>
                  </Box>
                </ButtonBase>
              );
            })}

            {hasMore && loading && (
              // Append small skeleton rows inline so scroll position doesn't jump
              <Box sx={{ maxHeight: 360, overflowY: "auto", px: 2, py: 1 }}>
                <SkeletonRows count={2} />
              </Box>
            )}

            {/* All loaded indicator when there are items and no more to load */}
            {!hasMore && items.length > 0 && (
              <Box
                sx={{
                  // position: "sticky",
                  bottom: 0,
                  left: 0,
                  right: 0,
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  py: 1.25,
                  background: "linear-gradient(to top, rgba(0,0,0,0.03) 0%)",
                  borderBottomLeftRadius: 12,
                  borderBottomRightRadius: 12,
                  // subtle fade-in
                  transition: "opacity 300ms ease",
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Image
                    src="/images/notification/mark.svg"
                    alt="done"
                    width={14}
                    height={14}
                  />
                  <Typography
                    sx={{
                      fontSize: 13,
                      color: "rgba(99,115,129,1)",
                      fontWeight: 500,
                    }}
                  >
                    You&apos;re all caught up
                  </Typography>
                </Box>
              </Box>
            )}
          </Box>
        ) : (
          <Box
            height={125}
            display="flex"
            gap={1.25}
            py={2}
            px={2}
            justifyContent="center"
            alignItems="center"
          >
            <Image
              src="/images/notification/notification-silent.svg"
              alt="users"
              width={24}
              height={24}
            />
            <Typography>You have no notifications</Typography>
          </Box>
        )}
      </Menu>
    </>
  );
};

export default Notification;
