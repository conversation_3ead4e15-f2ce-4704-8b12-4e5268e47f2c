"use client";
import PageContainer from "@/app/(DashboardLayout)/components/container/PageContainer";
import Button from "@/components/common/button/Button";
import type {
  FieldConfig,
  FieldOption,
} from "@/components/common/DynamicForm/DynamicForm.types";
import DynamicSearch from "@/components/common/DynamicSearch/DynamicSearch";
import DynamicDataGrid, {
  DynamicDataGridColumn,
} from "@/components/common/DynamicTable/DynamicDataGrid";
import PopupModal from "@/components/common/PopupModal";
import { useSnackbar } from "@/components/common/SnackBar/SnackbarProvider";
import { useBreadcrumb } from "@/features/breadcrumb/hook/useBreadcrumb";
import { getCustomerList } from "@/features/customer/state/customerSelector";
import {
  ConditionSearch,
  MappingColumnSort,
  MILLSHEET_STATUS,
  MillSheetSearchParams,
  Sort,
  type MillSheetEdit,
} from "@/features/millsheet/types/millSheetTypes";
import { selectUserProfile } from "@/features/users/state/usersSelectors";
import * as MillSheetService from "@/services/millSheetService";
import { ORG_TYPES, ROLE_CODE } from "@/types/auth";
import { Box, Typography } from "@mui/material";
import { GridSortModel } from "@mui/x-data-grid";
import dayjs from "dayjs";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import MillSheetEditPopup from "./MillSheetEditPopup";
import { PAGE_TITLES } from "@/constants/routes";
import { useMillSheetQueryIds } from "@/utils/useFileIdParam";

const DEFAULT_PAGE_SIZE = 20;
const STATUS_EDIT = 4;
const INITIAL_CONDITION: ConditionSearch = { viewMode: 1 };
const INITIAL_SORT: Sort[] = [];
const INITIAL_PAGINATION = { pageNumber: 1, pageSize: DEFAULT_PAGE_SIZE };
const INITIAL_SEARCH_PARAMS: MillSheetSearchParams = {
  condition: INITIAL_CONDITION,
  sort: INITIAL_SORT,
  pagination: INITIAL_PAGINATION,
};

interface MillSheetRecord {
  id: number | string;
  status: number;
  serialNumber: string;
  mill: string;
  customer: string;
  invoiceNo?: string;
  poNo?: string;
  heatNo?: string;
  standard: string;
  size: string;
  weight: string;
  dateOfIssue?: string | Date | undefined;
}

const MillSheetEdit = () => {
  useBreadcrumb({
    pageTitle: PAGE_TITLES.MILL_SHEET_EDIT,
    items: [
      { id: "millsheet", title: PAGE_TITLES.MILL_SHEET },
      { id: "millsheet-list", title: PAGE_TITLES.MILL_SHEET_EDIT },
    ],
  });
  const { t } = useTranslation("common");
  const userProfile = useSelector(selectUserProfile);
  const isCustomer =
    !!userProfile && userProfile.organizationGroupType === ORG_TYPES.EXTERNAL;
  const isMOAdmin = !!userProfile && userProfile.roleCode === ROLE_CODE.M_ADMIN;
  // Lookup lists
  const customerList = useSelector(getCustomerList);
  const [supplierList, setSupplierList] = useState<FieldOption[]>([]);
  useEffect(() => {
    MillSheetService.getSuplierList().then((suppliers) => {
      setSupplierList(
        suppliers.map((s) => ({ label: s.supplierName, value: s.supplierId }))
      );
    });
  }, []);

  // Table & search states
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_PAGE_SIZE);
  const [total, setTotal] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<MillSheetEdit[]>([]);
  const [selectedIds, setSelectedIds] = useState<(string | number)[]>([]);
  const [searchParams, setSearchParams] = useState<MillSheetSearchParams>(
    INITIAL_SEARCH_PARAMS
  );
  const [sortModel, setSortModel] = useState<GridSortModel>([]);
  const [dataRow, setDataRow] = useState<Partial<MillSheetRecord>>({});
  const [dialogEdit, setOpenDialogEdit] = useState(false);
  const [highlightedRowIds, setHighlightedRowIds] = useState<
    (number | string)[]
  >([]);
  // Derived
  const checkedRows = useMemo(
    () => data.filter((r) => selectedIds.includes(r.id)),
    [data, selectedIds]
  );
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [loadingClear, setLoadingClear] = useState(false);
  const isSorting = useRef(false);
  const isPageChanging = useRef(false);

  // file_id param logic
  const router = useRouter();
  const { fileIdParam, fileIdArray, setFileIdArray } = useMillSheetQueryIds();

  // Dialog
  const [openDialog, setOpenDialog] = useState(false);

  // Search field config
  const fieldsSearch = useMemo<FieldConfig[]>(() => {
    const full: FieldConfig[] = [
      {
        name: "mill",
        label: "Mill",
        placeholder: "Enter Mill",
        hiddenLabelNormal: true,
        type: "autocomplete",
        options: supplierList,
        maxLength: 255,
      },
      {
        name: "customerId",
        label: "Customer",
        placeholder: "Enter Customer",
        hiddenLabelNormal: true,
        type: "autocomplete",
        options: customerList.map((c) => ({
          label: c.customerName,
          value: c.customerId,
        })),
        maxLength: 255,
      },
      {
        name: "invoiceNo",
        label: "Invoice No.",
        placeholder: "Enter Invoice Number",
        hiddenLabelNormal: true,
        type: "text",
        maxLength: 50,
      },
      {
        name: "poNo",
        label: "PO No.",
        placeholder: "Enter PO Number",
        hiddenLabelNormal: true,
        type: "text",
        maxLength: 50,
      },
      {
        name: "serialNumber",
        label: "Serial Number",
        placeholder: "Enter Serial Number",
        hiddenLabelNormal: true,
        type: "text",
        labelTooltipContent:
          "The serial number may consist of Coil No., Product ID, Bundle No., or other types of identifiers.",
        labelTooltipProps: { placement: "right", enterDelay: 200 },
        labelTooltipIcon: (
          <Image
            src="../images/millsheet/information.svg"
            alt="information"
            width={16}
            height={16}
          />
        ),
      },
      {
        name: "heatNo",
        label: "Heat No.",
        placeholder: "Enter Heat Number",
        hiddenLabelNormal: true,
        type: "text",
        maxLength: 50,
      },
      {
        name: "standard",
        label: "Standard",
        placeholder: "Enter Standard",
        hiddenLabelNormal: true,
        type: "text",
      },
      {
        name: "size",
        label: "Size",
        placeholder: "Enter Size",
        hiddenLabelNormal: true,
        type: "text",
      },
      {
        name: "weight",
        label: "Weight",
        placeholder: "Enter Weight",
        hiddenLabelNormal: true,
        type: "textRange",
        isNumberic: true,
        allowDecimal: true,
        maxIntegerDigits: 7,
        maxDecimalDigits: 3,
        customValidation: (value) => {
          if (value) {
            const { from, to } = value;
            if (parseFloat(from) > parseFloat(to)) {
              return "From value cannot be greater than the To value";
            }
          }
          return true;
        },
      },
      {
        name: "issueDate",
        label: "Issue Date",
        placeholder: "Enter Issue Date",
        hiddenLabelNormal: true,
        type: "dateRange",
      },
      {
        name: "status",
        label: "Status",
        type: "select",
        placeholder: "Select Status",
        hiddenLabelNormal: true,
        options: [
          { label: "No ERP", value: 2 },
          { label: "Error", value: 3 },
          { label: "Imported", value: 4 },
        ],
      },
    ];
    return isCustomer ? full.filter((f) => f.name !== "customerId") : full;
  }, [isCustomer, customerList, supplierList]);

  const Center = ({ children }: { children: React.ReactNode }) => (
    <Box
      sx={{
        width: "100%",
        height: "100%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      {children}
    </Box>
  );

  const columns = useMemo<DynamicDataGridColumn<MillSheetEdit>[]>(() => {
    const base: DynamicDataGridColumn<MillSheetEdit>[] = [
      {
        key: "mill",
        label: "Mill",
        headerAlign: "center",
        align: "left",
        flex: 1,
      },
      {
        key: "customer",
        label: "Customer",
        headerAlign: "center",
        align: "left",
        flex: 1,
      },
      {
        key: "invoiceNo",
        label: "Invoice No.",
        headerAlign: "center",
        align: "left",
        flex: 0.9,
      },
      {
        key: "poNo",
        label: "PO No.",
        headerAlign: "center",
        align: "left",
        flex: 0.8,
      },
      {
        key: "serialNumber",
        label: "Serial Number",
        headerAlign: "center",
        align: "left",
        flex: 0.9,
      },
      {
        key: "heatNo",
        label: "Heat No.",
        headerAlign: "center",
        align: "left",
        flex: 0.8,
      },
      {
        key: "standard",
        label: "Standard",
        headerAlign: "center",
        align: "left",
        flex: 1,
      },
      {
        key: "size",
        label: "Size",
        headerAlign: "center",
        align: "right",
        flex: 0.8,
      },
      {
        key: "weight",
        label: "Weight (kg)",
        headerAlign: "center",
        align: "right",
        flex: 0.8,
      },
      {
        key: "dateOfIssue",
        label: "Date Of Issue",
        headerAlign: "center",
        align: "left",
        flex: 0.95,
      },
      {
        key: "status",
        label: "Status",
        headerAlign: "center",
        align: "left",
        flex: 0.8,
        render: (row) => (
          <Typography
            sx={{
              textAlign: "center",
              width: "72px",
              display: "inline-block",
              borderRadius: "4px",
              fontSize: "12px",
              color:
                row.status === MILLSHEET_STATUS.ERROR
                  ? "rgba(255, 61, 0, 1)"
                  : row.status === MILLSHEET_STATUS.IMPORTED
                  ? "rgba(76, 175, 80, 1)"
                  : "rgba(255, 61, 0, 1)",
              backgroundColor:
                row.status === MILLSHEET_STATUS.ERROR
                  ? "rgba(202, 0, 46, 0.2)"
                  : row.status === MILLSHEET_STATUS.IMPORTED
                  ? "rgba(63, 194, 138, 0.2)"
                  : "rgba(202, 0, 46, 0.2)",
            }}
          >
            {row.status === MILLSHEET_STATUS.ERROR
              ? "Error"
              : row.status === MILLSHEET_STATUS.IMPORTED
              ? "Imported"
              : "No ERP"}
          </Typography>
        ),
      },
      {
        key: "editedAt",
        label: "Last Edited",
        headerAlign: "center",
        align: "left",
        flex: 1,
      },
    ];
    return isCustomer ? base.filter((c) => c.key !== "customer") : base;
  }, [isCustomer]);

  const buildCondition = useCallback((form: any): ConditionSearch => {
    const { issueDate, weight, ...rest } = form || {};
    const [fromIssueDate, toIssueDate] = issueDate || [];
    return {
      ...INITIAL_CONDITION,
      ...rest,
      fromWeight: weight?.from,
      toWeight: weight?.to,
      fromIssueDate: fromIssueDate
        ? dayjs(fromIssueDate).format("DD/MM/YYYY")
        : "",
      toIssueDate: toIssueDate ? dayjs(toIssueDate).format("DD/MM/YYYY") : "",
    };
  }, []);

  function isStartAfterEnd(startStr: string, endStr: string): boolean {
    const format = "DD/MM/YYYY";
    const start = dayjs(startStr, format, true);
    const end = dayjs(endStr, format, true);
    if (!start.isValid() || !end.isValid()) return false;
    return start.isAfter(end, "day");
  }

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const { fromIssueDate, toIssueDate, status } =
        searchParams.condition as ConditionSearch;
      const isInvalidDate =
        fromIssueDate &&
        toIssueDate &&
        isStartAfterEnd(fromIssueDate, toIssueDate);
      // Truyền thẳng fileIdArray vào fileId, không cần convert array
      const res = await MillSheetService.getMillSheetList({
        ...searchParams,
        condition: {
          ...searchParams.condition,
          fileId: fileIdArray,
          fromIssueDate: isInvalidDate ? "" : fromIssueDate,
          toIssueDate: isInvalidDate ? "" : toIssueDate,
          status: Number(status) || undefined,
        },
        pagination: { pageNumber: page, pageSize },
      });
      setData(res.items as MillSheetEdit[]);
      setTotal(res.totalItem);
    } catch {
      setData([]);
      setTotal(0);
      setLoadingSearch(false);
      setLoadingClear(false);
      setLoading(false);
    } finally {
      setLoading(false);
      setLoadingClear(false);
      setLoadingSearch(false);
      isSorting.current = false;
      isPageChanging.current = false;
    }
  }, [page, pageSize, searchParams, fileIdArray]);

  useEffect(() => {
    if (fileIdParam) {
      if (fileIdArray !== fileIdParam) {
        setFileIdArray(fileIdParam);
      }
      setSearchParams((prev) => ({
        ...prev,
        condition: {
          ...prev.condition,
        },
      }));
      setPage(1);
      setSelectedIds([]);
      setHighlightedRowIds([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fileIdParam]);

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams, page, pageSize]);

  const handleSearch = useCallback(
    (form: any) => {
      setLoadingClear(false);
      setLoadingSearch(true);
      setPage(1);
      setSelectedIds([]);
      setHighlightedRowIds([]);
      setSearchParams((prev) => ({
        ...prev,
        condition: buildCondition(form),
      }));
    },
    [buildCondition]
  );

  const handleClear = useCallback(() => {
    if (fileIdParam) {
      const url = new URL(window.location.href);
      url.searchParams.delete("file_id");
      router.replace(url.pathname + url.search);
      setFileIdArray(undefined);
    }
    setLoadingSearch(false);
    setLoadingClear(true);
    setSortModel([]);
    setSearchParams(() => JSON.parse(JSON.stringify(INITIAL_SEARCH_PARAMS)));
    setPage(1);
    setSelectedIds([]);
  }, [fileIdParam, router, setFileIdArray]);

  const handlePageChange = useCallback((newPage: number) => {
    if (isPageChanging.current) return;
    setPage((prev) => {
      if (prev !== newPage) {
        setSelectedIds([]);
        setHighlightedRowIds([]);
        isPageChanging.current = true;
        return newPage;
      }
      return prev;
    });
  }, []);
  const handlePageSizeChange = useCallback((newSize: number) => {
    setPageSize(newSize);
    setPage(1);
    setSelectedIds([]);
    setHighlightedRowIds([]);
  }, []);

  const handleSortModelChange = useCallback((model: GridSortModel) => {
    if (isSorting.current) {
      return;
    }
    isSorting.current = true;
    setSortModel(model);
    setPage(1);
    setSelectedIds([]);
    setHighlightedRowIds([]);
    if (model.length === 0) {
      setSearchParams((prev) => ({ ...prev, sort: [] }));
      return;
    }
    const sort: Sort[] = model.map(({ field, sort }) => ({
      sortColumn: MappingColumnSort[field as keyof typeof MappingColumnSort],
      sortDirection: (sort ?? "asc") as "asc" | "desc",
    }));
    setSearchParams((prev) => ({ ...prev, sort }));
  }, []);

  const { showSnackbar } = useSnackbar();
  const handleCloseDialog = useCallback(
    async (action: "cancel" | "submit") => {
      if (action === "cancel") {
        setOpenDialog(false);
        return;
      }
      try {
        const payload = checkedRows.map((r) => ({
          id: r.id,
          status: r.status,
        }));
        if (payload.length === 0) {
          setOpenDialog(false);
          return;
        }
        await MillSheetService.deleteMillSheetByIds(payload);
        await fetchData();
        setSelectedIds([]);
        setHighlightedRowIds([]);
      } finally {
        setOpenDialog(false);
        showSnackbar(
          "Delete Successful!",
          "The mill sheet(s) have been successfully deleted.",
          "success"
        );
      }
    },
    [checkedRows, fetchData, showSnackbar]
  );

  const handleRowClick = (row: MillSheetRecord) => {
    setDataRow(row);
    setOpenDialogEdit(true);
  };

  const handleCloseEdit = () => {
    setOpenDialogEdit(false);
    setDataRow({});
  };

  const handleEditSuccess = (row: MillSheetRecord, idNew: number) => {
    const now = dayjs(new Date()).format("DD/MM/YYYY");
    setData((prevData) =>
      prevData.map((item) =>
        item.id === row.id
          ? ({
              ...item,
              ...row,
              id: idNew,
              editedAt: now,
              status: STATUS_EDIT,
            } as MillSheetEdit)
          : item
      )
    );
    setHighlightedRowIds((prev) => Array.from(new Set([...prev, idNew])));
  };

  return (
    <PageContainer
      title={PAGE_TITLES.MILL_SHEET_EDIT}
      description={"this is " + PAGE_TITLES.MILL_SHEET_EDIT}
    >
      <Box
        sx={{
          ".form-search-control": {
            marginBottom: "10px !important",
          },
        }}
      >
        <DynamicSearch
          loadingSearch={loadingSearch}
          loadingClear={loadingClear}
          fields={fieldsSearch}
          onSubmit={handleSearch}
          onClear={handleClear}
          searchLabel={t("search")}
          clearLabel={t("clear Filter")}
          advancedLabel={"Hide Advanced Filters"}
          infoContent={
            'Enter multiple values separated by "," to perform a multi-condition search.'
          }
        />
      </Box>
      {!isMOAdmin && <Box sx={{ height: 24 }} />}
      {isMOAdmin && (
        <Button
          sx={{
            my: 1.25,
          }}
          prefixIcon={
            <Image
              src={
                selectedIds.length === 0
                  ? "../images/millsheet/trash-disabled.svg"
                  : "../images/millsheet/trash.svg"
              }
              alt="edit"
              width={18}
              height={18}
            />
          }
          variant="outlined"
          size="small"
          disabled={selectedIds.length === 0}
          onClick={() => setOpenDialog(true)}
        >
          Delete
        </Button>
      )}
      <DynamicDataGrid<MillSheetEdit>
        columns={columns}
        data={data}
        page={page}
        pageSize={pageSize}
        total={total}
        itemLabel={"results"}
        loading={loading}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        showCheckbox={data.length > 0}
        rowKey="id"
        onRowClick={handleRowClick}
        showPointer={true}
        onSelectRow={(ids) => setSelectedIds(ids)}
        resultPerPageOptions={[10, 20, 50, 100]}
        selectedRows={selectedIds}
        onSortModelChange={handleSortModelChange}
        sortModel={sortModel}
        getRowClassName={(params) =>
          highlightedRowIds.includes(params.id) ? "highlighted-row" : ""
        }
      />
      <PopupModal
        open={openDialog}
        onClose={() => handleCloseDialog("cancel")}
        onSubmit={() => handleCloseDialog("submit")}
        hideCloseButton
        type="warning"
        title="Confirm Delete"
        description={
          checkedRows.length > 1
            ? "Are you sure you want to delete these mill sheets?"
            : "Are you sure you want to delete this mill sheet?"
        }
      ></PopupModal>
      {/* When clicking on a row, show the edit popup. */}
      <MillSheetEditPopup
        open={dialogEdit}
        onClose={handleCloseEdit}
        data={dataRow as MillSheetRecord}
        handleEditSuccess={handleEditSuccess}
      />
    </PageContainer>
  );
};

export default MillSheetEdit;
