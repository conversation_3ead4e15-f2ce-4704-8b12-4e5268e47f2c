"use client";
import { Typography } from "@mui/material";
import PageContainer from "@/app/(DashboardLayout)/components/container/PageContainer";
import DashboardCard from "@/app/(DashboardLayout)/components/shared/DashboardCard";
// Thêm import useTranslation
import { useTranslation } from "react-i18next";
import NotificationApp from "@/app/(DashboardLayout)/layout/header/badge";
import { useBreadcrumb } from "@/features/breadcrumb/hook/useBreadcrumb";
const MillsheetList = () => {
  useBreadcrumb({
    pageTitle: "Mill Sheet",
    items: [
      { id: "millsheet", title: "Mill Sheet" },
      { id: "millsheet-modification", title: "Mill Sheet Modification" },
    ],
  });
  const { t } = useTranslation("common"); // 'common' là namespace

  return (
    <PageContainer title="Sample Page" description="this is Sample page">
      <NotificationApp />
    </PageContainer>
  );
};

export default MillsheetList;
