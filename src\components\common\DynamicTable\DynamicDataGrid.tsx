import * as React from "react";
import {
  DataGrid,
  GridCallbackDetails,
  GridCellParams,
  GridColDef,
  GridColumnHeaderParams,
  GridRenderCellParams,
  GridRowClassNameParams,
  GridRowSelectionModel,
  GridSortModel,
} from "@mui/x-data-grid";
import {
  Box,
  Typography,
  Select,
  MenuItem,
  Pagination,
  PaginationItem,
  Checkbox,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import KeyboardDoubleArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import SearchIcon from "@mui/icons-material/Search";

export interface DynamicDataGridColumn<TData = any> {
  key: string;
  label: string;
  align?: "left" | "center" | "right";
  headerAlign?: "left" | "center" | "right";
  render?: (row: TData) => React.ReactNode;
  width?: number;
  flex?: number;
  sortable?: boolean;
}

export interface DynamicDataGridProps<TData = any> {
  columns: DynamicDataGridColumn<TData>[];
  data: TData[];
  page?: number;
  pageSize?: number;
  total?: number;
  itemLabel: string;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (size: number) => void;
  showCheckbox?: boolean;
  loading?: boolean;
  showPointer?: boolean;
  loadingRedirect?: boolean;
  selectedRows?: (string | number)[];
  onSelectRow?: (rowIds: (string | number)[]) => void;
  rowKey?: string;
  renderActions?: (row: any) => React.ReactNode;
  onRowClick?: (row: any) => void;
  noRowsText?: string;
  sortModel?: GridSortModel;
  onSortModelChange?: (
    sortModel: import("@mui/x-data-grid").GridSortModel
  ) => void;
  resultPerPageOptions?: number[];
  getRowClassName?: (params: GridRowClassNameParams) => string;
}

const DynamicDataGrid = <TData extends Record<string, any> = any>({
  columns,
  data,
  page = 1,
  pageSize = 10,
  total = 0,
  itemLabel = "results",
  onPageChange,
  onPageSizeChange,
  showCheckbox = false,
  loading = false,
  loadingRedirect = false,
  selectedRows,
  onSelectRow,
  rowKey = "id",
  showPointer = false,
  renderActions,
  onRowClick,
  noRowsText = "No Results Found",
  onSortModelChange,
  sortModel,
  resultPerPageOptions,
  getRowClassName,
}: DynamicDataGridProps<TData>) => {
  // DataGrid uses 0-based page index internally (not used here, but for future devs)
  // const gridPage = Math.max(0, page - 1);

  // Map columns to DataGrid columns, add actions column at the end
  const gridColumns: GridColDef[] = React.useMemo(() => {
    const mapped: GridColDef[] = columns.map((col) => {
      const colDef: GridColDef = {
        field: col.key,
        headerName: col.label,
        align: col.align,
        headerAlign: col.headerAlign,
        sortable: col.sortable ?? true,
        renderCell: col.render
          ? (params: GridRenderCellParams) =>
              col.render && col.render(params.row)
          : undefined,
        disableColumnMenu: true,
      };

      if (typeof col.width === "number") {
        colDef.width = col.width;
      } else if (typeof col.flex === "number") {
        colDef.flex = col.flex;
      } else {
        colDef.flex = 1;
      }

      return colDef;
    });

    if (renderActions) {
      mapped.push({
        field: "__actions__",
        headerName: "",
        width: 60,
        sortable: false,
        filterable: false,
        align: "center",
        headerAlign: "center",
        renderCell: (params: GridRenderCellParams) => (
          <span
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              width: "100%",
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {renderActions(params.row)}
          </span>
        ),
        disableColumnMenu: true,
      });
    }

    return mapped;
  }, [columns, renderActions]);

  // DataGrid rows must have id
  const rows = data.map((row) => ({ ...row, id: row[rowKey] }));
  // Handle row selection (checkbox)
  const handleRowSelection = (
    selection: GridRowSelectionModel,
    detail: GridCallbackDetails
  ) => {
    const { type, ids } = selection;
    let selectedIds = [];
    if (type === "include") {
      selectedIds = Array.from(ids);
    } else {
      const allRows = detail.api.getAllRowIds();
      selectedIds = allRows.filter((id) => !ids.has(id));
    }
    setRowSelectionModel(selection);
    onSelectRow?.(selectedIds);
  };

  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const [headerHeight, setHeaderHeight] = React.useState(40);

  React.useLayoutEffect(() => {
    if (!containerRef.current) return;
    const headers = containerRef.current.querySelectorAll(
      ".MuiDataGrid-columnHeader"
    );
    let max = 40;
    headers.forEach((el) => {
      const title = el.querySelector(
        ".MuiDataGrid-columnHeaderTitle"
      ) as HTMLElement | null;
      if (title) {
        const contentHeight = title.offsetHeight;
        const total = contentHeight + 10;
        if (total > max) max = total;
      }
    });
    if (max !== headerHeight) setHeaderHeight(max);
  }, [columns, data, headerHeight]);
  // Handle page size change
  const handlePageSizeChange = (newSize: number) => {
    onPageSizeChange?.(newSize);
    onPageChange?.(1); // Reset to page 1
  };

  // Summary text for pagination
  const start = total === 0 ? 0 : (page - 1) * pageSize + 1;
  const end = Math.min(page * pageSize, total);
  const [rowSelectionModel, setRowSelectionModel] =
    React.useState<GridRowSelectionModel>({ type: "include", ids: new Set() });

  let isCheckboxClick = false;

  const handleCellClick = (params: GridCellParams, event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    isCheckboxClick = !!target.closest('input[type="checkbox"]');
  };

  // Detect checkbox click in header (select all checkbox)
  const handleColumnHeaderClick = (
    params: GridColumnHeaderParams,
    event: React.MouseEvent
  ) => {
    const target = event.target as HTMLElement;
    isCheckboxClick = !!target.closest('input[type="checkbox"]');
  };

  // Update table selection when selectedRows changes
  React.useEffect(() => {
    setRowSelectionModel({ type: "include", ids: new Set(selectedRows ?? []) });
  }, [selectedRows]);

  return (
    <Box>
      <Box
        ref={containerRef}
        sx={{
          height: "442px",
          width: "100%",
          borderRadius: "8px",
          border: "1px solid #E0E3E7",
          background: "#fff",
          boxShadow: "none",
        }}
      >
        <DataGrid
          rowHeight={40}
          columnHeaderHeight={headerHeight}
          getRowHeight={() => "auto"}
          rows={rows}
          columns={gridColumns}
          checkboxSelection={showCheckbox}
          disableRowSelectionOnClick={!onRowClick}
          onRowSelectionModelChange={(selection, detail) => {
            if (isCheckboxClick) {
              handleRowSelection(selection, detail);
            }
          }}
          onColumnHeaderClick={handleColumnHeaderClick}
          onCellClick={handleCellClick}
          rowSelectionModel={rowSelectionModel}
          rowCount={total}
          pagination
          paginationMode="server"
          onRowClick={
            onRowClick ? (params) => onRowClick(params.row) : undefined
          }
          getRowId={(row) => row[rowKey]}
          disableColumnResize
          loading={loading || loadingRedirect}
          slotProps={{
            loadingOverlay: {
              variant: loadingRedirect ? "linear-progress" : "skeleton",
              noRowsVariant: "skeleton",
            },
          }}
          sortingMode="server"
          getRowClassName={getRowClassName}
          sx={{
            border: "none",
            borderRadius: "8px",
            "& .MuiDataGrid-columnHeaderCheckbox, & .MuiDataGrid-cellCheckbox":
              {
                width: 36,
                minWidth: 36,
                maxWidth: 36,
                padding: 2,
              },
            "& .MuiDataGrid-columnHeaderCheckbox .MuiButtonBase-root, & .MuiDataGrid-cellCheckbox .MuiButtonBase-root":
              {
                width: 20,
                height: 20,
                margin: 0,
                padding: 2,
              },
            "& .MuiDataGrid-columnHeaderCheckbox .MuiDataGrid-columnSeparator":
              { display: "none" },
            "& .MuiDataGrid-columnHeaderCheckbox .MuiDataGrid-columnHeaderDraggableContainer":
              {
                justifyContent: "center",
              },
            // Header
            "& .MuiDataGrid-columnHeader": {
              background: "#ECEFF4",
              color: "#212B36",
              fontSize: "14px",
              minHeight: "40px",
              borderRight: "1px solid #E0E3E7",
              whiteSpace: "normal",
              overflowWrap: "normal",
              wordBreak: "keep-all",
              lineHeight: 1.3,
              display: "flex",
              alignItems: "flex-start",
              padding: 0,
            },
            "& .MuiDataGrid-columnHeader--last": { borderRight: "none" },
            "& .MuiDataGrid-columnHeaders .MuiDataGrid-scrollbarFiller": {
              backgroundColor: "#ECEFF4 !important",
            },
            "& .MuiDataGrid-columnHeaderTitle": {
              fontWeight: "bold",
              whiteSpace: "normal",
              overflowWrap: "normal",
              wordBreak: "keep-all",
              textOverflow: "clip",
              lineHeight: 1.3,
              display: "block",
              textAlign: "center",
            },

            // Base row
            "& .MuiDataGrid-row": {
              backgroundColor: "#fff",
              minHeight: "40px",
            },
            "& .highlighted-row": {
              backgroundColor: "#E3F2FD !important", // highlighted-row when edit row
            },

            // Zebra chỉ áp dụng khi KHÔNG selected
            "& .MuiDataGrid-row:nth-of-type(even):not(.Mui-selected)": {
              backgroundColor: "#F5F6FA",
            },
            "& .MuiDataGrid-row:hover": {
              cursor: showPointer ? "pointer" : "default",
            },

            // Hover chỉ áp dụng khi KHÔNG selected
            "& .MuiDataGrid-row:hover:not(.Mui-selected)": {
              backgroundColor: "#E1E4EA",
              cursor: showPointer ? "pointer" : "default",
            },

            // Highlight khi selected
            "& .MuiDataGrid-row.Mui-selected": {
              backgroundColor: "#FDE8EB !important",
            },
            "& .MuiDataGrid-row.Mui-selected:hover": {
              backgroundColor: "#FDE8EB !important",
            },
            "& .MuiDataGrid-columnHeaders .MuiDataGrid-filler": {
              backgroundColor: "#ECEFF4 !important",
            },

            // Bỏ overlay màu xanh ở từng cell khi selected để dùng nền của row
            "& .MuiDataGrid-cell--selected": {
              backgroundColor: "inherit !important",
            },

            // Cell
            "& .MuiDataGrid-cell": {
              fontSize: 14,
              color: "#212B36",
              py: 0.25,
              borderRight: "none",
              display: "flex",
              alignItems: "center",
              whiteSpace: "normal !important",
              overflowWrap: "break-word",
              wordBreak: "break-word",
              lineHeight: 1.4,
            },

            "& .MuiDataGrid-cellContent": {
              whiteSpace: "inherit",
              overflow: "visible",
              textOverflow: "clip",
              display: "block",
              minWidth: 0,
            },

            // Footer (ẩn mặc định)
            "& .MuiDataGrid-footerContainer": {
              borderTop: "none",
              minHeight: 56,
              px: 2,
            },

            // Ẩn icon separator
            "& .MuiDataGrid-iconSeparator": { display: "none !important" },

            // Bỏ outline khi focus
            ".MuiDataGrid-cell:focus, .MuiDataGrid-columnHeader:focus, .MuiDataGrid-cell:focus-within, .MuiDataGrid-columnHeader:focus-within":
              {
                outline: "none !important",
              },
          }}
          localeText={{
            noRowsLabel: noRowsText,
            footerRowSelected: (count) => `${count} selected`,
          }}
          slots={{
            pagination: () => null, // Hide default pagination
            footer: () => null, // Hide footer table
            noRowsOverlay: () => (
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "100%",
                  color: "#9ca3af",
                  fontSize: 18,
                  fontWeight: 500,
                  letterSpacing: 0.2,
                  background: "transparent",
                  p: 2,
                }}
              >
                <SearchIcon sx={{ fontSize: 48, mb: 1, color: "#A3A8B1" }} />
                <Box component="span" sx={{ fontSize: 20, mt: 2 }}>
                  {noRowsText}
                </Box>
              </Box>
            ),
          }}
          onSortModelChange={
            onSortModelChange ? (model) => onSortModelChange(model) : undefined
          }
          sortModel={sortModel}
        />
      </Box>
      {/* Custom pagination UI giống DynamicTable */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          py: "7px",
          px: "10px",
          background: "#fff",
          borderRadius: "12px",
          m: "14px 10px 0px 10px",
        }}
      >
        {/* Left: summary text + page size selector */}
        <Box display="flex" alignItems="center" gap={3}>
          {/* Summary text */}
          <Typography fontSize={14} color="text.primary">
            {`Showing ${start}-${end} of ${total} ${itemLabel}`}
          </Typography>
          {/* Page size selector */}
          <Box display="flex" alignItems="center" gap={1}>
            <Typography fontSize={14} color="text.primary">
              Results per page
            </Typography>
            <Select
              variant="outlined"
              IconComponent={ExpandMoreIcon}
              aria-label="Chọn số dòng trên mỗi trang"
              title="Chọn số dòng trên mỗi trang"
              value={pageSize}
              onChange={(e) => handlePageSizeChange(Number(e.target.value))}
              size="small"
              sx={{ border: "none", height: "36px" }}
              renderValue={(value) => `${value}`}
            >
              {resultPerPageOptions?.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </Box>
        </Box>
        {/* Pagination controls giống DynamicTable */}
        <Pagination
          count={Math.max(1, Math.ceil(total / pageSize))}
          page={page}
          onChange={(_, value) => onPageChange && onPageChange(value)}
          color="primary"
          shape="rounded"
          size="medium"
          showFirstButton
          showLastButton
          siblingCount={1}
          boundaryCount={1}
          renderItem={(item) => (
            <PaginationItem
              slots={{
                first: KeyboardDoubleArrowLeftIcon,
                last: KeyboardDoubleArrowRightIcon,
              }}
              {...item}
            />
          )}
          sx={{
            ml: "auto",
            "& .MuiPagination-ul": { gap: 1 },
            "& .MuiPaginationItem-root": {
              borderRadius: "6px",
              border: "1px solid #E0E3E7",
              minWidth: "32px",
              height: "32px",
              fontSize: 14,
              boxSizing: "border-box",
            },
          }}
        />
      </Box>
    </Box>
  );
};

export default DynamicDataGrid;
