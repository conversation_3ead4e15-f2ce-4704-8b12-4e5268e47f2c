import * as React from "react";
import {
  DataGrid,
  GridCallbackDetails,
  GridColDef,
  GridRenderCellParams,
  GridRowSelectionModel,
  GridSortModel,
} from "@mui/x-data-grid";
import { Box, Typography, Select, MenuItem, Pagination, PaginationItem, Checkbox } from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import KeyboardDoubleArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import SearchIcon from "@mui/icons-material/Search";

export interface DynamicDataGridColumn<TData = any> {
  key: string;
  label: string;
  align?: "left" | "center" | "right";
  headerAlign?: "left" | "center" | "right";
  render?: (row: TData) => React.ReactNode;
  width?: number;
  sortable?: boolean;
}

export interface DynamicDataGridProps<TData = any> {
  columns: DynamicDataGridColumn<TData>[];
  data: TData[];
  page?: number;
  pageSize?: number;
  total?: number;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (size: number) => void;
  showCheckbox?: boolean;
  loading?: boolean;
  selectedRows?: (string | number)[];
  onSelectRow?: (rowIds: (string | number)[]) => void;
  rowKey?: string;
  renderActions?: (row: any) => React.ReactNode;
  onRowClick?: (row: any) => void;
  noRowsText?: string;
  sortModel?: GridSortModel;
  onSortModelChange?: (sortModel: import("@mui/x-data-grid").GridSortModel) => void;
  resultPerPageOptions?: number[];
}

const DynamicDataGrid = <TData extends Record<string, any> = any>({
  columns,
  data,
  page = 1,
  pageSize = 10,
  total = 0,
  onPageChange,
  onPageSizeChange,
  showCheckbox = false,
  loading = false,
  selectedRows,
  onSelectRow,
  rowKey = "id",
  renderActions,
  onRowClick,
  noRowsText = "No Results Found",
  onSortModelChange,
  sortModel,
  resultPerPageOptions,
}: DynamicDataGridProps<TData>) => {
  // DataGrid uses 0-based page index internally (not used here, but for future devs)
  // const gridPage = Math.max(0, page - 1);

  // Map columns to DataGrid columns, add actions column at the end
  const gridColumns: GridColDef[] = React.useMemo(() => {
    const mapped: GridColDef[] = columns.map((col) => {
      const colDef: GridColDef = {
        flex: 1,
        field: col.key,
        headerName: col.label,
        align: col.align,
        headerAlign: col.headerAlign,
        sortable: col.sortable ?? true,
        renderCell: col.render ? (params: GridRenderCellParams) => col.render && col.render(params.row) : undefined,
        disableColumnMenu: true,
      };

      // if (typeof col.width === "number") {
      //   colDef.width = col.width;
      // } else {
      //   colDef.minWidth = 150;
      // }

      return colDef;
    });

    if (renderActions) {
      mapped.push({
        field: "__actions__",
        headerName: "",
        width: 60,
        sortable: false,
        filterable: false,
        align: "center",
        headerAlign: "center",
        renderCell: (params: GridRenderCellParams) => (
          <span
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              width: "100%",
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {renderActions(params.row)}
          </span>
        ),
        disableColumnMenu: true,
      });
    }

    return mapped;
  }, [columns, renderActions]);
  // DataGrid rows must have id
  const rows = data.map((row) => ({ ...row, id: row[rowKey] }));
  // Handle row selection (checkbox)
  const handleRowSelection = (selection: GridRowSelectionModel, x: GridCallbackDetails): any => {
    const ids = Array.isArray(selection) ? (selection as string[]) : (selection as any)?.ids ?? [];
    if (x.reason === "multipleRowsSelection") {
      const allRows = x.api.getAllRowIds();
      if (selectedRows?.length !== allRows.length) {
        onSelectRow?.(allRows);
      } else {
        return onSelectRow?.([]);
      }
      return;
    }
    onSelectRow?.(ids as (string | number)[]);
  };

  // Handle page size change
  const handlePageSizeChange = (newSize: number) => {
    onPageSizeChange?.(newSize);
    onPageChange?.(1); // Reset to page 1
  };

  // Summary text for pagination
  const start = total === 0 ? 0 : (page - 1) * pageSize + 1;
  const end = Math.min(page * pageSize, total);

  const NoIndeterminateCheckbox = (props: any) => {
    const { indeterminate, ...rest } = props;
    return <Checkbox indeterminate={false} {...rest} />;
  };

  return (
    <Box>
      <Box
        sx={{
          height: "442px",
          width: "100%",
          borderRadius: "8px",
          border: "1px solid #E0E3E7",
          background: "#fff",
          boxShadow: "none",
        }}
      >
        <DataGrid
          rowHeight={40}
          columnHeaderHeight={40}
          rows={rows}
          columns={gridColumns}
          checkboxSelection={showCheckbox}
          disableRowSelectionOnClick={!onRowClick}
          onRowSelectionModelChange={(e, x) => handleRowSelection(e, x)}
          rowCount={total}
          pagination
          paginationMode="server"
          onRowClick={onRowClick ? (params) => onRowClick(params.row) : undefined}
          getRowId={(row) => row[rowKey]}
          disableColumnResize
          loading={loading}
          slotProps={{
            loadingOverlay: {
              variant: "skeleton",
              noRowsVariant: "skeleton",
            },
          }}
          sx={{
            border: "none",
            borderRadius: "8px",
            // Header
            "& .MuiDataGrid-columnHeader": {
              background: "#ECEFF4",
              color: "#212B36",
              fontSize: "14px",
              minHeight: "40px",
              height: "40px",
              borderRight: "1px solid #E0E3E7",
            },
            "& .MuiDataGrid-columnHeader--last": { borderRight: "none" },
            "& .MuiDataGrid-scrollbarFiller--header": { backgroundColor: "#ECEFF4 !important" },
            "& .MuiDataGrid-columnHeaderTitle": {
              fontWeight: "bold",
              borderBottom: "None",
            },

            // Base row
            "& .MuiDataGrid-row": {
              backgroundColor: "#fff",
              minHeight: "40px",
              height: "40px",
            },

            // Zebra chỉ áp dụng khi KHÔNG selected
            "& .MuiDataGrid-row:nth-of-type(even):not(.Mui-selected)": {
              backgroundColor: "#F5F6FA",
            },

            // Hover chỉ áp dụng khi KHÔNG selected
            "& .MuiDataGrid-row:hover:not(.Mui-selected)": {
              backgroundColor: "#E1E4EA",
              cursor: "pointer",
            },

            // Highlight khi selected
            "& .MuiDataGrid-row.Mui-selected": {
              backgroundColor: "#FDE8EB !important",
            },
            "& .MuiDataGrid-row.Mui-selected:hover": {
              backgroundColor: "#FDE8EB !important",
            },
            "& .MuiDataGrid-columnHeaders .MuiDataGrid-filler": {
              backgroundColor: "#ECEFF4 !important",
            },

            // Bỏ overlay màu xanh ở từng cell khi selected để dùng nền của row
            "& .MuiDataGrid-cell--selected": {
              backgroundColor: "inherit !important",
            },

            // Cell
            "& .MuiDataGrid-cell": {
              fontSize: 14,
              color: "#212B36",
              py: 0,
              px: 2,
              borderRight: "none",
            },

            // Footer (ẩn mặc định)
            "& .MuiDataGrid-footerContainer": {
              borderTop: "none",
              minHeight: 56,
              px: 2,
            },

            // Ẩn icon separator
            "& .MuiDataGrid-iconSeparator": { display: "none !important" },

            // Bỏ outline khi focus
            ".MuiDataGrid-cell:focus, .MuiDataGrid-columnHeader:focus, .MuiDataGrid-cell:focus-within, .MuiDataGrid-columnHeader:focus-within":
              {
                outline: "none !important",
              },
          }}
          localeText={{
            noRowsLabel: noRowsText,
            footerRowSelected: (count) => `${count} selected`,
          }}
          slots={{
            pagination: () => null, // Hide default pagination
            footer: () => null, // Hide footer table
            noRowsOverlay: () => (
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "100%",
                  color: "#9ca3af",
                  fontSize: 18,
                  fontWeight: 500,
                  letterSpacing: 0.2,
                  background: "transparent",
                  p: 2,
                }}
              >
                <SearchIcon sx={{ fontSize: 48, mb: 1, color: "#A3A8B1" }} />
                <Box component="span" sx={{ fontSize: 20, mt: 2 }}>
                  {noRowsText}
                </Box>
              </Box>
            ),
            baseCheckbox: NoIndeterminateCheckbox,
          }}
          onSortModelChange={onSortModelChange ? (model) => onSortModelChange(model) : undefined}
          sortModel={sortModel}
        />
      </Box>
      {/* Custom pagination UI giống DynamicTable */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          py: "7px",
          px: "10px",
          background: "#fff",
          borderRadius: "12px",
          m: "14px 10px 0px 10px",
        }}
      >
        {/* Left: summary text + page size selector */}
        <Box display="flex" alignItems="center" gap={3}>
          {/* Summary text */}
          <Typography fontSize={14} color="text.primary">
            {`Showing ${start}-${end} of ${total} users`}
          </Typography>
          {/* Page size selector */}
          <Box display="flex" alignItems="center" gap={1}>
            <Typography fontSize={14} color="text.primary">
              Results per page
            </Typography>
            <Select
              variant="outlined"
              IconComponent={ExpandMoreIcon}
              aria-label="Chọn số dòng trên mỗi trang"
              title="Chọn số dòng trên mỗi trang"
              value={pageSize}
              onChange={(e) => handlePageSizeChange(Number(e.target.value))}
              size="small"
              sx={{ border: "none", height: "36px" }}
              renderValue={(value) => `${value}`}
            >
              {resultPerPageOptions?.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </Box>
        </Box>
        {/* Pagination controls giống DynamicTable */}
        <Pagination
          count={Math.max(1, Math.ceil(total / pageSize))}
          page={page}
          onChange={(_, value) => onPageChange && onPageChange(value)}
          color="primary"
          shape="rounded"
          size="medium"
          showFirstButton
          showLastButton
          siblingCount={1}
          boundaryCount={1}
          renderItem={(item) => (
            <PaginationItem
              slots={{
                first: KeyboardDoubleArrowLeftIcon,
                last: KeyboardDoubleArrowRightIcon,
              }}
              {...item}
            />
          )}
          sx={{
            ml: "auto",
            "& .MuiPagination-ul": { gap: 1 },
            "& .MuiPaginationItem-root": {
              borderRadius: "6px",
              border: "1px solid #E0E3E7",
              minWidth: "32px",
              height: "32px",
              fontSize: 14,
              boxSizing: "border-box",
            },
          }}
        />
      </Box>
    </Box>
  );
};

export default DynamicDataGrid;
