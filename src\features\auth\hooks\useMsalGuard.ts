import { useMsal } from '@azure/msal-react';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useMemo } from 'react';

export function useMsalGuard() {
  const { instance,accounts, inProgress } = useMsal();
  const router = useRouter();

  const scopes = useMemo(() => [
    'openid',
    '0b135d95-2488-4110-8042-aef3b76766ed/API.Write',
    '0b135d95-2488-4110-8042-aef3b76766ed/API.Read'
  ], []);

  useEffect(() => {
    // Chỉ redirect nếu không đăng nhập và không có interaction đang diễn ra
    if (accounts.length === 0 && inProgress === 'none') {
      router.replace('/authentication/login');
      // instance.loginPopup({ scopes })
    }
  }, [instance, accounts, inProgress, router, scopes]);

  return accounts.length > 0;
}
