// src/features/auth/hooks/useAuth.ts
import { useDispatch, useSelector } from 'react-redux';
import { useCallback } from 'react';
import {
  login,
  register,
  logout,
  clearError,
  updateProfile,
} from '@/features/auth/state/authSlice';
import {
  selectUser,
  selectIsAuthenticated,
  selectAuthLoading,
  selectAuthError,
} from '@/features/auth/state/authSelectors';
import { AppDispatch } from '@/store/store';
import { LoginRequest, RegisterRequest, User } from '@/types/auth';

export function useAuth() {
  const dispatch = useDispatch<AppDispatch>();
  const user = useSelector(selectUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const isLoading = useSelector(selectAuthLoading);
  const error = useSelector(selectAuthError);

  const loginUser = useCallback((credentials: LoginRequest) => dispatch(login(credentials)), [dispatch]);
  const registerUser = useCallback((data: RegisterRequest) => dispatch(register(data)), [dispatch]);
  const logoutUser = useCallback(() => dispatch(logout()), [dispatch]);
  const clearAuthError = useCallback(() => dispatch(clearError()), [dispatch]);
  const updateUserProfile = useCallback((user: User) => dispatch(updateProfile(user)), [dispatch]);

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    loginUser,
    registerUser,
    logoutUser,
    clearAuthError,
    updateUserProfile,
  };
}
