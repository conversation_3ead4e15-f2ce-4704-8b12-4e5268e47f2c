import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import * as organizationService from "@/services/organizationService";
import { OrganizationState } from "../types/organization";

const initialState: OrganizationState = {
  list: [],
  error: null,
  isLoading: true,
};
const nameState = "organization";

export const organization = createAsyncThunk(`${nameState}/list`, async (countryCode: string) => {
  try {
    const res = await organizationService.getOrganizationList(countryCode);
    return res;
  } catch (err: any) {}
});

const organizationSlice = createSlice({
  name: nameState,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(organization.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(organization.fulfilled, (state, action: any) => {
        state.isLoading = false;
        state.list = action.payload;
      })
      .addCase(organization.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as any;
      });
  },
});

export default organizationSlice.reducer;
