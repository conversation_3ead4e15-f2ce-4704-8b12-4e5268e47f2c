import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import {
  MILLSHEET_STATUS,
  MillSheetPreviewFileParams,
  MillSheetStatusType,
} from "@/features/millsheet/types/millSheetTypes";
import {
  AutocompleteField,
  CheckboxField,
  DateFieldEdit,
  DateRangeField,
  FileField,
  RadioField,
  SelectField,
  TextField,
  SelectMultipleField,
  TextDecimalField,
} from "@/components/common/DynamicForm/fields";
import Button<PERSON>ommon from "@/components/common/button/Button";
import TextRangeField from "@/components/common/DynamicForm/fields/TextRageField";
import { useDynamicForm } from "@/components/common/DynamicForm/hooks/useDynamicForm";
import dayjs from "dayjs";
import * as MillSheetService from "@/services/millSheetService";
import DynamicForm, {
  FieldConfig,
  FieldOption,
} from "@/components/common/DynamicForm";
import { useSelector } from "react-redux";
import { selectUserProfile } from "@/features/users/state/usersSelectors";
import { useSnackbar } from "@/components/common/SnackBar/SnackbarProvider";
import { isFormModified } from "@/utils/common";

interface MillSheetRecord {
  id: number | string;
  status: number;
  serialNumber: string;
  mill: string;
  customer: string;
  invoiceNo?: string;
  poNo?: string;
  heatNo?: string;
  standard: string;
  size: string;
  weight: string;
  dateOfIssue?: string | Date | undefined;
}

interface MillSheetEditPopupProps {
  open: boolean;
  onClose: () => void;
  data: Partial<MillSheetRecord>;
  handleEditSuccess: (payload: MillSheetRecord, idNew: number) => void;
}

const fieldComponentMap: Record<string, any> = {
  text: TextField,
  email: TextField,
  password: TextField,
  number: TextField,
  textarea: TextField,
  select: SelectField,
  autocomplete: AutocompleteField,
  checkbox: CheckboxField,
  radio: RadioField,
  date: DateFieldEdit,
  file: FileField,
  dateRange: DateRangeField,
  selectMultiple: SelectMultipleField,
  textDecimal: TextDecimalField,
  textRange: TextRangeField,
};

const MillSheetEditPopup: React.FC<MillSheetEditPopupProps> = ({
  open,
  onClose,
  data,
  handleEditSuccess,
}) => {
  const userProfile = useSelector(selectUserProfile);
  const [supplierList, setSupplierList] = useState<FieldOption[]>([]);
  const [customerList, setCustomerList] = useState<FieldOption[]>([]);
  const [dataEdit, setDataEdit] = useState<Partial<MillSheetRecord>>({});
  const [originalData, setOriginalData] = useState<Partial<MillSheetRecord>>({});

  const [isLoading, setIsLoading] = useState(false);
  const [isDisabled, setIsDisabled] = useState(false);

  const { showSnackbar } = useSnackbar();

  useEffect(() => {
    if (open) {
      MillSheetService.getSuplierList()
        .then((suppliers) => {
          setSupplierList(
            suppliers.map((s) => ({
              label: s.supplierName,
              value: s.supplierId,
            }))
          );
        })
        .catch((error) => {
          console.error("Failed to fetch supplier list:", error);
        });
    }
  }, [open]);

  useEffect(() => {
    if (open) {
      const getCountryCode = userProfile?.countryCode as string;
      MillSheetService.getCustomerList(getCountryCode)
        .then((customers) => {
          setCustomerList(
            customers.map((s) => ({
              label: s.customerName,
              value: s.customerId,
            }))
          );
        })
        .catch((error) => {
          console.error("Failed to fetch customer list:", error);
        });
    }
  }, [open, userProfile?.countryCode]);

  const millSheetFields: FieldConfig[] = useMemo(() => {
    return [
      {
        name: "mill",
        label: "Mill",
        type: "autocomplete",
        required: true,
        options: supplierList.map((m) => ({
          label: m.label,
          value: m.value,
        })),
        maxLength: 255,
        customValidation: (value) => {
          if (value.trim() === "") {
            return "Mill is a required field. Please enter it";
          }

          return true;
        },
      },
      {
        name: "customer",
        label: "Customer",
        type: "autocomplete",
        required: true,
        options: customerList.map((c) => ({
          label: c.label,
          value: c.value,
        })),
        maxLength: 255,
        customValidation: (value) => {
          if (value.trim() === "") {
            return "Customer is a required field. Please enter it";
          }

          return true;
        },
      },
      {
        name: "invoiceNo",
        label: "Invoice No.",
        type: "text",
        maxLength: 50,
      },
      { name: "poNo", label: "PO No.", type: "text", maxLength: 50 },
      {
        name: "serialNumber",
        label: "Serial Number",
        required: true,
        type: "text",
      },
      {
        name: "heatNo",
        label: "Heat No.",
        type: "text",
        maxLength: 50,
      },
      {
        name: "standard",
        label: "Standard",
        type: "text",
        required: true,
      },
      { name: "size", label: "Size", type: "text", required: true },
      {
        name: "weight",
        label: "Weight",
        type: "textDecimal",
        isNumberic: true,
        required: true,
        allowDecimal: true,
        maxIntegerDigits: 7,
        maxDecimalDigits: 3,
        customValidation: (value) => {
          const trimmedValue = value;
          // Regex to match a valid number (integer or decimal)
          const numberRegex = /^-?\d+(\.\d+)?$/;

          if (!numberRegex.test(trimmedValue)) {
            return "Please enter a valid number.";
          }

          return true;
        },
      },
      {
        name: "dateOfIssue",
        label: "Date of Issue",
        required: true,
        type: "date",
      },
    ];
  }, [supplierList, customerList]);

  const safeFields = millSheetFields ?? [];
  const defaultValues = {};
  const form = useDynamicForm(safeFields, defaultValues);

  const { control, formState, reset, setValue } = form;

  const fetchData = async () => {
    if (!data.id) return;
    try {
      reset([]);

      const { id, status } = data;
      const body = {
        id: id,
        status: status as MillSheetStatusType,
      };
      const res: Partial<MillSheetRecord> =
        await MillSheetService.getMillSheetById(body);
      setOriginalData({ ...res });
      const mockMillSheetRecord: Partial<MillSheetRecord> = {
        ...res,
        serialNumber: res.serialNumber || "",
        status: res.status || 0,
        mill: res.mill || "",
        customer: res.customer || "",
        poNo: res.poNo || "",
        invoiceNo: res.invoiceNo || "",
        heatNo: res.heatNo || "",
        standard: res.standard || "",
        size: res.size || "",
        weight: res.weight || "",
        dateOfIssue: res.dateOfIssue
          ? dayjs(res.dateOfIssue, "DD/MM/YYYY").toDate()
          : undefined,
      };
      // Set form values here
      const transformedData = transformApiDataToFormValues(mockMillSheetRecord);
      setDataEdit(transformedData);
      reset(transformedData);
    } catch (error) {
      console.error("Failed to fetch mill sheet:", error);
    } finally {
    }
  };

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const getLabelByValue = (value: number | undefined): string => {
    if (!value) return "";
    const options = [
      { label: "No ERP", value: 2 },
      { label: "Error", value: 3 },
      { label: "Imported", value: 4 },
    ];

    const match = options.find((opt) => opt.value === value);
    return match ? match.label : "";
  };

  const RenderFieldComponent = useCallback((fieldProps: any) => {
    const FieldComponent = fieldComponentMap[fieldProps.field.type];
    if (!FieldComponent) return null;
    return <FieldComponent {...fieldProps} />;
  }, []);

  const transformApiDataToFormValues = (
    apiResponse: Partial<MillSheetRecord>
  ) => {
    if (apiResponse?.customer && customerList.length > 0) {
      const matchedCustomer = customerList.find(
        (c) => c.value === apiResponse.customer
      );

      if (matchedCustomer) {
        setValue("customer", {
          label: matchedCustomer.label,
          value: matchedCustomer.value,
        });
      }
    }
    if (apiResponse?.mill && supplierList.length > 0) {
      const matchedCustomer = supplierList.find(
        (c) => c.value === apiResponse.mill
      );

      if (matchedCustomer) {
        setValue("mill", {
          label: matchedCustomer.label,
          value: matchedCustomer.value,
        });
      }
    }

    return {
      ...apiResponse,
    };
  };

  const handleSubmit = async () => {
    const isValid = await form.trigger(); // validate all fields
    if (!isValid) return; // stop if form has errors

    if (isLoading) return;
    setIsLoading(true);
    setIsDisabled(true);

    try {
      const convertPayload = {
        ...form.getValues(),
        dateOfIssue: dayjs(form.getValues("dateOfIssue")).format("DD/MM/YYYY"),
      };
      if (isFormModified(originalData, convertPayload)) {
        const res = await MillSheetService.updateMillSheet(convertPayload);
        const idNew = res?.millSheetId;
        handleEditSuccess({
          ...convertPayload,
          customer: customerList.find((c) => c.value === convertPayload.customer)?.label,
          mill: supplierList.find((s) => s.value === convertPayload.mill)?.label
        }, idNew);
      }
      onClose();
    } catch (error) {
      // Handle unexpected errors
      console.error("Save failed:", error);
    } finally {
      setTimeout(() => {
        setIsLoading(false);
        setIsDisabled(false); // allow next click after delay
      }, 1000); // debounce delay (1 second)
      showSnackbar(
        "Edit Successful!",
        "The mill sheet has been successfully updated.",
        "success"
      );
    }
  };

  const getDisplayTitle = (
    customerName?: string,
    serialNo?: string
  ): string => {
    const matchedCustomer = customerList.find(
      (c) => c.value === customerName
    )?.label;
    if (matchedCustomer && serialNo) {
      return `${matchedCustomer} - ${serialNo}`;
    }
    return customerName || serialNo || "";
  };

  const handlePdfPreview = () => {
    const { id, status } = data;
    const payload: MillSheetPreviewFileParams = {
      id: Number(id),
      fileType: 0,
      status: Number(status),
    };
    MillSheetService.getPreviewPdfFile(payload)
      .then((res) => {
        if (res.fileUrl) {
          window.open(res.fileUrl, "_blank");
        }
      })
      .catch((error) => {
        console.error("Failed to fetch supplier list:", error);
      });
  };

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
      <DialogTitle
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          height: "34px",
          margin: "20px 21px",
          padding: "0px",
          backgroundColor: "#f9f9f9",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: "6px",
          }}
        >
          <Typography
            sx={{
              textAlign: "center",
              width: "70px",
              height: "24px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              borderRadius: "4px",
              fontSize: "12px",
              color:
                dataEdit.status === MILLSHEET_STATUS.ERROR
                  ? "rgba(255, 61, 0, 1)"
                  : dataEdit.status === MILLSHEET_STATUS.IMPORTED
                  ? "rgba(76, 175, 80, 1)"
                  : "rgba(255, 61, 0, 1)",
              backgroundColor:
                dataEdit.status === MILLSHEET_STATUS.ERROR
                  ? "rgba(202, 0, 46, 0.2)"
                  : dataEdit.status === MILLSHEET_STATUS.IMPORTED
                  ? "rgba(63, 194, 138, 0.2)"
                  : "rgba(136, 54, 73, 0.2)",
            }}
          >
            {getLabelByValue(dataEdit.status)}
          </Typography>
          <Typography
            sx={{
              fontSize: "18px",
              fontWeight: 600,
              color: "#333",
            }}
          >
            {getDisplayTitle(dataEdit.customer, dataEdit.serialNumber)}
          </Typography>
        </Box>
        <Box
          sx={{
            display: "flex",
            flexWrap: "nowrap",
            alignItems: "center",
          }}
        >
          <ButtonCommon
            variant="outlined"
            onClick={handlePdfPreview}
            sx={{
              width: "110px",
              height: "34px",
              color: "#FFFFFF",
              fontWeight: 700,
              fontSize: "14px",
              lineHeight: "120%", // or "16.8px" if you want exact pixel value
              letterSpacing: "0%",
              textAlign: "center",
              borderColor: "#6A1B9A",
              backgroundColor: "#332F94",

              "&:hover": {
                backgroundColor: "#4A3FB4", // Example hover color
                borderColor: "#8E24AA", // Optional: change border on hover
              },
            }}
            size="medium"
          >
            <Typography>PDF Preview</Typography>
          </ButtonCommon>
          <IconButton
            aria-label="close"
            onClick={onClose}
            sx={{
              color: "#2A3547",
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent
        sx={{
          display: "grid",
          gridTemplateColumns: "1fr 1fr",
          maxHeight: "420px",
          padding: "20px 107px !important",
          columnGap: "20px",
          overflow: "hidden", // disables scrollbars
          alignItems: "center", // vertical alignment
          justifyContent: "center", // horizontal alignment
        }}
      >
        {millSheetFields.map((field) => (
          <Box key={field.name} sx={{ minHeight: "80px" }}>
            {RenderFieldComponent({
              field,
              control,
              error: formState.errors[field.name]?.message,
            })}
          </Box>
        ))}
      </DialogContent>
      <DialogActions
        sx={{
          height: "34px",
          margin: "20px 21px",
          padding: "0px",
          gap: "10px",
        }}
      >
        <ButtonCommon
          variant="outlined"
          onClick={onClose}
          sx={{ width: "100px", height: "34px", backgroundColor: "#fff" }}
          size="medium"
        >
          Cancel
        </ButtonCommon>
        <ButtonCommon
          type="button"
          variant="contained"
          size="small"
          sx={{
            width: "100px",
            marginLeft: "0px !important",
          }}
          disabled={isDisabled || isLoading}
          isLoading={isLoading}
          onClick={handleSubmit}
        >
          Save
        </ButtonCommon>
      </DialogActions>
    </Dialog>
  );
};

export default MillSheetEditPopup;
