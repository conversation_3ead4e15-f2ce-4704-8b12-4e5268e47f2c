import axiosInstance from "@/lib/axiosInstance";
import { Customer, MillSheetBulkDownloadParams, MillSheetDownloadParams, MillSheetResponse, MillSheetSearchParams, PreviewPDF, Supplier } from "../types/millSheetTypes";

type ApiResponse<T> = {
  success: boolean;
  code: string;
  message: string;
  params: any[];
  data: T;
};

async function apiRequest<T>(promise: Promise<any>): Promise<T> {
  const response = await promise;
  const apiResponse: ApiResponse<T> = response.data;
  if (!apiResponse.success) {
    throw new Error(apiResponse.message || "API Error");
  }
  return apiResponse.data;
}

export const searchMillSheet = async (dataSearch: MillSheetSearchParams): Promise<MillSheetResponse> => {
    return (apiRequest<MillSheetResponse>(axiosInstance.post("/mill-sheets/search", dataSearch)))
}

export const customerList = async (): Promise<Customer[]> => {
    return (apiRequest<Customer[]>(axiosInstance.get("/customer")))
}

export const supplierList = async (): Promise<Supplier[]> => {
    return (apiRequest<Supplier[]>(axiosInstance.get("/supplier")))
}

export const downloadMillSheet = async (payload: MillSheetDownloadParams): Promise<any> => {
  const res = await axiosInstance.post(`/mill-sheets/download`, payload);
  const disposition = res.headers["content-disposition"];
  const fileName = extractFilename(disposition) || `millsheet_${Date.now()}`;
  // res.data là Blob (pdf hoặc zip)
  return { blob: res.data as Blob, fileName };
}

export const bulkDownloadMillSheet = async (payload: MillSheetBulkDownloadParams[]): Promise<Blob> => {
  return axiosInstance.post(`/mill-sheets/bulk-download`, payload)
}

export const previewPDF = async (millSheetId: number): Promise<PreviewPDF> => {
  return apiRequest<PreviewPDF>(axiosInstance.get(`/mill-sheets/preview-file/${millSheetId}`));
}

function extractFilename(disposition?: string) {
  if (!disposition) return "";
  // Content-Disposition: attachment; filename="Millsheet_ABC_20240910.pdf"
  const match = /filename\*?=(?:UTF-8''|")?([^\";]+)/i.exec(disposition);
  if (match?.[1]) {
    return decodeURIComponent(match[1].replace(/"/g, ""));
  }
  return "";
}

export const confirmMillSheet = async (millSheetId: number): Promise<void> => {
  return apiRequest<void>(axiosInstance.post(`/mill-sheets/confirm/${millSheetId}`));
}