import axiosInstance from "@/lib/axiosInstance";
import {
  Customer,
  InfoFile,
  MillSheetBulkDownloadParams,
  MillSheetConfirmParams,
  MillSheetDownloadParams,
  MillSheetPreviewFileParams,
  MillSheetRecord,
  MillSheetResponse,
  MillSheetResponseEdit,
  MillSheetSearchParams,
  MillSheetStatusType,
  PreviewPDF,
  Supplier,
} from "@/features/millsheet/types/millSheetTypes";

type ApiResponse<T> = {
  success: boolean;
  code: string;
  message: string;
  params: any[];
  data: T;
};

async function apiRequest<T>(promise: Promise<any>): Promise<T> {
  const response = await promise;
  const apiResponse: ApiResponse<T> = response.data;
  if (!apiResponse.success) {
    throw new Error(apiResponse.message || "API Error");
  }
  return apiResponse.data;
}

export const searchMillSheet = async (dataSearch: MillSheetSearchParams): Promise<MillSheetResponse> => {
  return apiRequest<MillSheetResponse>(axiosInstance.post("/mill-sheets/search", dataSearch));
};

export const customerList = async (countryCode: string): Promise<Customer[]> => {
  return apiRequest<Customer[]>(axiosInstance.get(`/customer/${countryCode}`));
};

export const supplierList = async (): Promise<Supplier[]> => {
  return apiRequest<Supplier[]>(axiosInstance.get("/supplier"));
};

export const downloadMillSheet = async (payload: MillSheetDownloadParams): Promise<InfoFile> => {
  const res = await axiosInstance.post(`/mill-sheets/download`, payload, { responseType: "blob" });
  const fileName = res.headers["x-my-file-name"];
  return { file: res.data as Blob, fileName };
};

export const bulkDownloadMillSheet = async (payload: MillSheetBulkDownloadParams[]): Promise<InfoFile> => {
  const res = await axiosInstance.post(`/mill-sheets/bulk-download`, payload, { responseType: "blob" });
  const fileName = res.headers["x-my-file-name"];
  return { file: res.data as Blob, fileName};
};

export const previewPDF = async (payload: MillSheetPreviewFileParams): Promise<PreviewPDF> => {
  return apiRequest<PreviewPDF>(axiosInstance.post(`/mill-sheets/preview-file`, payload));
};

export const confirmMillSheet = async (payload: MillSheetConfirmParams): Promise<void> => {
  return apiRequest<void>(axiosInstance.put('/mill-sheets/confirm', payload));
};

export const deleteMillSheets = async (payload: {id: number; status: MillSheetStatusType}[]): Promise<void> => {
  return apiRequest<void>(axiosInstance.post(`/mill-sheets/delete`, payload));
}

export const getMillSheetEdit= async (payload: {id: number| string; status: MillSheetStatusType}): Promise<Partial<MillSheetRecord>> => {
  return apiRequest<Partial<MillSheetRecord>>(axiosInstance.post(`/mill-sheets/find`, payload));
};

export const putMillSheetEdit= async (payload: MillSheetRecord): Promise<MillSheetResponseEdit> => {
  return apiRequest<MillSheetResponseEdit>(axiosInstance.put(`/mill-sheets/update`, payload));
};
