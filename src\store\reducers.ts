// src/store/reducers.ts
import { combineReducers } from '@reduxjs/toolkit';

import authReducer from '@/features/auth/state/authSlice';
import usersReducer from '@/features/users/state/usersSlice';
import modalReducer from '@/features/modal/modalSlice';
import departmentReducer from '@/features/department/state/departmentSlice';
import roleReducer from '@/features/role/state/roleSlice';
import organizationReducer from '@/features/organization/state/organizationSlice'
import countryReducer from '@/features/country/state/countrySlice';
import breadcrumbReducer from '@/features/breadcrumb/state/BreadcrumbSlice';

const rootReducer = combineReducers({
  auth: authReducer,
  users: usersReducer,
  modal: modalReducer,
  department: departmentReducer,
  role: roleReducer,
  organization: organizationReducer,
  country: countryReducer,
  breadcrumb: breadcrumbReducer
});

export type RootState = ReturnType<typeof rootReducer>;
export default rootReducer;
