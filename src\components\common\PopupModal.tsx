import React from "react";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import WarningIcon from "@mui/icons-material/Warning";
import InfoIcon from "@mui/icons-material/Info";
import CloseIcon from "@mui/icons-material/Close";
import { Box } from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";

type PopupType = "success" | "warning" | "info" | "error";

interface PopupModalProps {
  open: boolean;
  title: string;
  description: string;
  type?: PopupType;
  isOKOnly?: boolean;
  onClose?: () => void;
  onSubmit?: () => void;
  hideCloseButton?: boolean;
  disableBackdropClick?: boolean;
}

const colorMap: { [type: string]: { glowLarge: string; glowSmall: string } } = {
  success: {
    glowLarge: "rgba(236, 253, 243, 1)",
    glowSmall: "rgba(209, 250, 223, 1)",
  },
  warning: {
    glowLarge: "rgba(255, 250, 235, 1)",
    glowSmall: "rgba(254, 240, 199, 1)",
  },
  info: {
    glowLarge: "rgba(244, 246, 255, 1)",
    glowSmall: "rgba(218, 221, 255,1)",
  },
  error: {
    glowLarge: "rgba(254, 243, 242, 1)",
    glowSmall: "rgba(254, 228, 226, 1)",
  },
};

const iconMap = {
  success: <CheckCircleOutlineIcon sx={{ color: "#2e7d32", fontSize: 40 }} />,
  warning: <WarningIcon sx={{ color: "rgba(220, 104, 3, 1)", fontSize: 40 }} />,
  error: <CancelIcon sx={{ color: "rgba(202, 0, 46, 1)", fontSize: 40 }} />,
  info: <InfoIcon sx={{ color: "#1976d2", fontSize: 40 }} />,
};

const PopupModal: React.FC<PopupModalProps> = ({
  open,
  title,
  description,
  type = "info",
  isOKOnly = false,
  onClose,
  onSubmit,
  hideCloseButton = false,
  disableBackdropClick = false,
}) => {
  const [internalOpen, setInternalOpen] = React.useState(open);
  React.useEffect(() => {
    setInternalOpen(open);
  }, [open]);

  const handleClose = (event?: object, reason?: string) => {
    if (disableBackdropClick && (reason === 'backdropClick' || reason === 'escapeKeyDown')) {
      return;
    }
    if (onClose) {
      onClose();
    } else {
      setInternalOpen(false);
    }
  };

  const Icon = () => iconMap[type];
  return (
    <Dialog
      open={internalOpen}
      onClose={handleClose}
      maxWidth="xs"
      fullWidth
      PaperProps={{
        sx: {
          width: 400,
          minHeight: 270,
          borderRadius: "16px",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          position: "relative",
          pb: 0,
        }}
      >
        {!hideCloseButton && (
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              padding: "10px",
              borderRadius: "8px",
            }}
          >
            <CloseIcon />
          </IconButton>
        )}
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            width: "100%",
          }}
        >
          <Box
            sx={{
              marginBottom: 2,
              width: "80px",
              height: "80px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              background: colorMap[type]?.glowLarge,
              borderRadius: "50%",
              position: "relative",
              "&::before": {
                content: '""',
                position: "absolute",
                width: "55px",
                height: "55px",
                left: "50%",
                top: "50%",
                transform: "translate(-50%, -50%)",
                background: colorMap[type]?.glowSmall,
                borderRadius: "50%",
                zIndex: 1,
              },
              ".MuiSvgIcon-root": {
                width: "36px",
                height: "36px",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "20px",
                position: "relative",
                zIndex: 2,
              },
            }}
          >
            <Icon></Icon>
          </Box>
          <Typography
            variant="h6"
            fontWeight={600}
            textAlign="center"
            fontSize={18}
            lineHeight="130%"
          >
            {title}
          </Typography>
        </div>
      </DialogTitle>
      <DialogContent sx={{ textAlign: "center", pt: 1, mt: 0.5 }}>
        <Typography
          variant="body2"
          color="text.secondary"
          fontSize={14}
          lineHeight="150%"
        >
          {description}
        </Typography>
      </DialogContent>
      <DialogActions sx={{ justifyContent: isOKOnly ? "center": "space-between", pb: 2, padding: '8px 24px 24px 24px' }}>
        {!isOKOnly && (
          <Button
            onClick={handleClose}
            variant="contained"
            sx={{
              minWidth: "150px",
              fontWeight: 600,
              height: "50px",
              fontSize: 18,
              lineHeight: "26px",
              backgroundColor: "rgba(255, 255, 255, 1)",
              border: "1px solid rgba(191, 196, 204, 1)",
              color: "rgba(57, 0, 11, 1)",
              boxShadow: "none",
              ":hover": {
                boxShadow: "none",
              },
            }}
          >
            Cancel
          </Button>
        )}
        <Button
          onClick={() => {
            if (onSubmit) {
              onSubmit();
            } else {
              handleClose();
            }
          }}
          variant="contained"
          sx={{
            minWidth: "150px",
            fontWeight: 600,
            height: "50px",
            fontSize: 18,
            lineHeight: "26px",
            backgroundColor: "rgba(202, 0, 46, 1)",
            boxShadow: "none",
            ":hover": {
              boxShadow: "none",
            },
          }}
        >
          OK
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PopupModal;
