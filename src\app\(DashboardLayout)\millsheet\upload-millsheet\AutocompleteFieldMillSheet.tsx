import React from "react";
import Autocomplete from "@mui/material/Autocomplete";
import TextField from "@mui/material/TextField";
import { Controller } from "react-hook-form";
import { Box } from "@mui/material";
import MuiErrorCustomStyles from "@/utils/theme/muiErrorStyles";
import { FieldConfig, FieldOption } from "@/components/common/DynamicForm";

interface Props {
  field: FieldConfig;
  control: any;
  error?: string;
  disabled?: boolean;
  i18n?: (key: string) => string;
}

const AutocompleteFieldMillSheet: React.FC<Props> = ({
  field,
  control,
  error,
  disabled,
  i18n,
}) => (
  <Controller
    name={field.name}
    control={control}
    render={({ field: controllerField }) => (
      <Box style={{ position: "relative", width: "100%" }}>
        <Box
          style={{
            position: "absolute",
            left: 0,
            right: 0,
            top: 2,
            color: error ? "#CA002E" : "#222222",
            fontFamily: "Roboto",
            fontWeight: 400,
            fontSize: 14,
            lineHeight: 1.5,
            zIndex: 2,
          }}
        >
          {field.required ? (
            <span>
              {i18n ? i18n(field.label) : field.label}
              <span style={{ color: "#CA002E", marginLeft: 4 }}>*</span>
            </span>
          ) : i18n ? (
            i18n(field.label)
          ) : (
            field.label
          )}
        </Box>
        <Autocomplete
          {...controllerField}
          options={field.options || []}
          getOptionLabel={(option: any) =>
            i18n ? i18n(option?.label || "") : option?.label || ""
          }
          isOptionEqualToValue={(option: any, value: any) =>
            option.value === value.value
          }
          disabled={disabled || field.disabled}
          onBlur={controllerField.onBlur}
          renderInput={(params) => (
            <TextField
              {...params}
              label=""
              placeholder={field.placeholder}
              error={!!error}
              helperText={
                error
                  ? "Please select a customer before uploading files."
                  : field.helperText
              }
              fullWidth
              slotProps={{
                htmlInput: {
                  ...params.inputProps,
                  maxLength: field.maxLength,
                },
              }}
              sx={{
                ...field.style,
                ...MuiErrorCustomStyles,
                mt: 3,
                "& .MuiInputBase-input.Mui-disabled": {
                  WebkitTextFillColor:
                    (field as any).disabledValueColor || "#111827",
                  color: (field as any).disabledValueColor || "#111827",
                },
              }}
              className={field.className}
            />
          )}
          onChange={(_, newValue) => {
            controllerField.onChange(newValue ? newValue.value : undefined);
          }}
          value={
            field.options?.find(
              (option: FieldOption) => option.value === controllerField.value
            ) || null
          }
        />
      </Box>
    )}
  />
);

export default AutocompleteFieldMillSheet;
