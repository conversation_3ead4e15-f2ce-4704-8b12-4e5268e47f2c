# Next.js Project

This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, install dependencies and run the development server:

```bash
npm install
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Building for Production

To build the project for production:

```bash
npm run build
```

This command generates a static export in the `out` folder, optimized for production deployment.

## Deployment to Azure Static Web Apps

After building the project, the static files will be available in the `out` directory. Follow these steps to deploy to Azure Static Web Apps:

1. **Build the project:**
   ```bash
   npm run build
   ```

2. **Upload to Azure Static Web Apps:**
   - Navigate to your Azure Static Web App resource in the Azure portal
   - Go to the "Overview" section
   - Click on "Browse" or use the deployment center to upload the contents of the `out` folder
   - Alternatively, you can drag and drop the `out` folder contents directly to the Azure Static Web Apps deployment interface

3. **Configure build settings (if using GitHub Actions):**
   - App location: `/`
   - Build location: `out`
   - API location: `` (leave empty if no API)

## Project Structure

```plaintext
├── .husky/                     # Git hooks (pre-commit lint, pre-push test)
├── .vscode/                    # VSCode config (recommended extensions, settings)
├── cypress/                    # E2E testing với Cypress
│   ├── e2e/
│   ├── fixtures/
│   ├── support/
│   └── cypress.config.ts
├── public/                     # Static assets (images, logos, fonts)
│   ├── images/
│   └── favicon.ico
├── src/
│   ├── app/                    # NextJS App Router structure
│   │   ├── dashboard/
│   │   ├── login/
│   │   └── layout.tsx
│   ├── components/             # Shared, reusable UI components
│   │   ├── common/             # Buttons, Modals, Inputs...
│   │   ├── layout/             # Navbar, Sidebar, Footer...
│   │   └── feature/            # Specific feature-driven UI
│   ├── constants/              # Global constants (routes, roles, enums)
│   ├── features/               # Domain features (Redux slice, service, component)
│   │   ├── users/
│   │   │   ├── api/            # API calls for users
│   │   │   ├── components/     # User-specific UI components
│   │   │   ├── hooks/          # Hooks riêng của users
│   │   │   ├── screens/        # Pages/screens thuộc feature users
│   │   │   ├── state/          # Redux slice, selectors
│   │   │   ├── types/          # TypeScript interfaces
│   │   │   └── utils/          # Feature-specific utils
│   │   ├── products/
│   │   └── index.ts            # Barrel export toàn bộ feature
│   ├── hooks/                  # Global custom hooks (useDebounce, usePagination…)
│   ├── layouts/                # App-level layouts (AdminLayout, AuthLayout…)
│   ├── lib/                    # Third-party wrapper: axiosInstance, socket.ts, dayjs.ts
│   ├── providers/              # React Context Providers (AuthProvider, ThemeProvider…)
│   ├── services/               # Generic services, api instance
│   ├── store/                  # Redux store configuration
│   │   ├── hooks.ts            # Global typed hooks useAppSelector
│   │   ├── reducers.ts         # Root reducers
│   │   └── store.ts            # Store setup
│   ├── theme/                  # MUI custom theme, palette, typography
│   ├── types/                  # Global TypeScript types (API Response type…)
│   ├── utils/                  # Global utilities (validators, formatter…)
│   └── index.tsx               # Main entry Next.js
├── jest.config.ts              # Jest config
├── next.config.js              # NextJS config
├── sonar-project.properties    # SonarQube configuration
├── tsconfig.json               # TypeScript config
├── .eslintrc.json              # ESLint config
├── .prettierrc                 # Prettier config
├── package.json
└── README.md
```

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!