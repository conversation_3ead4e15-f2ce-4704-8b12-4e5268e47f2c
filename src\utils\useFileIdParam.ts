import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

/**
 * Custom hook to get and manage URL params related to mill sheet identifiers.
 * Supports:
 *  - file_id: existing behavior
 *  - id: new param (single or comma separated values) similar logic to file_id
 * Returns: { fileIdParam, fileIdArray, setFileIdArray, idParam, idArray, setIdArray }
 */
export function useMillSheetQueryIds() {
  const searchParamsUrl = useSearchParams();
  const fileIdParam = searchParamsUrl?.get("file_id");
  const idParam = searchParamsUrl?.get("id");

  const [fileIdArray, setFileIdArray] = useState<string | undefined>(fileIdParam ?? undefined);
  const [idArray, setIdArray] = useState<string | undefined>(idParam ?? undefined);

  useEffect(() => {
    setFileIdArray(fileIdParam ?? undefined);
  }, [fileIdParam]);

  useEffect(() => {
    setIdArray(idParam ?? undefined);
  }, [idParam]);

  return { fileIdParam, fileIdArray, setFileIdArray, idParam, idArray, setIdArray };
}
