import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import * as customerService from "@/services/customerService";
import {  CustomerState } from "../types/customer";

const initialState: CustomerState = {
  list: [],
  error: null,
  isLoading: true,
};
const nameState = "customer";

export const customer = createAsyncThunk(`${nameState}/list`, async (countryCode: string) => {
  try {
    const res = await customerService.getCustomerList(countryCode);
    return res;
  } catch (err: any) {}
});

const customerSlice = createSlice({
  name: nameState,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(customer.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(customer.fulfilled, (state, action: any) => {
        state.isLoading = false;
        state.list = action.payload;
      })
      .addCase(customer.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as any;
      });
  },
});

export default customerSlice.reducer;
