import {
  IconAperture,
  IconCopy,
  IconLayoutGrid,
  IconLogin,
  IconMoodHappy,
  IconTypography,
  IconUserPlus,
} from "@tabler/icons-react";
import { GridViewIcon, UserGroupIcon, Note01Icon } from "hugeicons-react";
import Image from "next/image";
import { uniqueId } from "lodash";
import { DATA_IDS, PAGE_TITLES } from "@/constants/routes";

const Menuitems = [
  // {
  //   navlabel: true,
  //   subheader: "HOME",
  // },

  {
    id: uniqueId(),
    title: PAGE_TITLES.MAIN_MENU,
    iconLink: {
      default: "/images/sidebar/Vector.svg",
      active: "/images/sidebar/active/Vector.svg",
    },
    href: "/",
  },
  // {
  //   navlabel: true,
  //   subheader: "UTILITIES",
  // },
  {
    id: uniqueId(),
    title: PAGE_TITLES.USER_LIST,
    href: "/user-master",
    screenCode: "SUMA01",
    iconLink: {
      default: "/images/sidebar/user.svg",
      active: "/images/sidebar/active/user.svg",
    },
    // children: [
    //   {
    //     id: uniqueId(),
    //     title: "Create User",
    //     href: "/user-master/create",
    //     isHidden: true,
    //   },
    //   {
    //     id: uniqueId(),
    //     title: "Edit User",
    //     href: "/user-master/edit",
    //     isHidden: true,
    //   },
    //   {
    //     id: uniqueId(),
    //     title: "Detail User",
    //     href: "/user-master/detail",
    //     isHidden: true,
    //   },
    // ],
  },
  {
    id: uniqueId(),
    title: PAGE_TITLES.MILL_SHEET,
    iconLink: {
      default: "/images/sidebar/millsheet.svg",
      active: "/images/sidebar/active/millsheet.svg",
    },
    dataId: DATA_IDS.MILL_SHEET,
    // href: '/millsheet',
    children: [
      {
        id: uniqueId(),
        title: PAGE_TITLES.MILL_SHEET_LIST,
        href: "/millsheet/list",
        screenCode: "SMIL01",
      },
      {
        id: uniqueId(),
        title: PAGE_TITLES.MILL_SHEET_EDIT,
        href: "/millsheet/modification",
        screenCode: "SMIL04",
      },
      {
        id: uniqueId(),
        title: PAGE_TITLES.MILL_SHEET_UPLOAD,
        href: "/millsheet/upload-millsheet",
        screenCode: "SMIL03",
      },
      {
        id: uniqueId(),
        title: PAGE_TITLES.ERP_DATA_UPLOAD,
        href: "/millsheet/upload-erp",
        screenCode: "SMIL02",
      },
    ],
  },
  // {
  //   navlabel: true,
  //   subheader: "AUTH",
  // },
  // {
  //   id: uniqueId(),
  //   title: "Login",
  //   icon: IconLogin,
  //   href: "/authentication/login",
  // },
  // {
  //   id: uniqueId(),
  //   title: "Register",
  //   icon: IconUserPlus,
  //   href: "/authentication/register",
  // },
  // // {
  // //   navlabel: true,
  // //   subheader: " EXTRA",
  // // },
  // {
  //   id: uniqueId(),
  //   title: "Icons",
  //   icon: IconMoodHappy,
  //   href: "/icons",
  // },
  // {
  //   id: uniqueId(),
  //   title: "Sample Page",
  //   icon: IconAperture,
  //   href: "/sample-page",
  // },
];
export const MenuItemsHidden = [
  {
    id: uniqueId(),
    title: PAGE_TITLES.USER_LIST,
    href: "/user-master",
    screenCode: "SUMA01",
    iconLink: {
      default: "/images/sidebar/user.svg",
      active: "/images/sidebar/active/user.svg",
    },
    children: [
      {
        id: uniqueId(),
        title: "Add New User",
        href: "/user-master/create",
        screenCode: "SUMA04",
      },
    ],
  },
  {
    id: uniqueId(),
    title: "Edit User",
    href: "/user-master/edit",
    screenCode: "SUMA03",
  },
  {
    id: uniqueId(),
    title: "Detail User",
    href: "/user-master/detail",
    screenCode: "SUMA02",
  },
  {
    id: uniqueId(),
    title: "My Profile",
    href: "/your-profile",
    screenCode: "SCOM02",
  },
];
export const AllMenuItems = [...Menuitems, ...MenuItemsHidden];

export default Menuitems;
