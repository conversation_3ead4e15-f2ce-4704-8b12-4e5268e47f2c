/**
 * Replace HTML entity "&amp;" with "&" in a URL string.
 * Returns the original value if empty.
 */
export function decodeHtmlUrl(url: string): string {
  if (!url) return url;
  return url.replace(/&amp;/g, "&");
}

/**
 * Check if file size is within maxMB (inclusive).
 */
export const withinSize = (file: File, maxMB: number) => file.size <= maxMB * 1024 * 1024;

/**
 * Get lowercase file extension (includes the dot), or "" if none.
 */
export const getFileExtension = (name: string) => {
  const idx = name.lastIndexOf(".");
  return idx >= 0 ? name.slice(idx).toLowerCase() : "";
};

/**
 * Check if a File (or filename) has an allowed extension.
 * exts should include the dot, e.g. [".png", ".jpg"].
 * Case-insensitive.
 */
export const hasAllowedExtension = (fileOrName: File | string, exts: string[]) => {
  const name = typeof fileOrName === "string" ? fileOrName : fileOrName.name;
  const ext = getFileExtension(name);
  return exts.map((e) => e.toLowerCase()).includes(ext);
};

export const downloadBlob = (blob: Blob, fileName?: string) => {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = fileName || "download";
  document.body.appendChild(a);
  a.click();
  a.remove();
  window.URL.revokeObjectURL(url);
};

export function getDateTimeSuffix(date: Date = new Date()): string {
  const pad = (n: number) => n.toString().padStart(2, "0");
  return `${date.getFullYear()}${pad(date.getMonth() + 1)}${pad(date.getDate())}_${pad(date.getHours())}${pad(date.getMinutes())}${pad(date.getSeconds())}`;
}
