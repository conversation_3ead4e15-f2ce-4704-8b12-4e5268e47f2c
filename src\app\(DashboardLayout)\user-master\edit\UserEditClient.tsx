"use client";
import PageContainer from "@/app/(DashboardLayout)/components/container/PageContainer";
import DashboardCard from "@/app/(DashboardLayout)/components/shared/DashboardCard";
import type { FieldConfig } from "@/components/common/DynamicForm/DynamicForm.types";
import PopupModal from "@/components/common/PopupModal";
import UserForm from "@/components/feature/UserMaster/UserForm/UserForm";
import { useBreadcrumb } from "@/features/breadcrumb/hook/useBreadcrumb";
import { selectUserProfile } from "@/features/users/state/usersSelectors";
import { setUserAccessMenus, setUserProfile } from "@/features/users/state/usersSlice";
import { UserMaster } from "@/features/users/types/user";
import { getProfile } from "@/services/profileService";
import * as userMasterService from "@/services/userMasterService";
import { isEqual } from "lodash";
import { useRouter } from "next/navigation";
import React, { useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

interface UserDetailClientProps {
  clientId: string;
  user: any;
  optionList: any;
}
const UserEditPage: React.FC<UserDetailClientProps> = ({ clientId = "", user, optionList }) => {
  useBreadcrumb({
    pageTitle: "Edit User",
    items: [
      { id: "user-master", title: "User List", href: "/user-master" },
      { id: `user-edit`, title: `${user.lastName} ${user.firstName}`, href: `/user-master/detail?id=${clientId}` },
      { id: "user-edit", title: "Edit" },
    ],
  });
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  const userProfile = useSelector(selectUserProfile);
  const dispatch = useDispatch();

  const departmentByCode = useMemo(
    () => Object.fromEntries(optionList.departmentList.map((d: any) => [d.departmentCode, d])),
    [optionList.departmentList]
  );
  const roleByCode = useMemo(
    () => Object.fromEntries(optionList.roleList.map((r: any) => [r.roleCode, r])),
    [optionList.roleList]
  );
  const countryByCode = useMemo(
    () => Object.fromEntries(optionList.countryList.map((c: any) => [c.countryCode, c])),
    [optionList.countryList]
  );
  const organizationByCode = useMemo(
    () => Object.fromEntries(optionList.organizationList.map((o: any) => [o.organizationCode, o])),
    [optionList.organizationList]
  );

  const userFormFields: FieldConfig[] = [
    {
      name: "firstName",
      label: "First Name",
      type: "text",
      placeholder: "Enter full name",
      required: true,
      disabled: true,
      validation: {
        required: "Full name is required",
        minLength: { value: 2, message: "Name must be at least 2 characters" },
        maxLength: { value: 50, message: "Name must not exceed 50 characters" },
      },
      customValidation: (value) => {
        if (value && value.length > 50) {
          return "First Name can be entered up to a maximum of 50 characters";
        }

        const regex = /^[A-Za-zÀ-ỹ\s]+$/;
        if (value && !regex.test(value)) {
          return "The format of First Name is incorrect";
        }
        return true;
      },
    },
    {
      name: "lastName",
      label: "Last Name",
      type: "text",
      placeholder: "Enter last name",
      required: true,
      disabled: true,
      validation: {
        required: "Last name is required",
      },
      customValidation: (value) => {
        if (value && value.length > 50) {
          return "Last Name can be entered up to a maximum of 50 characters";
        }
        const regex = /^[A-Za-zÀ-ỹ\s]+$/;
        if (value && !regex.test(value)) {
          return "The format of Last Name is incorrect";
        }
        return true;
      },
    },
    {
      name: "organizationCode",
      label: "Company",
      type: "autocomplete",
      required: true,
      options: optionList.organizationList.map((o: any) => ({
        value: o.organizationCode,
        label: o.organizationName,
      })),
      validation: {
        required: "Company is required",
      },
      customValidation: (value) => {
        if (!value) {
          return "Company is a required field. Please enter it";
        }
        return true;
      },
    },
    {
      name: "userId",
      label: "User ID",
      type: "text",
      placeholder: "Enter user ID",
      required: true,
      disabled: true,
      validation: {
        required: "User ID is required",
        minLength: {
          value: 3,
          message: "User ID must be at least 3 characters",
        },
      },
      customValidation: (value) => {
        if (value && value.length > 100) {
          return "User ID can be entered up to a maximum of 100 characters";
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          return "Please enter the correct email address format";
        }
        return true;
      },
    },
    {
      name: "departmentCode",
      label: "Department",
      type: "autocomplete",
      required: true,
      options: optionList.departmentList.map((d: any) => ({
        value: d.departmentCode,
        label: d.departmentName,
      })),
      validation: {
        required: "Department is required",
      },
      customValidation: (value) => {
        if (!value) {
          return "Department is a required field. Please enter it";
        }
        return true;
      },
    },
    {
      name: "countryCode",
      label: "Country",
      type: "autocomplete",
      required: true,
      disabled: true,
      options: optionList.countryList.map((c: any) => ({
        value: c.countryCode,
        label: c.countryName,
      })),
      validation: {
        required: "Country is required",
      },
      customValidation: (value) => {
        if (!value) {
          return "Country is a required field. Please enter it";
        }
        return true;
      },
    },
    {
      name: "roleCode",
      label: "Role",
      type: "autocomplete",
      required: true,
      options: optionList.roleList.map((r: any) => ({
        value: r.roleCode,
        label: r.roleName,
      })),
      validation: {
        required: "Role is required",
      },
      customValidation: (value) => {
        if (!value) {
          return "Role is a required field. Please enter it";
        }
        return true;
      },
    },
  ];

  let defaultOrgCode = undefined;
  if (userProfile?.organizationCode && Array.isArray(optionList.organizationList)) {
    const foundOrg = organizationByCode[userProfile?.organizationCode];
    if (foundOrg) {
      defaultOrgCode = userProfile?.organizationCode;
      userFormFields.find((f) => f.name === "organizationCode")!.disabled = true;
    }
  }

  const initialValues = { ...user, organizationCode: defaultOrgCode };
  const originalData = {
    firstName: initialValues.firstName || "",
    lastName: initialValues.lastName || "",
    userId: initialValues.userId || "",
    roleCode: initialValues.roleCode || "",
    organizationCode: initialValues.organizationCode || "",
    departmentCode: initialValues.departmentCode || "",
    countryCode: initialValues.countryCode || "",
    imageUrl: initialValues.imageUrl || "",
  };

  const handleSubmit = async (data: Record<string, any>) => {
    const userParsed: UserMaster = {
      firstName: data.firstName,
      lastName: data.lastName,
      countryCode: data.countryCode,
      countryName: countryByCode[data?.countryCode || ""]?.countryName || "",
      departmentCode: data.departmentCode,
      departmentName: departmentByCode[data?.departmentCode || ""]?.departmentName || "",
      roleCode: data.roleCode,
      roleName: roleByCode[data?.roleCode || ""]?.roleName || "",
      organizationCode: data.organizationCode,
      organizationName: organizationByCode[data?.organizationCode || ""]?.organizationName || "",
      userId: data.userId,
      imageUrl: data.imageUrl,
    };
    userMasterService
      .updateDetailUserMaster(clientId, userParsed)
      .then(() => {
        const clientIdUserLogin = userProfile?.clientId;
        if (clientIdUserLogin === clientId) {
          getProfile().then((res) => {
            dispatch(setUserProfile(res.data.data));
            dispatch(setUserAccessMenus(res.data.data.accessMenus));
            if (res.data.data.accessMenus) {
              localStorage.setItem("accessMenus", JSON.stringify(res.data.data.accessMenus));
            }
          });
        }
        router.push("/user-master");
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const onSubmitDialog = () => {
    handleClose();
    // Navigate back to the previous page if possible, otherwise fall back to the user list
    if (typeof window !== 'undefined' && window.history && window.history.length > 1) {
      router.back();
    } else {
      router.push('/user-master');
    }
  };
  const handleCancel = (currentData: any) => {
    const userParsed = {
      firstName: currentData.firstName,
      lastName: currentData.lastName,
      countryCode: currentData.countryCode,
      departmentCode: currentData.departmentCode,
      roleCode: currentData.roleCode,
      organizationCode: currentData.organizationCode,
      userId: currentData.userId,
      imageUrl: currentData.imageUrl,
    };
    if (!isEqual(originalData, userParsed)) {
      handleOpen();
      return;
    }
    onSubmitDialog();
  };

  return (
    <>
      <DashboardCard
        sx={{
          backgroundColor: "rgba(255, 255, 255, 1)",
          boxShadow: "none",
          borderRadius: "12px",
          border: "1px solid rgba(191, 196, 204, 1)",
        }}
        cardContentSx={{ padding: "24px" }}
      >
        {initialValues && Object.keys(initialValues).length > 0 && (
          <UserForm
            key={JSON.stringify(initialValues)}
            fields={userFormFields}
            initialValues={initialValues}
            submitLabel="Save"
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />
        )}
      </DashboardCard>
      <PopupModal
        open={open}
        onClose={handleClose}
        type="warning"
        onSubmit={onSubmitDialog}
        title=""
        description="The changes will be discarded, are you sure?"
        disableBackdropClick
      />
    </>
  );
};

export default UserEditPage;
