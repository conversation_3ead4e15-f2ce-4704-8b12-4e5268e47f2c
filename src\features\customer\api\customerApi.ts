import axiosInstance from "@/lib/axiosInstance"
import { Customer } from "../types/customer"

type ApiResponse<T> = {
  success: boolean;
  code: string;
  message: string;
  params: any[];
  data: T;
};

async function apiRequest<T>(promise: Promise<any>): Promise<T> {
  const response = await promise;
  const apiResponse: ApiResponse<T> = response.data;
  if (!apiResponse.success) {
    throw new Error(apiResponse.message || "API Error");
  }
  return apiResponse.data;
}

export const customer = async (countryCode: string): Promise<Customer[]> => {
  return apiRequest<Customer[]>(axiosInstance.get(`/customer/${countryCode}`));
};
