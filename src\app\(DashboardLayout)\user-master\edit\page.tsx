"use client";

import { getCountryList } from "@/features/country/state/countrySelector";
import { getDepartmentList } from "@/features/department/state/departmentSelector";
import { getOrganizationList } from "@/features/organization/state/organizationSelector";
import { getRoleList } from "@/features/role/state/roleSelector";
import { UserMaster } from "@/features/users/types/user";
import * as userMasterService from "@/services/userMasterService";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Loading from "../../loading";
import UserEditClient from "./UserEditClient";
import { setUserMasterDetail } from "@/features/users/state/usersSlice";
import PageContainer from "../../components/container/PageContainer";

export default function UserEditPage() {
  const searchParams = useSearchParams();
  const userId = searchParams.get("id");
  const [isLoading, setLoading] = useState(true);
  const [infoUser, setInfoUser] = useState<UserMaster>();
  const departmentList = useSelector(getDepartmentList);
  const roleList = useSelector(getRoleList);
  const countryList = useSelector(getCountryList);
  const organizationList = useSelector(getOrganizationList);
  const optionList = { departmentList, roleList, countryList, organizationList };
  const dispatch = useDispatch();

  useEffect(() => {
    if (!userId) return;
    userMasterService
      .getDetailUserMaster(userId)
      .then((res) => {
        const user: UserMaster = res?.["data"] as any;
        dispatch(setUserMasterDetail(user));
        setInfoUser(user);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [userId, dispatch]);

  if (isLoading) {
    return <Loading />;
  }

  if (!userId) {
    return <div>User ID is required</div>;
  }

  // TODO: Replace the empty array with actual infoFields data as needed
  return (
    <>
      <PageContainer title="Edit User" description="this is Edit User page">
        <UserEditClient clientId={userId} user={infoUser} optionList={optionList} />
      </PageContainer>
    </>
  );
}
