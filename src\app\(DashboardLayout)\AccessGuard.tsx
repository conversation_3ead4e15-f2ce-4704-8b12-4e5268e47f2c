"use client";
import PopupModal from "@/components/common/PopupModal";
import { useRouter } from "next/navigation";
import React from "react";
import { useScreenAccessDecision } from "@/features/auth/hooks/ScreenAccessDecision";

type Props = { children: React.ReactNode };

export default function AccessGuard({ children }: Props) {
  const router = useRouter();
  const { hasAccess, canDecide } = useScreenAccessDecision();

  if (!canDecide) return null;

  if (!hasAccess) {
    return (
      <PopupModal
        hideCloseButton
        isOKOnly
        type="warning"
        title="Access Denied"
        description="Your account does not have permission to view this page."
        open
        disableBackdropClick
        onSubmit={() => router.replace("/")}
      />
    );
  }

  return <>{children}</>;
}
