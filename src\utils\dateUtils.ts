// Parse date string in format 'dd/MM/yyyy HH:mm:ss' to Date object
export function parseDateString(dateStr: string): Date {
  // Expecting format: '16/09/2025 10:37:52'
  const [datePart, timePart] = dateStr.split(" ");
  if (!datePart || !timePart) return new Date(dateStr);
  const [day, month, year] = datePart.split("/").map(Number);
  const [hour, minute, second] = timePart.split(":").map(Number);
  return new Date(year, month - 1, day, hour, minute, second);
}
// Convert a Date object to Vietnam/Thailand timezone (returns a Date in local time, but for display use string)
export function convertToLocalTimezone(date: Date, tz: 'Asia/Ho_Chi_Minh' | 'Asia/Bangkok' = 'Asia/Ho_Chi_Minh'): Date {
  // This returns a string in the local time of the target timezone
  const localeString = date.toLocaleString('en-US', { timeZone: tz });
  // Parse back to Date object (local time)
  return new Date(localeString);
}
