import React from 'react';
import { Controller } from 'react-hook-form';
import MuiRadio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import { FieldConfig } from '../DynamicForm.types';

interface Props {
  field: FieldConfig;
  control: any;
  error?: string;
  disabled?: boolean;
  i18n?: (key: string) => string;
}

const RadioField: React.FC<Props> = ({ field, control, error, disabled, i18n }) => (
  <Controller
    name={field.name}
    control={control}
    render={({ field: controllerField }) => (
      <RadioGroup {...controllerField} row>
        {field.options?.map((option) => (
          <FormControlLabel
            key={String(option.value)}
            value={option.value}
            control={<MuiRadio disabled={disabled || field.disabled} />}
            label={i18n ? i18n(option.label) : option.label}
          />
        ))}
      </RadioGroup>
    )}
  />
);

export default RadioField;
