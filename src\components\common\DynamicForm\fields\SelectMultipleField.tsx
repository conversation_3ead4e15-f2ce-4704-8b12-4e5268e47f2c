import React from 'react';
import { Box, Checkbox, ListItemText } from '@mui/material';
import { Controller } from 'react-hook-form';
import MenuItem from '@mui/material/MenuItem';
import { FieldConfig } from '../DynamicForm.types';
import Select from '@mui/material/Select';
import MuiErrorCustomStyles from '@/utils/theme/muiErrorStyles';

interface Props {
    field: FieldConfig;
    control: any;
    error?: string;
    disabled?: boolean;
    i18n?: (key: string) => string;
}

const SelectMultipleField: React.FC<Props> = ({ field, control, error, disabled, i18n }) => (
    <Controller
        name={field.name}
        control={control}
        render={({ field: controllerField }) => (
            field.hiddenLabelNormal ? (
                <Box sx={{ position: 'relative', width: '100%' }}>
                    <Box
                        sx={{
                            position: 'absolute',
                            left: 0,
                            right: 0,
                            top: 2,
                            color: '#222222',
                            fontFamily: 'Roboto',
                            fontWeight: 400,
                            fontSize: 14,
                            lineHeight: 1.5,
                            zIndex: 2,
                        }}
                    >
                        {i18n ? i18n(field.label) : field.label}
                    </Box>
                    <Select
                        {...controllerField}
                        multiple
                        label=""
                        error={false}
                        fullWidth
                        disabled={disabled || field.disabled}
                        sx={{ ...field.style, ...MuiErrorCustomStyles, mt: 3 }}
                        className={field.className}
                        inputProps={{ 'aria-label': field.label }}
                        value={controllerField.value || []}
                        renderValue={(selected) =>
                            (selected as string[])
                                .map(
                                    (val) =>
                                        i18n
                                            ? i18n(
                                                field.options?.find((opt) => String(opt.value) === val)?.label || ''
                                            )
                                            : field.options?.find((opt) => String(opt.value) === val)?.label
                                )
                                .join(', ')
                        }
                    >
                        {field.options?.map((option) => (
                            <MenuItem key={String(option.value)} value={String(option.value)}>
                                <Checkbox
                                    checked={
                                        Array.isArray(controllerField.value) &&
                                        controllerField.value.includes(String(option.value))
                                    }
                                />
                                <ListItemText
                                    primary={i18n ? i18n(option.label) : option.label}
                                />
                            </MenuItem>
                        ))}
                    </Select>
                </Box>
            )

                :

                (
                    <Select
                        {...controllerField}
                        multiple
                        label={i18n ? i18n(field.label) : field.label}
                        error={!!error}
                        fullWidth
                        disabled={disabled || field.disabled}
                        sx={{ ...field.style, ...MuiErrorCustomStyles }}
                        className={field.className}
                        inputProps={{ 'aria-label': field.label }}
                        value={controllerField.value || []}
                        renderValue={(selected) =>
                            (selected as string[])
                                .map(
                                    (val) =>
                                        i18n
                                            ? i18n(
                                                field.options?.find((opt) => String(opt.value) === val)?.label || ''
                                            )
                                            : field.options?.find((opt) => String(opt.value) === val)?.label
                                )
                                .join(', ')
                        }
                    >
                        {field.options?.map((option) => (
                            <MenuItem key={String(option.value)} value={String(option.value)}>
                                <Checkbox
                                    checked={
                                        Array.isArray(controllerField.value) &&
                                        controllerField.value.includes(String(option.value))
                                    }
                                />
                                <ListItemText
                                    primary={i18n ? i18n(option.label) : option.label}
                                />
                            </MenuItem>
                        ))}
                    </Select>
                )
        )}
    />
);

export default SelectMultipleField;