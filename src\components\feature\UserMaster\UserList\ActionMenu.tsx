import Button from "@/components/common/button/Button";
import PopupModal from "@/components/common/PopupModal";
import { useScreenAccessDecision } from "@/features/auth/hooks/ScreenAccessDecision";
import { deleteUser } from "@/services/usersService";
import { Menu, MenuItem } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React from "react";

type ActionMenuProps = {
  row: any;
  onReload?: () => void;
  isSelf?: boolean;
};

const ActionMenu: React.FC<ActionMenuProps> = ({ isSelf, row, onReload }) => {
  const { has } = useScreenAccessDecision();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const router = useRouter();
  const handleOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const [confirmOpen, setConfirmOpen] = React.useState(false);
  const [pendingAction, setPendingAction] = React.useState<string | null>(null);
  const [canEdit, canDelete] = [has("update"), has("delete")];
  const handleAction = (action: string) => {
    if (action === "Delete") {
      setPendingAction("Delete");
      setConfirmOpen(true);
      handleClose();
    } else if (action === "Edit") {
      router.push(`/user-master/edit?id=${row.clientId}`);
      // window.location.href = `/user-master/edit?id=${row.clientId}`;
      handleClose();
    } else {
      console.log(`Action: ${action}`, row);
      handleClose();
    }
  };

  const handleConfirmDelete = async () => {
    try {
      await deleteUser(row.clientId);
      // Có thể thêm thông báo thành công/thất bại ở đây
      console.log("User deleted:", row.clientId);
      if (onReload) onReload();
    } catch (err) {
      console.error("Delete user failed:", err);
    }
    setConfirmOpen(false);
    handleClose();
    setPendingAction(null);
  };
  return (
    <>
      <Button disabled={[canEdit, canDelete].every((e) => !e)} variant="text" size="small" onClick={handleOpen}>
        <Image src="/images/icons/more.svg" alt="more" width={20} height={20} />
      </Button>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        transformOrigin={{ vertical: "top", horizontal: "right" }}
      >
        {canEdit && <MenuItem onClick={() => handleAction("Edit")}>Edit</MenuItem>}
        {!isSelf && canDelete && <MenuItem onClick={() => handleAction("Delete")}>Delete</MenuItem>}
      </Menu>
      <PopupModal
        open={confirmOpen}
        type="warning"
        title="Confirm Delete"
        description={`Are you sure you want to delete ${row.lastName || ""} ${row.firstName || ""}?`}
        onClose={() => {
          setConfirmOpen(false);
          setPendingAction(null);
        }}
        onSubmit={handleConfirmDelete}
      />
    </>
  );
};

export default ActionMenu;
