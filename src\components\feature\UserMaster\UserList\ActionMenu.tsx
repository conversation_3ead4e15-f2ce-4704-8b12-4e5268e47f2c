import React from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import PopupModal from "@/components/common/PopupModal";
import { useScreenAccessDecision } from "@/features/auth/hooks/ScreenAccessDecision";
import { deleteUser } from "@/services/usersService";
import { Menu, MenuItem, Typography } from "@mui/material";
import Button from "@/components/common/button/Button";
import { useSnackbar } from "@/components/common/SnackBar/SnackbarProvider";
import { menuStyles } from "./ActionMenu.styles";

type ActionMenuProps = {
  row: any;
  onReload?: () => void;
  isSelf?: boolean;
};

const ActionMenu: React.FC<ActionMenuProps> = ({ isSelf, row, onReload }) => {
  const { has } = useScreenAccessDecision();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const router = useRouter();
  const handleOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const [confirmOpen, setConfirmOpen] = React.useState(false);
  const [pendingAction, setPendingAction] = React.useState<string | null>(null);
  const [canEdit, canDelete] = [has("update"), has("delete")];

  const millsheetImageUrl = "/images/millsheet/";
  const handleAction = (action: string) => {
    if (action === "Delete") {
      setPendingAction("Delete");
      setConfirmOpen(true);
      handleClose();
    } else if (action === "Edit") {
      router.push(`/user-master/edit?id=${row.clientId}`);
      // window.location.href = `/user-master/edit?id=${row.clientId}`;
      handleClose();
    } else {
      handleClose();
    }
  };

  const { showSnackbar } = useSnackbar();
  const handleConfirmDelete = async () => {
    try {
      await deleteUser(row.clientId);
      showSnackbar(
        "",
        `Delete user ${row.lastName || ""} ${row.firstName || ""} completed`,
        "success"
      );
      if (onReload) onReload();
    } catch (err) {}
    setConfirmOpen(false);
    handleClose();
    setPendingAction(null);
  };
  return (
    <>
      <Button
        disabled={[canEdit, canDelete].every((e) => !e)}
        variant="text"
        size="small"
        onClick={handleOpen}
      >
        <Image src="/images/icons/more.svg" alt="more" width={20} height={20} />
      </Button>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        transformOrigin={{ vertical: "top", horizontal: "right" }}
        sx={menuStyles}
      >
        {canEdit && (
          <MenuItem onClick={() => handleAction("Edit")}>
            <Image
              src={millsheetImageUrl + "pencil.svg"}
              alt="edit"
              width={15}
              height={15}
            />
            <Typography>Edit</Typography>
          </MenuItem>
        )}
        {!isSelf && canDelete && (
          <MenuItem onClick={() => handleAction("Delete")}>
            <Image
              src={millsheetImageUrl + "trash.svg"}
              alt="edit"
              width={15}
              height={15}
            />
            <Typography>Delete</Typography>
          </MenuItem>
        )}
      </Menu>
      <PopupModal
        open={confirmOpen}
        type="warning"
        title="Confirm Delete"
        description={`Are you sure you want to delete ${row.lastName || ""} ${
          row.firstName || ""
        }?`}
        onClose={() => {
          setConfirmOpen(false);
          setPendingAction(null);
        }}
        onSubmit={handleConfirmDelete}
      />
    </>
  );
};

export default ActionMenu;
