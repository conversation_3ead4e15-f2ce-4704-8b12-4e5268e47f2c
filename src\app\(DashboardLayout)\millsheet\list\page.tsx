"use client";
import PageContainer from "@/app/(DashboardLayout)/components/container/PageContainer";
import { Box, Dialog, DialogActions, DialogContent, DialogTitle, IconButton, Menu, MenuItem, Typography } from "@mui/material";
import type { FieldConfig, FieldOption } from "@/components/common/DynamicForm/DynamicForm.types";
import { useTranslation } from "react-i18next";
import Button from "@/components/common/button/Button";
import DynamicSearch from "@/components/common/DynamicSearch/DynamicSearch";
import DynamicDataGrid, { DynamicDataGridColumn } from "@/components/common/DynamicTable/DynamicDataGrid";
import { useBreadcrumb } from "@/features/breadcrumb/hook/useBreadcrumb";
import {
  FlagStatus,
  MillSheet,
  MillSheetBulkDownloadParams,
  MillSheetDownloadParams,
  MillSheetSearchParams,
} from "@/features/millsheet/types/millSheetTypes";
import * as MillSheetService from "@/services/millSheetService";
import dayjs from "dayjs";
import Image from "next/image";
import { useCallback, useEffect, useMemo, useState } from "react";
import { downloadBlob, getDateTimeSuffix } from "@/utils/common";
import PdfPreviewer from "@/components/common/PdfPreviewer";
import Page from "../page";
import PdfDialog from "@/components/common/PdfDialog";

const DEFAULT_PAGE_SIZE_INIT = 100;
const MillsheetList = () => {
  useBreadcrumb({
    pageTitle: "Mill Sheet",
    items: [
      { id: "millsheet", title: "Mill Sheet" },
      { id: "millsheet-list", title: "Mill Sheet List" },
    ],
  });
  const { t } = useTranslation("common");
  const [customerList, setCustomerList] = useState<FieldOption[]>([]);
  const [supplierList, setSuplierList] = useState<FieldOption[]>([]);
  useEffect(() => {
    let mounted = true;
    (async () => {
      try {
        const [customers, suppliers] = await Promise.all([MillSheetService.getCustomerList(), MillSheetService.getSuplierList()]);
        if (!mounted) return;
        setCustomerList(customers.map((c) => ({ label: c.customerName, value: c.customerId })));
        setSuplierList(suppliers.map((s) => ({ label: s.supplierName, value: s.supplierId })));
      } catch (e) {
        console.log(e);
      }
    })();
    return () => {
      mounted = false;
    };
  }, []);
  const fieldsSearch: FieldConfig[] = [
    {
      name: "mill",
      label: "Mill",
      placeholder: "Enter Mill",
      hiddenLabelNormal: true,
      type: "autocomplete",
      options: supplierList,
    },
    {
      name: "customerId",
      label: "Customer",
      placeholder: "Enter Customer",
      hiddenLabelNormal: true,
      type: "autocomplete",
      options: customerList,
    },
    { name: "invoiceNo", label: "Invoice No.", placeholder: "Enter Invoice number", hiddenLabelNormal: true, type: "text" },
    { name: "poNo", label: "Po No.", placeholder: "Enter Po No.", hiddenLabelNormal: true, type: "text" },
    {
      name: "serialNumber",
      label: "Serial Number",
      placeholder: "Enter Serial Number",
      hiddenLabelNormal: true,
      type: "text",
      labelTooltipContent: "The serial number may consist of Coil No., Product ID, Bundle No., or other types of identifiers.",
      labelTooltipProps: { placement: "right", enterDelay: 200 },
      labelTooltipIcon: <Image src="../images/millsheet/information.svg" alt="information" width={16} height={16}></Image>,
    },
    { name: "standard", label: "Standard", placeholder: "Enter Standard", hiddenLabelNormal: true, type: "text" },
    { name: "heatNo", label: "Heat No.", placeholder: "Enter Heat No.", hiddenLabelNormal: true, type: "text" },
    { name: "size", label: "Size", placeholder: "Enter Size", hiddenLabelNormal: true, type: "text" },
    {
      name: "weight",
      label: "Weight",
      placeholder: "Enter Weight",
      hiddenLabelNormal: true,
      type: "textRange",
      isNumberic: true,
    },
    { name: "issueDate", label: "Issue Date", placeholder: "Enter Issue Date", hiddenLabelNormal: true, type: "dateRange" },
  ];

  const Center = ({ children }: { children: React.ReactNode }) => (
    <Box sx={{ width: "100%", height: "100%", display: "flex", alignItems: "center", justifyContent: "center" }}>{children}</Box>
  );
  const columns: DynamicDataGridColumn<MillSheet>[] = [
    {
      key: "mill",
      label: "Mill",
      headerAlign: "center",
      align: "left",
      sortable: false,
    },
    {
      key: "customer",
      label: "Customer",
      headerAlign: "center",
      align: "left",
      sortable: false,
    },
    {
      key: "invoiceNo",
      label: "Invoice No.",
      headerAlign: "center",
      align: "left",
      sortable: false,
    },
    {
      key: "poNo",
      label: "PO No.",
      headerAlign: "center",
      align: "left",
      sortable: false,
    },
    {
      key: "serialNumber",
      label: "Serial Number",
      headerAlign: "center",
      align: "left",
      sortable: false,
    },
    {
      key: "standard",
      label: "Standard",
      headerAlign: "center",
      align: "left",
      sortable: false,
    },
    {
      key: "heatNo",
      label: "Heat No.",
      headerAlign: "center",
      align: "left",
      sortable: false,
    },
    {
      key: "size",
      label: "Size",
      headerAlign: "center",
      align: "left",
      sortable: false,
    },
    {
      key: "weight",
      label: "Weight",
      headerAlign: "center",
      align: "left",
      sortable: false,
    },
    {
      key: "dateOfIssue",
      label: "Date Of Issue",
      headerAlign: "center",
      align: "left",
      sortable: false,
    },
    {
      key: "pdfDownloadFlag",
      label: "PDF",
      headerAlign: "center",
      align: "center",
      width: 100,
      sortable: false,
      render: (row) => (
        <Center>
          <Image
            onClick={() => handleClickOpenDialog(row)}
            src={
              row.pdfDownloadFlag === FlagStatus.On ? "../images/millsheet/pdf-check.svg" : "../images/millsheet/pdf-uncheck.svg"
            }
            alt="metadata"
            width={24}
            height={24}
            style={{ display: "block" }}
          />
        </Center>
      ),
    },
    {
      key: "metadataDownloadFlag",
      label: "Metadata",
      headerAlign: "center",
      align: "center",
      width: 100,
      sortable: false,
      render: (row) => (
        <Center>
          <Image
          onClick={() => {} }
            src={
              row.metadataDownloadFlag === FlagStatus.On
                ? "../images/millsheet/metadata-check.svg"
                : "../images/millsheet/metadata-uncheck.svg"
            }
            alt="metadata"
            width={20}
            height={20}
            style={{ display: "block" }}
          />
        </Center>
      ),
    },
    {
      key: "isConfirmed",
      label: "Received",
      headerAlign: "center",
      align: "center",
      sortable: false,
      width: 100,
      render: (row) =>
        row.isConfirmed === FlagStatus.On && (
          <Center>
            <Image src="../images/millsheet/check-mark-circle.svg" alt="metadata" width={24} height={24} />
          </Center>
        ),
    },
  ];

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE_INIT);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<MillSheet[]>([]);
  const [selectedIds, setSelectedIds] = useState<(string | number)[]>([]);
  const [searchParams, setSearchParams] = useState<MillSheetSearchParams>({
    condition: { millSheetSearchFlag: 0 },
    sort: [],
    pagination: {},
  });
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const checkedRows = useMemo(() => {
    console.log("selectedIds", Array.from(selectedIds));
    return data.filter((r) => [...selectedIds].includes(r.id))
  }, [data, selectedIds]);
  const isShowMetadata = useMemo(() => checkedRows.some((r) => !!r.metadataContainerPath), [checkedRows]);
  const isShowPdf = useMemo(() => checkedRows.some((r) => !!r.pdfDownloadFlag), [checkedRows]);
  const open = Boolean(anchorEl);
  const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>) => setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);

  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handlePageSizeChange = useCallback((newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(1);
  }, []);

  const resetData = useCallback(() => {
    setData([]);
    setTotal(0);
    setPage(1);
    setPageSize(DEFAULT_PAGE_SIZE_INIT);
  }, []);

  const fetchData = useCallback(
    async (pageParam = page, pageSizeParam = pageSize, params = searchParams) => {
      try {
        setLoading(true);
        const res = await MillSheetService.getMillSheetList({
          ...params,
          condition: { ...params?.condition, millSheetSearchFlag: 0 },
          pagination: { pageNumber: pageParam, pageSize: pageSizeParam },
        });
        console.log("fetch Data", res);

        setData(res.items);
        setTotal(res.totalItem);
      } catch (error) {
        setLoading(false);
        resetData();
      } finally {
        setLoading(false);
      }
    },
    [page, pageSize, searchParams, resetData]
  );

  const handleSearch = (data: any) => {
    const { issueDate, weight, ...dataSearch } = data;
    console.log(dataSearch);
    setPage(1);
    const [fromIssueDate, toIssueDate] = issueDate || [];
    setSearchParams((prev) => ({
      ...prev,
      condition: {
        ...prev?.condition,
        ...dataSearch,
        fromWeight: weight?.from,
        toWeight: weight?.to,
        fromIssueDate: fromIssueDate ? dayjs(fromIssueDate).format("DD/MM/YYYY") : "",
        toIssueDate: toIssueDate ? dayjs(toIssueDate).format("DD/MM/YYYY") : "",
      },
    }));
  };

  const handleClear = useCallback(() => {
    setPage(1);
    setSearchParams((prev) => ({
      ...prev,
      condition: {},
    }));
  }, []);

  useEffect(() => {
    fetchData(page, pageSize, searchParams);
  }, [page, pageSize, searchParams, fetchData]);

  const handleDownload = (type: "all" | "pdf" | "meta") => {
    if (!checkedRows.length) return;
    const example = `%PDF-1.1
1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj
2 0 obj<</Type/Pages/Count 1/Kids[3 0 R]>>endobj
3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 300 200]/Contents 4 0 R/Resources<</Font<</F1 5 0 R>>>>>>endobj
4 0 obj<</Length 60>>stream
BT /F1 16 Tf 30 150 Td (Mill Sheet PDF Demo) Tj 0 -24 Td (Time: ${new Date().toISOString()}) Tj ET
endstream
endobj
5 0 obj<</Type/Font/Subtype/Type1/BaseFont/Helvetica>>endobj
xref
0 6
0000000000 65535 f 
0000000010 00000 n 
0000000055 00000 n 
0000000116 00000 n 
0000000273 00000 n 
0000000362 00000 n 
trailer<</Root 1 0 R/Size 6>>
startxref
440
%%EOF`;
    const down = (paths: any) => {
      console.log("Download files", paths);
      const blob = new Blob([example], { type: "application/pdf" });
      const supplierName = "supplierName";
      const extension = ".pdf";
      downloadBlob(blob, `Millsheet_${supplierName}_${getDateTimeSuffix()}${extension}`);
    };

    const isSingle = checkedRows.length === 1;
    if (type === "all") {
      const bulkAll = checkedRows.map((r) => ({
        millSheetId: r.id,
        pathFileMetadata: r.metadataContainerPath || "",
        pathFilePdf: r.pdfContainerPath || "",
      }));
      MillSheetService.bulkDownloadMillSheetFile(bulkAll)
        .then((res) => console.log(res))
        .catch(console.error);
      down(bulkAll);
      return;
    }

    const pathField = type === "meta" ? "metadataContainerPath" : "pdfContainerPath";
    const bulkKey = type === "meta" ? "pathFileMetadata" : "pathFilePdf";

    if (isSingle) {
      const row = checkedRows[0];
      const singleReq: MillSheetDownloadParams = {
        millSheetId: row.id,
        pathFile: (row as any)[pathField],
      };
      MillSheetService.downloadMillSheetFile(singleReq)
        .then((res) => {
          const { blob, fileName } = res;
          const ext = blob.type === "application/zip" ? ".zip" : fileName.includes(".") ? "" : ".pdf";
          const finalName = fileName + ext;
          downloadBlob(blob, finalName);
        })
        .catch(console.error);
    } else {
      const bulkPayload: MillSheetBulkDownloadParams[] = checkedRows.map((r) => ({
        millSheetId: r.id,
        [bulkKey]: (r as any)[pathField],
      })) as MillSheetBulkDownloadParams[];
      MillSheetService.bulkDownloadMillSheetFile(bulkPayload)
        .then((res) => console.log(res))
        .catch(console.error);
    }
  };

  useEffect(() => {
    console.log("checkedRows", checkedRows);
  }, [checkedRows]);

  const [openDialog, setOpenDialog] = useState(false);
  const [urlPdf, setUrlPdf] = useState("");
  const [millSheet, setMillSheet] = useState<MillSheet>();
  const handleClickOpenDialog = (millSheet: MillSheet) => {
    setOpenDialog(true);
    setMillSheet(millSheet);
    // MillSheetService.getPreviewPdfFile(millSheetId)
    //   .then((res) => {
    //     setUrlPdf(res.fileUrl);
    //     setOpenDialog(true);
    //   })
    //   .catch((err) => {
    //     console.log(err);
    //   });
  };

  const handleCloseDialog = (action: "cancel" | "down" | "confirm") => {
    switch (action) {
      case "down":
        if (millSheet !== undefined) {
          MillSheetService.downloadMillSheetFile({ millSheetId: millSheet.id, pathFile: millSheet.pdfContainerPath || "" })
            .then((res) => console.log(res))
            .catch(console.error);
        }
        break;

      case "confirm":
        if (millSheet !== undefined) {
          MillSheetService.confirmMillSheetById(millSheet.id);
        }
        break;
      case "cancel":
        setOpenDialog(false);
        break;
    }
  };

  const [numPages, setNumPages] = useState<number>(0);
  return (
    <PageContainer title="Mill Sheet List" description="this is Mill Sheet List">
      <DynamicSearch
        fields={fieldsSearch}
        onSubmit={handleSearch}
        onClear={handleClear}
        searchLabel={t("search")}
        clearLabel={t("clear Filter")}
        advancedLabel={"Hide Advanced Label"}
        infoContent={'Enter multiple values separated by "," to perform a multi-condition search.'}
      />
      <Box display="flex" justifyContent="flex-end" gap={3.25} mt={0.5} mb={2}>
        <Button
          variant="outlined"
          prefixIcon={<Image src="../images/millsheet/bulk-download.svg" alt="clear" width={20} height={20} />}
          onClick={handleOpenMenu}
          size="small"
          disabled={checkedRows.length === 0}
        >
          Bulk download
        </Button>
        <Menu
          id="basic-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
          transformOrigin={{ vertical: "top", horizontal: "right" }}
          slotProps={{
            list: {
              sx: {
                "& .MuiMenuItem-root:hover": {
                  backgroundColor: "rgba(225, 228, 234, 1)",
                },
              },
            },
          }}
        >
          {isShowPdf && <MenuItem onClick={() => handleDownload("pdf")}>PDF File</MenuItem>}
          {isShowMetadata && <MenuItem onClick={() => handleDownload("meta")}>Metadata</MenuItem>}
          {isShowMetadata && <MenuItem onClick={() => handleDownload("all")}>All files</MenuItem>}
        </Menu>
        <Button variant="outlined" size="small">
          Mill Sheet Edit
        </Button>
      </Box>
      <DynamicDataGrid<MillSheet>
        columns={columns}
        data={data}
        page={page}
        pageSize={pageSize}
        total={total}
        loading={loading}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        showCheckbox={data.length > 0}
        rowKey="id"
        onSelectRow={(ids) => {
          console.log(ids)
          return setSelectedIds(ids)
        }}
        resultPerPageOptions={[20, 50, 100]}
        selectedRows={[...selectedIds]}
      />
      <PdfDialog
        open={openDialog}
        onClose={(action) => handleCloseDialog(action)}
        numPages={numPages}
        setNumPages={setNumPages}
        // pdfUrl={urlPdf}
        pdfUrl="../files/sample.pdf"
        row={millSheet}
      ></PdfDialog>
    </PageContainer>
  );
};

export default MillsheetList;
