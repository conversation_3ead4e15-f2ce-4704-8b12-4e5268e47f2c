"use client";
import PageContainer from "@/app/(DashboardLayout)/components/container/PageContainer";
import Button from "@/components/common/button/Button";
import type {
  FieldConfig,
  FieldOption,
} from "@/components/common/DynamicForm/DynamicForm.types";
import DynamicSearch from "@/components/common/DynamicSearch/DynamicSearch";
import DynamicDataGrid, {
  DynamicDataGridColumn,
} from "@/components/common/DynamicTable/DynamicDataGrid";
import PdfDialog from "@/components/common/PdfDialog";
import { PAGE_TITLES } from "@/constants/routes";
import { useBreadcrumb } from "@/features/breadcrumb/hook/useBreadcrumb";
import { getCustomerList } from "@/features/customer/state/customerSelector";
import {
  ConditionSearch,
  FlagStatus,
  MappingColumnSort,
  MillSheet,
  MillSheetBulkDownloadParams,
  MillSheetDownloadParams,
  MillSheetSearchParams,
  PreviewPDF,
  Sort,
} from "@/features/millsheet/types/millSheetTypes";
import { selectUserProfile } from "@/features/users/state/usersSelectors";
import * as MillSheetService from "@/services/millSheetService";
import { ORG_TYPES } from "@/types/auth";
import { downloadBlob } from "@/utils/common";
import { useMillSheetQueryIds } from "@/utils/useFileIdParam";
import { Box, Menu, MenuItem } from "@mui/material";
import { GridSortModel } from "@mui/x-data-grid";
import dayjs from "dayjs";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { use, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";

const DEFAULT_PAGE_SIZE = 20;
const INITIAL_CONDITION: ConditionSearch = { viewMode: 0 };
const INITIAL_SORT: Sort[] = [];
const INITIAL_PAGINATION = { pageNumber: 1, pageSize: DEFAULT_PAGE_SIZE };
const INITIAL_SEARCH_PARAMS: MillSheetSearchParams = {
  condition: INITIAL_CONDITION,
  sort: INITIAL_SORT,
  pagination: INITIAL_PAGINATION,
};

type DownloadType = "all" | "pdf" | "meta";

const MillSheetList = () => {
  const { fileIdParam, fileIdArray, setFileIdArray, idParam, idArray, setIdArray } = useMillSheetQueryIds();
  useBreadcrumb({
    pageTitle: PAGE_TITLES.MILL_SHEET_LIST,
    items: [
      { id: "millsheet", title: PAGE_TITLES.MILL_SHEET },
      { id: "millsheet-list", title: PAGE_TITLES.MILL_SHEET_LIST },
    ],
  });

  const { t } = useTranslation("common");
  const router = useRouter();
  const userProfile = useSelector(selectUserProfile);
  const isCustomer =
    !!userProfile && userProfile.organizationGroupType === ORG_TYPES.EXTERNAL;
  const isManufacturer =
    !!userProfile && userProfile.organizationGroupType === ORG_TYPES.INTERNAL;
  // Lookup lists
  const customerList = useSelector(getCustomerList);
  const [supplierList, setSupplierList] = useState<FieldOption[]>([]);
  useEffect(() => {
    MillSheetService.getSuplierList().then((suppliers) => {
      setSupplierList(
        suppliers.map((s) => ({ label: s.supplierName, value: s.supplierId }))
      );
    });
  }, []);
  // Table & search states
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(DEFAULT_PAGE_SIZE);
  const [total, setTotal] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [loadingDownload, setLoadingDownload] = useState(false);
  const [data, setData] = useState<MillSheet[]>([]);
  const [selectedIds, setSelectedIds] = useState<(string | number)[]>([]);
  const [searchParams, setSearchParams] = useState<MillSheetSearchParams>(
    INITIAL_SEARCH_PARAMS
  );
  const [sortModel, setSortModel] = useState<GridSortModel>([]);

  // Dialog
  const [openDialog, setOpenDialog] = useState(false);
  const [millSheet, setMillSheet] = useState<MillSheet | undefined>();
  const [infoPdfFile, setInfoPdfFile] = useState<PreviewPDF | undefined>();
  const [numPages, setNumPages] = useState<number>(0);

  // Bulk menu
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const bulkDownloadRef = useRef<HTMLButtonElement>(null);
  const [bulkDownloadWidth, setBulkDownloadWidth] = useState<number>();

  // Derived
  const isDownloading = useRef(false);
  const isSorting = useRef(false);
  const isPreviewLoading = useRef(false);
  const isPageChanging = useRef(false);
  const [loadingSearch, setLoadingSearch] = useState(false);
  const [loadingClear, setLoadingClear] = useState(false);
  const checkedRows = useMemo(
    () => data.filter((r) => selectedIds.includes(r.id)),
    [data, selectedIds]
  );
  const isShowMetadata = useMemo(
    () => checkedRows.some((r) => !!r.metadataContainerPath),
    [checkedRows]
  );
  const isShowPdf = useMemo(
    () => checkedRows.some((r) => !!r.pdfContainerPath),
    [checkedRows]
  );
  const isDisabledBulkDownload =
    checkedRows.length === 0 || (!isShowPdf && !isShowMetadata);

  // Search field config
  const fieldsSearch = useMemo<FieldConfig[]>(() => {
    const full: FieldConfig[] = [
      {
        name: "mill",
        label: "Mill",
        placeholder: "Enter Mill",
        hiddenLabelNormal: true,
        type: "autocomplete",
        options: supplierList,
        maxLength: 255,
      },
      {
        name: "customerId",
        label: "Customer",
        placeholder: "Enter Customer",
        hiddenLabelNormal: true,
        type: "autocomplete",
        options: customerList.map((c) => ({
          label: c.customerName,
          value: c.customerId,
        })),
        maxLength: 255,
      },
      {
        name: "invoiceNo",
        label: "Invoice No.",
        placeholder: "Enter Invoice Number",
        hiddenLabelNormal: true,
        type: "text",
        maxLength: 50,
      },
      {
        name: "poNo",
        label: "PO No.",
        placeholder: "Enter PO Number",
        hiddenLabelNormal: true,
        type: "text",
        maxLength: 50,
      },
      {
        name: "serialNumber",
        label: "Serial Number",
        placeholder: "Enter Serial Number",
        hiddenLabelNormal: true,
        type: "text",
        labelTooltipContent:
          "The serial number may consist of Coil No., Product ID, Bundle No., or other types of identifiers.",
        labelTooltipProps: { placement: "right", enterDelay: 200 },
        labelTooltipIcon: (
          <Image
            src="../images/millsheet/information.svg"
            alt="information"
            width={16}
            height={16}
          />
        ),
      },
      {
        name: "heatNo",
        label: "Heat No.",
        placeholder: "Enter Heat Number",
        hiddenLabelNormal: true,
        type: "text",
        maxLength: 50,
      },
      {
        name: "standard",
        label: "Standard",
        placeholder: "Enter Standard",
        hiddenLabelNormal: true,
        type: "text",
      },
      {
        name: "size",
        label: "Size",
        placeholder: "Enter Size",
        hiddenLabelNormal: true,
        type: "text",
      },
      {
        name: "weight",
        label: "Weight",
        placeholder: "Enter Weight",
        hiddenLabelNormal: true,
        type: "textRange",
        isNumberic: true,
        allowDecimal: true,
        maxIntegerDigits: 7,
        maxDecimalDigits: 3,
        customValidation: (value) => {
          if (value) {
            const { from, to } = value;
            if (parseFloat(from) > parseFloat(to)) {
              return "From value cannot be greater than the To value";
            }
          }
          return true;
        },
      },
      {
        name: "issueDate",
        label: "Issue Date",
        placeholder: "Enter Issue Date",
        hiddenLabelNormal: true,
        type: "dateRange",
      },
    ];
    return isCustomer ? full.filter((f) => f.name !== "customerId") : full;
  }, [isCustomer, customerList, supplierList]);

  const Center = ({ children }: { children: React.ReactNode }) => (
    <Box
      sx={{
        width: "100%",
        height: "100%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      {children}
    </Box>
  );

  const handleClickOpenDialog = useCallback((row: MillSheet, type: 0 | 1) => {
    if (isPreviewLoading.current) {
      return;
    }
    isPreviewLoading.current = true;
    setMillSheet(row);
    setInfoPdfFile(undefined);
    MillSheetService.getPreviewPdfFile({
      id: row.id,
      fileType: type,
      status: row.status,
    })
      .then((res) => {
        setInfoPdfFile(res);
        setOpenDialog(true);
      })
      .catch(() => setOpenDialog(false))
      .finally(() => {
        isPreviewLoading.current = false;
      });
  }, []);

  const closeMenu = useCallback(() => {
    if (anchorEl) setAnchorEl(null);
  }, [anchorEl]);
  const handleOpenMenu = (e: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(e.currentTarget);
    if (bulkDownloadRef.current)
      setBulkDownloadWidth(bulkDownloadRef.current.offsetWidth);
  };

  const handleDownload = useCallback(
    async (type: DownloadType, singleRow?: MillSheet) => {
      closeMenu();
      const targetRows = singleRow ? [singleRow] : checkedRows;
      if (!targetRows.length) return;

      const handleSetDataClient = (typeData: DownloadType) => {
        const clientData = data.map((d) => {
            return {
              ...d,
              pdfDownloadFlag:
                (typeData === "pdf" || typeData === "all") &&
                !!d.pdfContainerPath && (d.pdfContainerPath === singleRow?.pdfContainerPath || checkedRows.some((c) => c.pdfContainerPath === d.pdfContainerPath)) &&
                d.pdfDownloadFlag !== FlagStatus.On
                  ? FlagStatus.On
                  : d.pdfDownloadFlag,
              metadataDownloadFlag:
                (typeData === "meta" || typeData === "all") &&
                !!d.metadataContainerPath && (d.metadataContainerPath === singleRow?.metadataContainerPath || checkedRows.some((c) => c.metadataContainerPath === d.metadataContainerPath)) &&
                d.metadataDownloadFlag !== FlagStatus.On
                  ? FlagStatus.On
                  : d.metadataDownloadFlag,
          }
        });
        setData(clientData);
      };

      try {
        setLoadingDownload(true);
        if (isDownloading.current) {
          return;
        }
        isDownloading.current = true;
        if (type === "all") {
          const payload = targetRows
            .map((r) => ({
              millSheetId: r.id,
              pathFiles: [r.pdfContainerPath, r.metadataContainerPath].filter(
                Boolean
              ),
            }))
            .filter((p) => p.pathFiles.length > 0);
          const res = await MillSheetService.bulkDownloadMillSheetFile(payload);
          downloadBlob(res.file, res.fileName);
          handleSetDataClient(type);
          return;
        }

        const pathField =
          type === "meta" ? "metadataContainerPath" : "pdfContainerPath";
        if (targetRows.length === 1) {
          const r = targetRows[0];
          const req: MillSheetDownloadParams = {
            millSheetId: r.id,
            pathFile: (r as any)[pathField],
          };
          const res = await MillSheetService.downloadMillSheetFile(req);

          downloadBlob(res.file, res.fileName);
          handleSetDataClient(type);
        } else {
          const bulk: MillSheetBulkDownloadParams[] = targetRows
            .map((r) => ({
              millSheetId: r.id,
              pathFiles: [(r as any)[pathField]].filter(Boolean),
            }))
            .filter((p) => p.pathFiles.length > 0);
          const res = await MillSheetService.bulkDownloadMillSheetFile(bulk);
          downloadBlob(res.file, res.fileName);
          handleSetDataClient(type);
        }
      } catch (error) {
        setLoadingDownload(false);
        isDownloading.current = false;
      } finally {
        setLoadingDownload(false);
        isDownloading.current = false;
      }
    },
    [checkedRows, closeMenu, data, selectedIds]
  );

  const columns = useMemo<DynamicDataGridColumn<MillSheet>[]>(() => {
    const base: DynamicDataGridColumn<MillSheet>[] = [
      {
        key: "mill",
        label: "Mill",
        headerAlign: "center",
        align: "left",
        flex: 1,
      },
      {
        key: "customer",
        label: "Customer",
        headerAlign: "center",
        align: "left",
        flex: 1,
      },
      {
        key: "invoiceNo",
        label: "Invoice No.",
        headerAlign: "center",
        align: "left",
        flex: 0.9,
      },
      {
        key: "poNo",
        label: "PO No.",
        headerAlign: "center",
        align: "left",
        flex: 0.8,
      },
      {
        key: "serialNumber",
        label: "Serial Number",
        headerAlign: "center",
        align: "left",
        flex: 0.9,
      },
      {
        key: "heatNo",
        label: "Heat No.",
        headerAlign: "center",
        align: "left",
        flex: 0.8,
      },
      {
        key: "standard",
        label: "Standard",
        headerAlign: "center",
        align: "left",
        flex: 1,
      },
      {
        key: "size",
        label: "Size",
        headerAlign: "center",
        align: "right",
        flex: 0.8,
      },
      {
        key: "weight",
        label: "Weight (kg)",
        headerAlign: "center",
        align: "right",
        flex: 0.8,
      },
      {
        key: "dateOfIssue",
        label: "Date Of Issue",
        headerAlign: "center",
        align: "left",
        flex: 0.95,
      },
      {
        key: "pdfDownloadFlag",
        label: "PDF",
        headerAlign: "center",
        align: "center",
        flex: 0.5,
        sortable: false,
        render: (row) =>
          !!row.pdfContainerPath && (
            <Center>
              <Image
                onClick={() => handleClickOpenDialog(row, 0)}
                src={
                  row.pdfDownloadFlag === FlagStatus.On
                    ? "../images/millsheet/pdf-check.svg"
                    : "../images/millsheet/pdf-uncheck.svg"
                }
                alt="pdf"
                width={24}
                height={24}
                style={{ display: "block", cursor: "pointer" }}
              />
            </Center>
          ),
      },
      {
        key: "metadataDownloadFlag",
        label: "Metadata",
        headerAlign: "center",
        align: "center",
        flex: 0.65,
        sortable: false,
        render: (row) =>
          !!row.metadataContainerPath && (
            <Center>
              <Image
                onClick={() => handleDownload("meta", row)}
                src={
                  row.metadataDownloadFlag === FlagStatus.On
                    ? "../images/millsheet/metadata-check.svg"
                    : "../images/millsheet/metadata-uncheck.svg"
                }
                alt="metadata"
                width={24}
                height={24}
                style={{ display: "block", cursor: "pointer" }}
              />
            </Center>
          ),
      },
      {
        key: "isConfirmed",
        label: "Received",
        headerAlign: "center",
        align: "center",
        flex: 0.65,
        render: (row) =>
          row.isConfirmed === FlagStatus.On && (
            <Center>
              <Image
                src="../images/millsheet/check-mark-circle.svg"
                alt="received"
                width={24}
                height={24}
              />
            </Center>
          ),
      },
    ];
    return isManufacturer
      ? base.filter((c) => c.key !== "isConfirmed")
      : isCustomer
      ? base.filter((c) => c.key !== "customer")
      : base;
  }, [isCustomer, isManufacturer, handleClickOpenDialog, handleDownload]);

  const buildCondition = useCallback(
    (form: any): ConditionSearch => {
      const { issueDate, weight, ...rest } = form || {};
      const [fromIssueDate, toIssueDate] = issueDate || [];
      const base: any = {
        ...INITIAL_CONDITION,
        ...rest,
        fromWeight: weight?.from,
        toWeight: weight?.to,
        fromIssueDate: fromIssueDate
          ? dayjs(fromIssueDate).format("DD/MM/YYYY")
          : "",
        toIssueDate: toIssueDate ? dayjs(toIssueDate).format("DD/MM/YYYY") : "",
      };
      // Nếu có fileId trên URL, thêm vào condition
      if (fileIdArray && fileIdArray.length > 0) {
        base.fileId = fileIdArray;
      }
      if (idArray && idArray.length > 0) {
        base.millSheetId = idArray;
      }
      return base;
    },
    [fileIdArray, idArray]
  );

  function isStartAfterEnd(startStr: string, endStr: string): boolean {
    const format = "DD/MM/YYYY";
    const start = dayjs(startStr, format, true);
    const end = dayjs(endStr, format, true);
    if (!start.isValid() || !end.isValid()) return false;
    return start.isAfter(end, "day");
  }

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      // Chỉ truyền fileId vào condition nếu thực sự có file_id trên URL
      let baseCondition = searchParams.condition || {};
      if (fileIdParam && fileIdArray && fileIdArray.length > 0) {
        baseCondition = { ...baseCondition, fileId: fileIdArray };
      } else if (baseCondition.fileId) {
        const { fileId, ...rest } = baseCondition;
        baseCondition = rest;
      }

      if (idParam && idArray && idArray.length > 0) {
        baseCondition = { ...baseCondition, millSheetId: idArray };
      } else if (baseCondition.millSheetId) {
        const { millSheetId, ...rest } = baseCondition;
        baseCondition = rest;
      }
      const { fromIssueDate, toIssueDate } = baseCondition as ConditionSearch;
      const isInvalidDate =
        fromIssueDate &&
        toIssueDate &&
        isStartAfterEnd(fromIssueDate, toIssueDate);
      const finalCondition = {
        ...baseCondition,
        fromIssueDate: isInvalidDate ? "" : fromIssueDate,
        toIssueDate: isInvalidDate ? "" : toIssueDate,
      };
      const res = await MillSheetService.getMillSheetList({
        ...searchParams,
        condition: finalCondition,
        pagination: { pageNumber: page, pageSize },
      });
      setData(res.items as MillSheet[]);
      setTotal(res.totalItem);
    } catch {
      setData([]);
      setTotal(0);
      setLoadingSearch(false);
      setLoadingClear(false);
      setLoading(false);
    } finally {
      setLoading(false);
      setLoadingSearch(false);
      setLoadingClear(false);
      isSorting.current = false;
      isPageChanging.current = false;
    }
  }, [page, pageSize, searchParams, fileIdParam, fileIdArray, idParam, idArray]);

  useEffect(() => {
    if (fileIdParam || idParam) {
      setLoadingSearch(true);
      setPage(1);
      setSelectedIds([]);
      setSearchParams((prev) => ({
        ...prev,
        condition: buildCondition({}),
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fileIdParam, idParam]);

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams, page, pageSize]);

  const handleSearch = useCallback(
    (form: any) => {
      setLoadingClear(false);
      setLoadingSearch(true);
      setPage(1);
      setSelectedIds([]);
      setSearchParams((prev) => ({
        ...prev,
        condition: buildCondition(form),
      }));
    },
    [buildCondition]
  );

  const handleClear = useCallback(() => {
    if (fileIdParam || idParam) {
      const url = new URL(window.location.href);
      if (fileIdParam) url.searchParams.delete("file_id");
      if (idParam) url.searchParams.delete("id");
      router.replace(url.pathname + url.search);
      if (fileIdParam) setFileIdArray(undefined);
      if (idParam) setIdArray(undefined);
    }
    setLoadingSearch(false);
    setLoadingClear(true);
    setSortModel([]);
    setSearchParams(() => JSON.parse(JSON.stringify(INITIAL_SEARCH_PARAMS)));
    setPage(1);
    setSelectedIds([]);
  }, [fileIdParam, idParam, router, setFileIdArray, setIdArray]);

  const handlePageChange = useCallback((newPage: number) => {
    if (isPageChanging.current) return;
    setPage((prev) => {
      if (prev !== newPage) {
        setSelectedIds([]);
        isPageChanging.current = true;
        return newPage;
      }
      return prev;
    });
  }, []);
  const handlePageSizeChange = useCallback((newSize: number) => {
    setPageSize(newSize);
    setPage(1);
    setSelectedIds([]);
  }, []);

  const handleSortModelChange = useCallback((model: GridSortModel) => {
    if (isSorting.current) {
      return;
    }
    isSorting.current = true;
    setSortModel(model);
    setPage(1);
    setSelectedIds([]);

    if (model.length === 0) {
      setSearchParams((prev) => ({ ...prev, sort: [] }));
      return;
    }
    const sort: Sort[] = model.map(({ field, sort }) => ({
      sortColumn: MappingColumnSort[field as keyof typeof MappingColumnSort],
      sortDirection: (sort ?? "asc") as "asc" | "desc",
    }));
    setSearchParams((prev) => ({ ...prev, sort }));
  }, []);

  const handleCloseDialog = useCallback(
    async (action: "cancel" | "down" | "confirm") => {
      if (!millSheet) {
        setOpenDialog(false);
        return;
      }
      if (action === "cancel") {
        setOpenDialog(false);
        return;
      }
      try {
        if (action === "down") {
          await handleDownload("pdf", millSheet);
        } else if (action === "confirm") {
          await MillSheetService.confirmMillSheetById({
            id: millSheet.id,
            status: millSheet.status,
          });
          await fetchData();
        }
      } finally {
        setOpenDialog(false);
      }
    },
    [millSheet, handleDownload, fetchData]
  );

  return (
    <PageContainer
      title={PAGE_TITLES.MILL_SHEET_LIST}
      description={"this is " + PAGE_TITLES.MILL_SHEET_LIST}
    >
      <Box
        sx={{
          ".form-search-control": {
            marginBottom: "10px !important",
          },
        }}
      >
        <DynamicSearch
          loadingSearch={loadingSearch}
          loadingClear={loadingClear}
          fields={fieldsSearch}
          onSubmit={handleSearch}
          onClear={handleClear}
          searchLabel={t("search")}
          clearLabel={t("clear Filter")}
          advancedLabel={"Hide Advanced Filters"}
          infoContent={
            'Enter multiple values separated by "," to perform a multi-condition search.'
          }
        />
      </Box>
      <Box display="flex" justifyContent="flex-end" gap={3} mt={1.25} mb={1.25}>
        <Button
          ref={bulkDownloadRef}
          variant="outlined"
          isLoading={loadingDownload}
          prefixIcon={
            isDisabledBulkDownload ? (
              <Image
                src="../images/millsheet/bulk-download-disabled.svg"
                alt="clear"
                width={20}
                height={20}
              />
            ) : (
              <Image
                src="../images/millsheet/bulk-download.svg"
                alt="clear"
                width={20}
                height={20}
              />
            )
          }
          onClick={handleOpenMenu}
          size="small"
          disabled={isDisabledBulkDownload}
        >
          Bulk download
        </Button>
        <Menu
          id="bulk-download-menu"
          anchorEl={anchorEl}
          open={!!anchorEl}
          onClose={closeMenu}
          slotProps={{
            paper: { sx: { width: bulkDownloadWidth } },
            list: {
              sx: {
                "& .MuiMenuItem-root:hover": {
                  backgroundColor: "rgba(225, 228, 234, 1)",
                },
              },
            },
          }}
        >
          {[
            isShowPdf && { type: "pdf", label: "PDF file" },
            isShowMetadata && { type: "meta", label: "Metadata" },
            isShowPdf && isShowMetadata && { type: "all", label: "All files" },
          ]
            .filter(Boolean)
            .map((item) => {
              const menu = item as { type: string; label: string };
              return (
                <MenuItem
                  key={menu.type}
                  onClick={() => handleDownload(menu.type as DownloadType)}
                >
                  {menu.label}
                </MenuItem>
              );
            })}
        </Menu>
        {isManufacturer && (
          <Button
            prefixIcon={
              <Image
                src="../images/millsheet/pencil.svg"
                alt="edit"
                width={18}
                height={18}
              />
            }
            variant="outlined"
            size="small"
            onClick={() => router.push("/millsheet/modification")}
          >
            {PAGE_TITLES.MILL_SHEET_EDIT}
          </Button>
        )}
      </Box>
      <DynamicDataGrid<MillSheet>
        columns={columns}
        data={data}
        page={page}
        pageSize={pageSize}
        total={total}
        itemLabel={"results"}
        loading={loading}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        showCheckbox={data.length > 0}
        rowKey="id"
        onSelectRow={(ids) => setSelectedIds(ids)}
        resultPerPageOptions={[10, 20, 50, 100]}
        selectedRows={selectedIds}
        onSortModelChange={handleSortModelChange}
        sortModel={sortModel}
      />
      {infoPdfFile && (
        <PdfDialog
          key={infoPdfFile.fileUrl}
          open={openDialog}
          onClose={handleCloseDialog}
          numPages={numPages}
          setNumPages={setNumPages}
          infoPdfFile={infoPdfFile}
          isCustomer={isCustomer}
        />
      )}
    </PageContainer>
  );
};

export default MillSheetList;
