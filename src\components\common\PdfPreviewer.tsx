"use client";
import { Box } from "@mui/material";
import { useEffect, useRef, useState } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import "react-pdf/dist/Page/AnnotationLayer.css";
import "react-pdf/dist/Page/TextLayer.css";

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

interface PdfPreviewerProps {
  pdfUrl: string;
  onLoadSuccess?: (numPages: number) => void;
  onPageChange?: (currentPage: number) => void;
}

export default function PdfPreviewer({ pdfUrl, onLoadSuccess, onPageChange }: PdfPreviewerProps) {
  const [numPages, setNumPages] = useState<number>(1);
  const [readyUrl, setReadyUrl] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (pdfUrl && pdfUrl.trim() !== "") {
      setReadyUrl(pdfUrl);
    }
  }, [pdfUrl]);

  function onDocumentLoadSuccess({ numPages }: { numPages: number }): void {
    setNumPages(numPages);
    if (onLoadSuccess) {
      onLoadSuccess(numPages);
    }
  }

  // Detect current page on scroll
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const pages = Array.from(container.querySelectorAll(".react-pdf__Page"));
      let currentPage = 1;
      for (let i = 0; i < pages.length; i++) {
        const rect = pages[i].getBoundingClientRect();
        if (rect.top + rect.height / 2 > 0) {
          currentPage = i + 1;
          break;
        }
      }
      if (onPageChange) onPageChange(currentPage);
    };

    container.addEventListener("scroll", handleScroll);
    handleScroll(); // initial

    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, [numPages, onPageChange]);

  if (!readyUrl) {
    return (
      <Box
        sx={{
          minHeight: 400,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        Loading PDF...
      </Box>
    );
  }

  return (
    <Box
      ref={containerRef}
      sx={{
        maxHeight: "calc(100vh - 160px)",
        overflowY: "auto",
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
      }}
      className="pdf-preview-file"
    >
      <Document file={readyUrl} loading="Loading PDF..." onLoadSuccess={onDocumentLoadSuccess}>
        {Array.from(new Array(numPages), (_, index) => (
          <Page key={`page_${index + 1}`} pageNumber={index + 1} />
        ))}
      </Document>
    </Box>
  );
}