export type NotificationProps = {
  externalUnreadCount?: number; // number of unread notifications managed externally (e.g. realtime push)
  onUnreadChange?: (count: number) => void; // callback to notify parent when unread count changes locally
};
export type Notification = {
  id: string;
  title: string;
  tag: string;
  link: string;
  icon: string;
  content: string;
  createdAt: Date;
  isRead: boolean;
};
