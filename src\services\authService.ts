// src/services/authService.ts
import {
  loginUser,
  registerUser,
  logoutUser,
  refreshTokenRequest,
  forgotPasswordRequest,
  resetPasswordRequest,
  changePasswordRequest,
} from "@/features/auth/api/authApi";
import {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
} from "@/types/auth";

export const login = async (
  credentials: LoginRequest
): Promise<LoginResponse> => {
  return loginUser(credentials);
};

export const register = async (
  userData: RegisterRequest
): Promise<RegisterResponse> => {
  return registerUser(userData);
};

export const logout = async (): Promise<void> => {
  return logoutUser();
};

export const refreshToken = async (): Promise<{
  accessToken: string;
  refreshToken: string;
}> => {
  return refreshTokenRequest();
};

export const forgotPassword = async (email: string): Promise<void> => {
  return forgotPasswordRequest(email);
};

export const resetPassword = async (
  token: string,
  newPassword: string
): Promise<void> => {
  return resetPasswordRequest(token, newPassword);
};

export const changePassword = async (
  clientId: string | undefined,
  userId: string | undefined,
  oldPassword: string,
  newPassword: string,
  confirmNewPassword: string
): Promise<any> => {
  return changePasswordRequest({
    clientId,
    userId,
    oldPassword,
    newPassword,
    confirmNewPassword,
  });
};
