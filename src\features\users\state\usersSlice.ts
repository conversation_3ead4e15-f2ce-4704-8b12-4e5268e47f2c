// src/features/users/state/usersSlice.ts
import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { User } from '@/types/auth';
import { Pagination } from '@/types/api';
import {UserMaster}from"@/features/users/types/user"


interface UsersState {
  users: User[];
  userProfile: User | null;
  userMasterDetail: UserMaster | null;
  userAccessMenus: any[];
  isLoading: boolean;
  error: string | null;
  pagination: Pagination;
  search: string;
}

const initialState: UsersState = {
  users: [],
  userProfile: null,
  userMasterDetail: null,
  userAccessMenus: [],
  isLoading: false,
  error: null,
  pagination: { page: 1, pageSize: 10, total: 0, totalPages: 0 },
  search: '',
};


const usersSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    setUserMasterDetail(state, action: PayloadAction<UserMaster | null>) {
      state.userMasterDetail = action.payload;
    },
    setUserAccessMenus(state, action: PayloadAction<any[]>) {
      state.userAccessMenus = action.payload;
    },
    setUsers(state, action: PayloadAction<User[]>) {
      state.users = action.payload;
    },
    setUserProfile(state, action: PayloadAction<User | null>) {
      state.userProfile = action.payload;
    },
    setPagination(state, action: PayloadAction<Pagination>) {
      state.pagination = action.payload;
    },
    setSearch(state, action: PayloadAction<string>) {
      state.search = action.payload;
    },
    setError(state, action: PayloadAction<string | null>) {
      state.error = action.payload;
    },
    setLoading(state, action: PayloadAction<boolean>) {
      state.isLoading = action.payload;
    },
  },
});

export const { setUsers, setUserProfile, setUserMasterDetail, setUserAccessMenus, setPagination, setSearch, setError, setLoading } = usersSlice.actions;
export default usersSlice.reducer;
