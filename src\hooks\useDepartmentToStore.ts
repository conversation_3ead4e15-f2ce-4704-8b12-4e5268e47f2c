import { department, getDepartmentListByOrganizationCode } from "@/features/department/state/departmentSlice";
import { useEffect } from "react";
import { useDispatch } from "react-redux";

export function useDepartmentToStore(organizationCode: string) {
  const dispatch = useDispatch();
  useEffect(() => {
    if (!organizationCode) {
      return;
    }
    dispatch(department(organizationCode) as any);
  }, [organizationCode, dispatch]);
}

export function useTempDepartmentToStore(organizationCode: string) {
  const dispatch = useDispatch();
  useEffect(() => {
    if (!organizationCode) {
      return;
    }
    dispatch(getDepartmentListByOrganizationCode(organizationCode) as any);
  }, [dispatch, organizationCode]);
}
