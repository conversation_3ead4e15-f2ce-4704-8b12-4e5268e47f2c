import React from "react";
import { <PERSON><PERSON>, <PERSON>, Chip, Stack, Typography } from "@mui/material";
import Image from "next/image";
import { decodeHtmlUrl, hasAllowedExtension, withinSize } from "@/utils/common";

type ProfileHeaderProps = {
  name: string;
  roleName: string;
  userId: string;
  lockStatus: boolean;
  avatarUrl?: string;
  onUpload?: (formData: FormData) => void | Promise<void>;
  acceptExts?: string[];
  maxSizeMb?: number;
  mode?: "view" | "edit";
};

const DEFAULT_EXTS = [".png", ".jpg", ".gif"];
const DEFAULT_MAX_MB = 5;

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  name,
  roleName,
  userId,
  lockStatus,
  avatarUrl,
  onUpload,
  acceptExts = DEFAULT_EXTS,
  maxSizeMb = DEFAULT_MAX_MB,
  mode = "view",
}) => {
  console.log('avatarUrl', avatarUrl)
  const [hover, setHover] = React.useState(false);
  const [imageError, setImageError] = React.useState(false);
  const [imageErrorText, setImageErrorText] = React.useState("");

  // ensure unique input id if multiple components rendered
  const uniqueId = React.useId();
  const inputId = `avatar-upload-${uniqueId}`;
  const acceptAttr = React.useMemo(() => acceptExts.join(","), [acceptExts]);

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!hasAllowedExtension(file, acceptExts)) {
      setImageError(true);
      setImageErrorText("Select a file in JPG, PNG, or GIF format.");
      e.currentTarget.value = "";
      return;
    }
    if (!withinSize(file, maxSizeMb)) {
      setImageError(true);
      setImageErrorText(`File size exceeds ${maxSizeMb}MB`);
      e.currentTarget.value = "";
      return;
    }

    setImageError(false);
    setImageErrorText("");

    const formData = new FormData();
    formData.append("file", file);
    await onUpload?.(formData);
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        mb: mode === "edit"? "4px" : "20px",
      }}
    >
      <Box sx={{ display: "flex", gap: "16px" }}>
        {mode === "edit" ? (
          <Box
            sx={{
              width: "115px",
              height: "115px",
              borderRadius: "10px",
              border: "1px solid rgba(162, 161, 168, 0.20)",
              background: "rgba(162, 161, 168, 0.05)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
              position: "relative",
              overflow: "hidden",
              transition: "background 0.2s, border-color 0.2s",
              "&:hover": {
                background: "rgba(162, 161, 168, 0.15)",
                borderColor: "rgba(162, 161, 168, 0.40)",
              },
            }}
            onMouseEnter={() => setHover(true)}
            onMouseLeave={() => setHover(false)}
          >
            <input type="file" accept={acceptAttr} style={{ display: "none" }} id={inputId} onChange={handleImageChange} />
            <label
              htmlFor={inputId}
              style={{
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
              }}
            >
              {avatarUrl ? (
                <>
                  <Avatar
                    src={decodeHtmlUrl(avatarUrl ?? "")}
                    alt="Avatar"
                    sx={{ width: "100%", height: "100%", borderRadius: "10px" }}
                  />
                  {hover && (
                    <Box
                      sx={{
                        position: "absolute",
                        top: 0,
                        left: 0,
                        width: "100%",
                        height: "100%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        background: "rgba(255,255,255,0.5)",
                        borderRadius: "10px",
                        pointerEvents: "none",
                      }}
                    >
                      <svg width="36" height="36" viewBox="0 0 36 36" fill="none">
                        <circle cx="18" cy="18" r="18" fill="#E4E9F2" />
                        <path
                          d="M24 23.25C24 24.2165 23.2165 25 22.25 25H13.75C12.7835 25 12 24.2165 12 23.25V16.75C12 15.7835 12.7835 15 13.75 15H15.25L16.25 13H19.75L20.75 15H22.25C23.2165 15 24 15.7835 24 16.75V23.25ZM18 22C19.1046 22 20 21.1046 20 20C20 18.8954 19.1046 18 18 18C16.8954 18 16 18.8954 16 20C16 21.1046 16.8954 22 18 22Z"
                          fill="#8F9BB3"
                        />
                      </svg>
                    </Box>
                  )}
                </>
              ) : (
                <svg width="36" height="36" viewBox="0 0 36 36" fill="none">
                  <circle cx="18" cy="18" r="18" fill="#E4E9F2" />
                  <path
                    d="M24 23.25C24 24.2165 23.2165 25 22.25 25H13.75C12.7835 25 12 24.2165 12 23.25V16.75C12 15.7835 12.7835 15 13.75 15H15.25L16.25 13H19.75L20.75 15H22.25C23.2165 15 24 15.7835 24 16.75V23.25ZM18 22C19.1046 22 20 21.1046 20 20C20 18.8954 19.1046 18 18 18C16.8954 18 16 18.8954 16 20C16 21.1046 16.8954 22 18 22Z"
                    fill="#8F9BB3"
                  />
                </svg>
              )}
            </label>
          </Box>
        ) : (
          <Avatar
            src={decodeHtmlUrl(avatarUrl ?? "")}
            alt={`Avatar of ${name}`}
            sx={{
              width: "115px",
              height: "115px",
              borderRadius: 2,
              top: 0,
              left: 0,
            }}
          />
        )}

        <Stack direction="column" sx={{ justifyContent: "center" }} spacing={2.25}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: "bold",
              color: "#16151c",
              fontFamily: "Roboto, sans-serif",
              fontSize: 32,
              lineHeight: 1.2,
              whiteSpace: "nowrap",
            }}
          >
            {name}
          </Typography>
          <Stack direction="column" spacing={1.25}>
            <Stack direction="row" spacing={2.5} alignItems="center">
              <Image src="/images/user/brief.svg" alt="brief" width={24} height={24} />
              <Typography
                sx={{
                  color: "#16151c",
                  fontFamily: "Roboto, sans-serif",
                  fontSize: 16,
                  whiteSpace: "nowrap",
                }}
              >
                {roleName}
              </Typography>
            </Stack>
            <Stack direction="row" spacing={2.5} alignItems="center">
              <Image src="/images/user/gmail.svg" alt="gmail" width={24} height={24} />
              <Typography
                sx={{
                  color: "#16151c",
                  fontFamily: "Roboto, sans-serif",
                  fontSize: 16,
                  whiteSpace: "nowrap",
                }}
              >
                {userId}
              </Typography>
            </Stack>
          </Stack>
        </Stack>

        <Box sx={{ position: "relative", display: "inline-block", mt: 0.5 }}>
          <Chip
            label={lockStatus ? "Active" : "Inactive"}
            color={lockStatus ? "success" : "default"}
            sx={{
              backgroundColor: lockStatus ? "rgba(52, 199, 89, 1)" : "rgba(209, 213, 219, 1)",
              color: lockStatus ? "#fff" : "#222",
            }}
            size="medium"
          />
        </Box>
      </Box>
      {mode === "edit" && (
        <Box sx={{ mt: 1, minHeight: 18 }}>
          <Typography variant="caption" color="#CA002E" sx={{ visibility: imageError ? "visible" : "hidden" }}>
            {imageErrorText}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default ProfileHeader;
