'use client';
import { Typography } from '@mui/material';
import PageContainer from '@/app/(DashboardLayout)/components/container/PageContainer';
import DashboardCard from '@/app/(DashboardLayout)/components/shared/DashboardCard';

import { useTranslation } from 'react-i18next';
import type { FieldConfig } from '@/components/common/DynamicForm/DynamicForm.types';

import DynamicSearch from '@/components/common/DynamicSearch/DynamicSearch';
import DynamicTableSamplePage from '@/components/common/DynamicTable/DynamicTableSamplePage'
const MillsheetUpload = () => {
  const { t } = useTranslation('common'); // 'common' là namespace

  // Sample fields for DynamicSearch
  const fields: FieldConfig[] = [
    // Row 1
    { name: 'keyword', label: 'Keyword', type: 'text', placeholder: 'Enter keyword...' },
    { name: 'status', label: 'Status', type: 'select', options: [
      { label: 'All', value: '' },
      { label: 'Active', value: 'active' },
      { label: 'Inactive', value: 'inactive' },
    ] },
    { name: 'from', label: 'From', type: 'date' },
    { name: 'to', label: 'To', type: 'date' },
    // Row 2
    { name: 'username', label: 'Username', type: 'text', placeholder: 'Enter username...' },
    { name: 'role', label: 'Role', type: 'select', options: [
      { label: 'Admin', value: 'admin' },
      { label: 'User', value: 'user' },
      { label: 'Guest', value: 'guest' },
    ] },
    { name: 'active', label: 'Active', type: 'checkbox' },
    { name: 'type', label: 'Type', type: 'radio', options: [
      { label: 'A', value: 'A' },
      { label: 'B', value: 'B' },
    ] },
    // Row 3
    { name: 'email', label: 'Email', type: 'email', placeholder: 'Enter email...' },
    { name: 'phone', label: 'Phone', type: 'text', placeholder: 'Enter phone...' },
    { name: 'note', label: 'Note', type: 'textarea', placeholder: 'Enter note...' },
    { name: 'file', label: 'File', type: 'file' },
  ];

  const handleSearch = (data: any) => {
    // Xử lý search ở đây
    console.log('Search data:', data);
  };
  const handleClear = () => {
    // Xử lý clear filter ở đây
    console.log('Clear filter');
  };

  return (
    <PageContainer title="Sample Page" description="this is Sample page">
      <DynamicSearch
        fields={fields}
        onSubmit={handleSearch}
        onClear={handleClear}
        searchLabel={t('search')}
        clearLabel={t('clearFilter')}
        advancedLabel={'Hide Advanced Label'}
      />
      <DashboardCard title="Sample Page">
        <Typography>{t('samplePage')}</Typography>
      <DynamicTableSamplePage/>
      </DashboardCard>
    </PageContainer>
  );
};

export default MillsheetUpload;