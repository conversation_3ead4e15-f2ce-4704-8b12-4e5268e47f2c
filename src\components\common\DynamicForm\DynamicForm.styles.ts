import { SxProps, Theme } from "@mui/material";

export const formSx: SxProps<Theme> = {
  form: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    gap: 2,
  },
  ".gap0": {
    gap: 0
  },
  ".changePasswordForm": {
    display:"flex",
    "changePasswordForm-checkbox":{
    },
    ".buttonActions": {
      ".buttonSubmit": {
        height: "2.5rem",
        width: "100%",
      },
    },
  },
};

export const fieldSx: SxProps<Theme> = {};
