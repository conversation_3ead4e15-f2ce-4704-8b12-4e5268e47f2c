import Box from "@mui/material/Box";
import CircularProgress from "@mui/material/CircularProgress";
import Typography from "@mui/material/Typography";

const Loading = () => {
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        minHeight: "80vh",
        // bgcolor: 'background.default',
      }}
    >
      <Box
        component="img"
        src="/images/gif/loadingAll.gif"
        alt="Loading"
        sx={{
          width: 140,
          height: 140,
          borderRadius: "50%",
          objectFit: "cover",
          display: "block",
        }}
      />
      {/* <Typography variant="body1" color="text.secondary">
                Loading content...
            </Typography> */}
    </Box>
  );
};

export default Loading;
