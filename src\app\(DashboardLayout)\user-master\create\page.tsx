"use client";
import PageContainer from "@/app/(DashboardLayout)/components/container/PageContainer";
import DashboardCard from "@/app/(DashboardLayout)/components/shared/DashboardCard";
import type { FieldConfig } from "@/components/common/DynamicForm/DynamicForm.types";
import PopupModal from "@/components/common/PopupModal";
import UserForm from "@/components/feature/UserMaster/UserForm/UserForm";
import { useBreadcrumb } from "@/features/breadcrumb/hook/useBreadcrumb";
import { getCountryList } from "@/features/country/state/countrySelector";
import { getDepartmentList } from "@/features/department/state/departmentSelector";
import { getOrganizationList } from "@/features/organization/state/organizationSelector";
import { getRoleList } from "@/features/role/state/roleSelector";
import { selectUserProfile } from "@/features/users/state/usersSelectors";
import { UserMaster } from "@/features/users/types/user";
import { useRoleToStore } from "@/hooks/useRoleToStore";
import { createNewUserMaster } from "@/services/userMasterService";
import { ROLE_CODE, RoleCode } from "@/types/auth";
import { isEqual } from "lodash";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import Loading from "../../loading";
import { useDepartmentToStore } from "@/hooks/useDepartmentToStore";
import { PAGE_TITLES } from "@/constants/routes";
import { useSnackbar } from "@/components/common/SnackBar/SnackbarProvider";

const UserCreatePage = () => {
  const router = useRouter();
  useBreadcrumb({
    pageTitle: "Add New User",
    items: [
      { id: "user-master", title: PAGE_TITLES.USER_LIST, href: "/user-master" },
      { id: "user-create", title: "Add New User" },
    ],
  });
  const [isLoading, setLoading] = useState(true);
  const [orgValue, setOrgValue] = useState("");
  const { showSnackbar } = useSnackbar();
  useRoleToStore(orgValue);
  useDepartmentToStore(orgValue);
  const [open, setOpen] = useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  const [loadingSubmit, setLoadingSubmit] = useState(false);
  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 500);
    return () => clearTimeout(timer);
  }, []);
  const departmentList = useSelector(getDepartmentList);
  const roleList = useSelector(getRoleList);
  const countryList = useSelector(getCountryList);
  const organizationList = useSelector(getOrganizationList);
  const departmentByCode = useMemo(
    () => Object.fromEntries((departmentList || []).map((d) => [d.departmentCode, d])),
    [departmentList]
  );
  const roleByCode = useMemo(() => Object.fromEntries(roleList.map((r) => [r.roleCode, r])), [roleList]);
  const countryByCode = useMemo(() => Object.fromEntries(countryList.map((c) => [c.countryCode, c])), [countryList]);
  const organizationByCode = useMemo(
    () => Object.fromEntries(organizationList.map((o) => [o.organizationCode, o])),
    [organizationList]
  );
  const userProfile = useSelector(selectUserProfile);

  const defaultCountryCode = useMemo(() => userProfile?.countryCode || "", [userProfile?.countryCode]);

  const defaultOrgCode = useMemo(() => {
    if (userProfile?.organizationCode && organizationList?.length) {
      const ADMIN_CODES: RoleCode[] = [ROLE_CODE.C_ADMIN, ROLE_CODE.M_ADMIN];
      const foundOrg = organizationByCode[userProfile.organizationCode];
      if (foundOrg && !ADMIN_CODES.includes(userProfile.roleCode)) {
        return userProfile.organizationCode;
      }
    }
    return undefined;
  }, [userProfile?.organizationCode, userProfile?.roleCode, organizationByCode, organizationList]);

  const userFormFields = useMemo<FieldConfig[]>(() => {
    const effectiveOrg = orgValue || defaultOrgCode;
    const hasOrg = !!effectiveOrg;

    return [
      {
        name: "firstName",
        label: "First Name",
        type: "text",
        placeholder: "Enter full name",
        required: true,
        validation: {
          required: "Full name is required",
          minLength: { value: 2, message: "Name must be at least 2 characters" },
          maxLength: { value: 50, message: "Name must not exceed 50 characters" },
        },
        customValidation: (value: string) => {
          if (value && value.length > 50) return "First Name can be entered up to a maximum of 50 characters";
          const regex = /^[A-Za-zÀ-ỹ\s]+$/;
          if (value && !regex.test(value)) return "The format of First Name is incorrect";
          return true;
        },
      },
      {
        name: "lastName",
        label: "Last Name",
        type: "text",
        placeholder: "Enter last name",
        required: true,
        validation: { required: "Last name is required" },
        customValidation: (value: string) => {
          if (value && value.length > 50) return "Last Name can be entered up to a maximum of 50 characters";
          const regex = /^[A-Za-zÀ-ỹ\s]+$/;
          if (value && !regex.test(value)) return "The format of Last Name is incorrect";
          return true;
        },
      },
      {
        name: "organizationCode",
        label: "Company",
        type: "autocomplete",
        required: true,
        options: organizationList.map((o) => ({ value: o.organizationCode, label: o.organizationName })),
        validation: { required: "Company is required" },
        customValidation: (value: string) => (!value ? "Company is a required field. Please enter it" : true),
        disabled: !!defaultOrgCode,
      },
      {
        name: "userId",
        label: "User ID",
        type: "email",
        placeholder: "Enter user ID",
        required: true,
        validation: {
          required: "User ID is required",
          minLength: { value: 3, message: "User ID must be at least 3 characters" },
          pattern: { value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i, message: "Invalid email address" },
        },
        customValidation: (value: string) => {
          if (!value) return true;
          if (value.length > 100) return "User ID can be entered up to a maximum of 100 characters";
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(value)) return "Please enter the correct email address format";
          return true;
        },
      },
      {
        name: "departmentCode",
        label: "Department",
        type: "autocomplete",
        required: true,
        options: (departmentList || []).map((d) => ({ value: d.departmentCode, label: d.departmentName })),
        validation: { required: "Department is required" },
        disabled: !hasOrg,
        customValidation: (value: string) => (!value ? "Department is a required field. Please enter it" : true),
      },
      {
        name: "countryCode",
        label: "Country",
        type: "autocomplete",
        required: true,
        options: countryList.map((c) => ({ value: c.countryCode, label: c.countryName })),
        validation: { required: "Country is required" },
        customValidation: (value: string) => (!value ? "Country is a required field. Please enter it" : true),
        disabled: !!defaultCountryCode,
      },
      {
        name: "roleCode",
        label: "Role",
        type: "autocomplete",
        required: true,
        options: roleList.map((r) => ({ value: r.roleCode, label: r.roleName })),
        validation: { required: "Role is required" },
        disabled: !hasOrg,
        customValidation: (value: string) => (!value ? "Role is a required field. Please enter it" : true),
      },
    ];
  }, [organizationList, departmentList, countryList, roleList, orgValue, defaultOrgCode, defaultCountryCode]);

  const initialValues = {
    firstName: "",
    lastName: "",
    userId: "",
    countryCode: defaultCountryCode,
    departmentCode: "",
    roleCode: "",
    organizationCode: defaultOrgCode,
    imageUrl: "",
  };
  const originalData = { ...initialValues, imageUrl: "" };

  if (isLoading) {
    return <Loading />;
  }
  const onSubmitDialog = () => {
    handleClose();
    router.push("/user-master");
  };

  const handleCancel = (currentData: any) => {
    if (!isEqual(originalData, currentData)) {
      handleOpen();
      return;
    }
    onSubmitDialog();
  };

  return (
    <>
      <PageContainer title="Create User" description="Create a new user account">
        <DashboardCard
          sx={{
            backgroundColor: "rgba(255, 255, 255, 1)",
            boxShadow: "none",
            borderRadius: "12px",
            border: "1px solid rgba(191, 196, 204, 1)",
          }}
          cardContentSx={{ padding: "24px" }}
        >
          <UserForm
            loadingSubmit={loadingSubmit}
            submitLabel="Create"
            fields={userFormFields}
            initialValues={initialValues}
            onCancel={handleCancel}
            onValuesChange={({ name, values, setValue, prevValues, clearErrors }) => {
              if (name === "organizationCode") {
                const changed = prevValues.organizationCode !== values.organizationCode;
                const hasOrg = !!values.organizationCode;
                setOrgValue(values.organizationCode || "");
                if (!hasOrg || changed) {
                  setValue("roleCode", "");
                  setValue("departmentCode", "");
                  setTimeout(() => {
                    clearErrors(["roleCode", "departmentCode"]);
                  });
                }
              }
            }}
            onSubmit={async (data: Partial<UserMaster>) => {
              setLoadingSubmit(true);
              const parseDataPayload: UserMaster = {
                firstName: data?.firstName || "",
                lastName: data?.lastName || "",
                userId: data?.userId || "",
                departmentCode: data?.departmentCode || "",
                departmentName: departmentByCode[data?.departmentCode || ""]?.departmentName || "",
                countryCode: data?.countryCode || "",
                countryName: countryByCode[data?.countryCode || ""]?.countryName || "",
                roleCode: data?.roleCode || "",
                roleName: roleByCode[data?.roleCode || ""]?.roleName || "",
                organizationCode: data?.organizationCode || "",
                organizationName: organizationByCode[data?.organizationCode || ""]?.organizationName || "",
                imageUrl: data.imageUrl || "",
              };
              try {
                await createNewUserMaster(parseDataPayload);
                setLoadingSubmit(false);
                showSnackbar("Create Successful!", "The account has been successfully created.", "success");
                router.push("/user-master");
              } catch (error: any) {
                setLoadingSubmit(false);
                const code = error?.response?.data?.code;
                if (code === "E_UMA003") {
                  return { fieldErrors: { userId: "This User ID is already in use" } };
                }
                return { formError: "Failed to create user. Please try again." };
              }
            }}
          />
        </DashboardCard>
      </PageContainer>
      <PopupModal
        open={open}
        onClose={handleClose}
        type="warning"
        onSubmit={onSubmitDialog}
        title=""
        description="The changes will be discarded, are you sure?"
        disableBackdropClick
      />
    </>
  );
};

export default UserCreatePage;
