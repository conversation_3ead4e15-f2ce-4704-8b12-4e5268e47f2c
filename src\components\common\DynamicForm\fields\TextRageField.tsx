import React from "react";
import { Box } from "@mui/material";
import { Controller } from "react-hook-form";
import MuiTextField from "@mui/material/TextField";
import { FieldConfig } from "../DynamicForm.types";
import MuiErrorCustomStyles from "@/utils/theme/muiErrorStyles";

interface Props {
  field: FieldConfig;
  control: any;
  errorFrom?: string;
  errorTo?: string;
  disabled?: boolean;
  i18n?: (key: string) => string;
}

const TextRangeField: React.FC<Props> = ({ field, control, errorFrom, errorTo, disabled, i18n }) => {
  const nameFrom = `${field.name}.from`;
  const nameTo = `${field.name}.to`;

  const CombinedLabel = () =>
    field.required ? (
      <span>
        {i18n ? i18n(field.label) : field.label}
        <span style={{ color: "#CA002E", marginLeft: 4 }}>*</span>
      </span>
    ) : i18n ? (
      i18n(field.label)
    ) : (
      field.label
    );

  const sharedSx = {
    ...field.style,
    ...MuiErrorCustomStyles,
    "& .MuiInputBase-input.Mui-disabled": {
      WebkitTextFillColor: (field as any).disabledValueColor || "#111827",
      color: (field as any).disabledValueColor || "#111827",
    },
  };

  const allowOnlyDigitsKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const editKeys = ["Backspace", "Delete", "ArrowLeft", "ArrowRight", "ArrowUp", "ArrowDown", "Tab", "Home", "End", "Enter"];
    if (e.ctrlKey || e.metaKey) return;
    if (editKeys.includes(e.key)) return;
    if (e.key.length > 1) return;
    if (!/[0-9]/.test(e.key)) e.preventDefault();
  };

  const sanitizePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    const text = e.clipboardData.getData("text");
    if (!/^[0-9]+$/.test(text)) {
      e.preventDefault();
    }
  };

  return (
    <>
      {field.hiddenLabelNormal ? (
        <Box sx={{ position: "relative", width: "100%" }} className={field.className}>
          <Box
            sx={{
              position: "absolute",
              left: 0,
              right: 0,
              top: "-22px",
              color: "#222222",
              fontFamily: "Roboto",
              fontWeight: 400,
              fontSize: 14,
              lineHeight: 1.5,
              zIndex: 2,
            }}
          >
            {i18n ? i18n(field.label) : field.label}
          </Box>

          <Box sx={{ mt: 3, display: "flex", alignItems: "center", gap: 1, width: "100%" }}>
            <Controller
              name={nameFrom}
              control={control}
              render={({ field: controllerField }) => (
                <MuiTextField
                  {...controllerField}
                  value={controllerField.value ?? ""}
                  label=""
                  placeholder="From"
                  error={!!errorFrom}
                  helperText={errorFrom || ""}
                  fullWidth
                  disabled={disabled || field.disabled}
                  multiline={field.multiline}
                  rows={field.rows}
                  sx={sharedSx}
                  inputMode="numeric"
                  onKeyDown={field.isNumberic ? allowOnlyDigitsKeyDown : undefined}
                  onPaste={field.isNumberic ? sanitizePaste : undefined}
                />
              )}
            />

            <Box sx={{ color: "rgba(0, 0, 0, 1)", userSelect: "none" }}>～</Box>

            <Controller
              name={nameTo}
              control={control}
              render={({ field: controllerField }) => (
                <MuiTextField
                  {...controllerField}
                  value={controllerField.value ?? ""}
                  label=""
                  placeholder="To"
                  error={!!errorTo}
                  helperText={errorTo || ""}
                  fullWidth
                  disabled={disabled || field.disabled}
                  multiline={field.multiline}
                  rows={field.rows}
                  sx={sharedSx}
                  onKeyDown={field.isNumberic ? allowOnlyDigitsKeyDown : undefined}
                  onPaste={field.isNumberic ? sanitizePaste : undefined}
                />
              )}
            />
          </Box>
        </Box>
      ) : (
        <Box sx={{ display: "flex", flexDirection: "column", width: "100%" }} className={field.className}>
          {field.label ? (
            <Box sx={{ mb: 1, fontSize: 14, color: "#111827" }}>
              <CombinedLabel />
            </Box>
          ) : null}

          <Box sx={{ display: "flex", alignItems: "center", gap: 1, width: "100%" }}>
            <Controller
              name={nameFrom}
              control={control}
              render={({ field: controllerField }) => (
                <MuiTextField
                  {...controllerField}
                  value={controllerField.value ?? ""}
                  placeholder="From"
                  error={!!errorFrom}
                  helperText={errorFrom || field.helperText}
                  fullWidth
                  disabled={disabled || field.disabled}
                  multiline={field.multiline}
                  rows={field.rows}
                  sx={sharedSx}
                />
              )}
            />

            <Box sx={{ color: "rgba(0, 0, 0, 1)", userSelect: "none" }}>～</Box>

            <Controller
              name={nameTo}
              control={control}
              render={({ field: controllerField }) => (
                <MuiTextField
                  {...controllerField}
                  value={controllerField.value ?? ""}
                  placeholder="To"
                  error={!!errorTo}
                  helperText={errorTo || field.helperText}
                  fullWidth
                  disabled={disabled || field.disabled}
                  multiline={field.multiline}
                  rows={field.rows}
                  sx={sharedSx}
                />
              )}
            />
          </Box>
        </Box>
      )}
    </>
  );
};

export default TextRangeField;
