import React from "react";
import { Box, Typography } from "@mui/material";
import { Controller } from "react-hook-form";
import MuiTextField from "@mui/material/TextField";
import MuiErrorCustomStyles from "@/utils/theme/muiErrorStyles";
import { Props } from "react-apexcharts";

const TextRangeField: React.FC<Props> = ({ field, control, errorFrom, errorTo, disabled, i18n }) => {
  const CombinedLabel = () =>
    field.required ? (
      <span>
        {i18n ? i18n(field.label) : field.label}
        <span style={{ color: "#CA002E", marginLeft: 4 }}>*</span>
      </span>
    ) : i18n ? (
      i18n(field.label)
    ) : (
      field.label
    );

  const sharedSx = {
    ...field.style,
    ...MuiErrorCustomStyles,
    "& .MuiInputBase-input.Mui-disabled": {
      WebkitTextFillColor: (field as any).disabledValueColor || "#111827",
      color: (field as any).disabledValueColor || "#111827",
    },
  };

  const numberInputFix = {
    "& input[type=number]": { MozAppearance: "textfield" },
    "& input[type=number]::-webkit-inner-spin-button, & input[type=number]::-webkit-outer-spin-button": {
      WebkitAppearance: "none",
      margin: 0,
    },
  };

  const maxInt = typeof field.maxIntegerDigits === "number" ? field.maxIntegerDigits : undefined;
  const maxDec = typeof field.maxDecimalDigits === "number" ? field.maxDecimalDigits : undefined;

  const limitDecimal = (raw: string) => {
    if (raw === "") return "";
    let v = raw.replace(/[^\d.]/g, "");
    const firstDot = v.indexOf(".");
    if (firstDot !== -1) {
      v = v.substring(0, firstDot + 1) + v.substring(firstDot + 1).replace(/\./g, "");
    }
    if (maxInt === undefined && maxDec === undefined) {
      if (v === ".") return "0";
      return v;
    }
    let [intPart, decPart = ""] = v.split(".");
    if (intPart.length > 1 && intPart.startsWith("0")) {
      intPart = intPart.replace(/^0+/, "") || "0";
    }
    if (maxInt !== undefined) intPart = intPart.slice(0, maxInt);
    if (maxDec !== undefined) decPart = decPart.slice(0, maxDec);
    return decPart.length > 0 ? `${intPart}.${decPart}` : intPart;
  };

  const commonNumberGuards = (controllerOnChange: (v: any) => void, current: any, key: "from" | "to") => ({
    onBeforeInput: (e: any) => {
      if ((current?.[key] ?? "") === "" && e.data === ".") {
        e.preventDefault();
        controllerOnChange({ ...current, [key]: "0" });
      }
    },
    onKeyDown: (e: any) => {
      if (["e", "E", "+", "-"].includes(e.key)) e.preventDefault();
    },
    onPaste: (e: any) => {
      const text = e.clipboardData.getData("text").trim();
      if (text === ".") {
        e.preventDefault();
        controllerOnChange({ ...current, [key]: "0" });
        return;
      }
      if (!/^\d+(\.\d+)?$/.test(text) || ["e", "E", "+", "-"].includes(text)) e.preventDefault();
    },
    onDrop: (e: any) => {
      const droppedText = e.dataTransfer.getData("text/plain");
      if (!/^\d+(\.\d+)?$/.test(droppedText)) e.preventDefault();
    },
  });

  return (
    <Controller
      name={field.name}
      control={control}
      render={({ field: controllerField, fieldState }) => {
        const value = controllerField.value || { from: "", to: "" };
        const { from, to } = value;
        const setPart = (k: "from" | "to", raw: string) => {
          const limited = limitDecimal(raw);
          controllerField.onChange({ ...value, [k]: limited });
        };
        let errorMsg = fieldState.error?.message || errorFrom || errorTo || "";
        if (parseFloat(from) > parseFloat(to)) {
          errorMsg = "From value cannot be greater than the To value";
        }
        const isError = !!errorMsg;
        const RangeUI = (
          <Box sx={{ mt: field.hiddenLabelNormal ? 3 : 0, display: "flex", alignItems: "center", gap: 1, width: "100%" }}>
            <MuiTextField
              value={value.from ?? ""}
              label={field.hiddenLabelNormal ? "" : undefined}
              placeholder="From"
              error={isError}
              helperText={field.hiddenLabelNormal ? "" : isError ? errorMsg : field.helperText}
              fullWidth
              disabled={disabled || field.disabled}
              multiline={field.multiline}
              rows={field.rows}
              type="number"
              inputMode="decimal"
              sx={{ ...sharedSx, ...numberInputFix }}
              onChange={(e) => setPart("from", e.target.value)}
              {...commonNumberGuards(controllerField.onChange, value, "from")}
            />

            <Box sx={{ color: "rgba(0, 0, 0, 1)", userSelect: "none" }}>～</Box>

            <MuiTextField
              value={value.to ?? ""}
              label={field.hiddenLabelNormal ? "" : undefined}
              placeholder="To"
              error={isError}
              helperText={field.hiddenLabelNormal ? "" : isError ? errorMsg : field.helperText}
              fullWidth
              disabled={disabled || field.disabled}
              multiline={field.multiline}
              rows={field.rows}
              type="number"
              inputMode="decimal"
              sx={{ ...sharedSx, ...numberInputFix }}
              onChange={(e) => setPart("to", e.target.value)}
              {...commonNumberGuards(controllerField.onChange, value, "to")}
            />
          </Box>
        );

        if (field.hiddenLabelNormal) {
          return (
            <>
              <Box sx={{ position: "relative", width: "100%" }} className={field.className}>
                <Box
                  className="abbc-phunq7"
                  sx={{
                    position: "absolute",
                    left: 0,
                    right: 0,
                    top: "-22px",
                    color: "#222",
                    fontFamily: "Roboto",
                    fontWeight: 400,
                    fontSize: 14,
                    lineHeight: 1.5,
                    zIndex: 2,
                  }}
                >
                  {i18n ? i18n(field.label) : field.label}
                </Box>
                {RangeUI}
              </Box>
              {isError && <Typography color="#CA002E">{errorMsg}</Typography>}
            </>
          );
        }

        return (
          <Box sx={{ display: "flex", flexDirection: "column", width: "100%" }} className={field.className}>
            {field.label ? (
              <Box sx={{ mb: 1, fontSize: 14, color: "#111827" }}>
                <CombinedLabel />
              </Box>
            ) : null}
            {RangeUI}
          </Box>
        );
      }}
    />
  );
};

export default TextRangeField;
