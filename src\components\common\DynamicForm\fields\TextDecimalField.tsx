import React from "react";
import { Controller } from "react-hook-form";
import Mu<PERSON><PERSON>extField from "@mui/material/TextField";
import MuiErrorCustomStyles from "@/utils/theme/muiErrorStyles";
import { Box, Tooltip } from "@mui/material";
import { InfoOutlined } from "@mui/icons-material";

const TextDecimalField: React.FC<any> = ({
  field,
  control,
  errorMessage,
  disabled,
  i18n,
}) => {
  const labelText = i18n ? i18n(field.label) : field.label;
  const requiredAsterisk =
    field.required && !field.disabled ? (
      <span style={{ color: "#CA002E", marginLeft: 4 }}>*</span>
    ) : null;

  const labelWithTooltip = (
    <span style={{ display: "inline-flex", alignItems: "center", gap: 6 }}>
      <span>
        {labelText}
        {requiredAsterisk}
      </span>
      {field.labelTooltipContent ? (
        <Tooltip
          title={
            typeof field.labelTooltipContent === "string"
              ? i18n
                ? i18n(field.labelTooltipContent)
                : field.labelTooltipContent
              : field.labelTooltipContent
          }
          {...field.labelTooltipProps}
        >
          <span
            style={{ display: "inline-flex", color: "rgba(0,0,0,0.54)" }}
            onMouseDown={(e) => e.preventDefault()}
          >
            {field.labelTooltipIcon ?? <InfoOutlined fontSize="small" />}
          </span>
        </Tooltip>
      ) : null}
    </span>
  );

  const sharedSx = {
    ...field.style,
    ...MuiErrorCustomStyles,
    "& .MuiInputBase-input.Mui-disabled": {
      WebkitTextFillColor: field.disabledValueColor || "#111827",
      color: field.disabledValueColor || "#111827",
    },
  };

  const numberInputFix = {
    "& input[type=number]": { MozAppearance: "textfield" },
    "& input[type=number]::-webkit-inner-spin-button, & input[type=number]::-webkit-outer-spin-button":
      {
        WebkitAppearance: "none",
        margin: 0,
      },
  };

  const maxInt =
    typeof field.maxIntegerDigits === "number"
      ? field.maxIntegerDigits
      : undefined;
  const maxDec =
    typeof field.maxDecimalDigits === "number"
      ? field.maxDecimalDigits
      : undefined;

  const limitDecimal = (raw: string) => {
    if (raw === "") return "";
    let v = raw.replace(/[^\d.]/g, "");
    const firstDot = v.indexOf(".");
    if (firstDot !== -1) {
      v =
        v.substring(0, firstDot + 1) +
        v.substring(firstDot + 1).replace(/\./g, "");
    }
    let [intPart, decPart = ""] = v.split(".");
    if (intPart.length > 1 && intPart.startsWith("0")) {
      intPart = intPart.replace(/^0+/, "") || "0";
    }
    if (maxInt !== undefined) intPart = intPart.slice(0, maxInt);
    if (maxDec !== undefined) decPart = decPart.slice(0, maxDec);
    return decPart.length > 0 ? `${intPart}.${decPart}` : intPart;
  };

  const inputGuards = (onChange: (v: any) => void, current: string) => ({
    onBeforeInput: (e: any) => {
      if (current === "" && e.data === ".") {
        e.preventDefault();
        onChange("0");
      }
    },
    onKeyDown: (e: any) => {
      if (["e", "E", "+", "-"].includes(e.key)) e.preventDefault();
    },
    onPaste: (e: any) => {
      const text = e.clipboardData.getData("text").trim();
      if (text === ".") {
        e.preventDefault();
        onChange("0");
        return;
      }
      if (!/^\d+(\.\d+)?$/.test(text)) e.preventDefault();
    },
    onDrop: (e: any) => {
      const droppedText = e.dataTransfer.getData("text/plain");
      if (!/^\d+(\.\d+)?$/.test(droppedText)) e.preventDefault();
    },
  });

  return (
    <Controller
      name={field.name}
      control={control}
      render={({ field: controllerField, fieldState }) => {
        const value = controllerField.value ?? "";
        const setValue = (raw: string) => {
          const limited = limitDecimal(raw);
          controllerField.onChange(limited);
        };
        const isError = !!(fieldState.error?.message || errorMessage);
        const helperText = isError
          ? fieldState.error?.message || errorMessage
          : field.helperText;

        return (
          <Box
            sx={{ display: "flex", flexDirection: "column", width: "100%" }}
            className={field.className}
          >
            <MuiTextField
              value={value}
              label={labelWithTooltip}
              placeholder={field.placeholder}
              error={isError}
              helperText={field.hiddenLabelNormal ? "" : helperText}
              fullWidth
              disabled={disabled || field.disabled}
              multiline={field.multiline}
              rows={field.rows}
              type="number"
              inputMode="decimal"
              sx={{ ...sharedSx, ...numberInputFix }}
              slotProps={{
                inputLabel: {
                  sx: {
                    "&.MuiInputLabel-shrink": {
                      fontSize: 18,
                      backgroundColor: "rgba(255, 255, 255, 1)",
                    },
                  },
                },
              }}
              onChange={(e) => setValue(e.target.value)}
              {...inputGuards(controllerField.onChange, value)}
            />
          </Box>
        );
      }}
    />
  );
};

export default TextDecimalField;
