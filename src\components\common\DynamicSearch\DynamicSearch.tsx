import Button from "@/components/common/button/Button";
import DynamicForm from "@/components/common/DynamicForm/DynamicForm";
import type { DynamicFormProps } from "@/components/common/DynamicForm/DynamicForm.types";
import { Box, Collapse, Typography } from "@mui/material";
import Image from "next/image";
import React, { useCallback, useState, useMemo, useRef } from "react";
import styles from "./DynamicSearch.module.css";

const hideAdvancedIcon = "/images/icons/hide-advanced.svg";
const showAdvancedIcon = "/images/icons/show-advanced.svg";
const iconClear = "/images/icons/clear-filter.svg";
const iconSearch = "/images/icons/search.svg";

interface DynamicSearchProps extends Omit<DynamicFormProps, "submitLabel"> {
  onClear?: () => void;
  clearLabel?: string;
  searchLabel?: string;
  advancedLabel?: string;
  skipValidationOnSubmit?: boolean;
  infoContent?: string;
  loadingSearch?: boolean;
  loadingClear?: boolean;
}

type ValuesChangeParams = {
  reset?: (values?: any, options?: any) => void;
};

const DynamicSearch: React.FC<React.PropsWithChildren<DynamicSearchProps>> = ({
  onClear,
  clearLabel = "Clear Filter",
  searchLabel = "Search",
  advancedLabel = "Hide Advanced Filters",
  skipValidationOnSubmit = true,
  infoContent = "",
  loadingSearch,
  loadingClear,
  ...formProps
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const resetRef = useRef<null | ((values?: any) => void)>(null);
  const handleValuesChange = useCallback(
    (params: any) => {
      if (params.reset) {
        resetRef.current = params.reset;
      }
      if (formProps.onValuesChange) {
        formProps.onValuesChange(params);
      }
    },
    [formProps]
  );

  const handleClearInternal = useCallback(() => {
    resetRef.current?.();
    if (formProps.onSubmit) formProps.onSubmit({});
    if (onClear) onClear();
  }, [formProps, onClear]);
  const handleToggleAdvanced = useCallback(
    () => setShowAdvanced((prev) => !prev),
    []
  );
  const memoFields = useMemo(() => formProps.fields, [formProps.fields]);
  const defaultFields = useMemo(
    () =>
      memoFields
        ? memoFields
            .slice(0, Math.min(4, memoFields.length))
            .map((field) => field.name)
        : [],
    [memoFields]
  );
  return (
    <Box
      sx={{
        background: "#fff",
        borderRadius: "12px",
        boxShadow: "0px 2px 10px 0px rgba(0,0,0,0.06)",
        border: "1px solid #f5f6fa",
        position: "relative",
        flexGrow: 1,
      }}
    >
      <DynamicForm
        {...formProps}
        fields={memoFields}
        onSubmit={formProps.onSubmit}
        skipValidationOnSubmit={skipValidationOnSubmit}
        submitLabel={searchLabel}
        onValuesChange={handleValuesChange}
        hideSubmit
        hideCancel
        formClassName="flex flex-col gap0"
        fieldClassName="flex-1 min-w-0"
        renderFields={(
          fields,
          FieldComponent,
          control,
          formState,
          loading,
          i18n
        ) => {
          const hasErrors = Object.keys(formState?.errors || {}).length > 0;
          return (
            <>
              <Box
                className={showAdvanced ? "form-search-control" : ""}
                sx={{ p: "10px 20px 0px 20px" }}
              >
                <Box
                  display="grid"
                  gridTemplateColumns={{
                    xs: "1fr",
                    sm: "2fr 1fr",
                    md: "repeat(4, 1fr)",
                  }}
                  gap={2.5}
                  sx={{
                    "& .MuiTextField-root, & .MuiInputBase-root, & .MuiOutlinedInput-root":
                      {
                        height: "34px",
                        borderRadius: "4px",
                      },
                    "& .MuiSelect-select, & .MuiInputBase-input": {
                      height: "34px",
                      padding: "0 14px",
                      display: "flex",
                      alignItems: "center",
                      borderRadius: "4px",
                      boxSizing: "border-box",
                    },
                  }}
                >
                  {fields
                    .filter((field) => defaultFields.includes(field.name))
                    .map((field) => (
                      <Box key={field.name}>
                        <FieldComponent
                          field={field}
                          control={control}
                          error={formState.errors[field.name]?.message}
                          disabled={loading}
                          i18n={i18n}
                        />
                      </Box>
                    ))}
                </Box>
                <Box
                  sx={{
                    overflow: "hidden",
                    transition: "max-height 0.35s cubic-bezier(0.4, 0, 0.2, 1)",
                    maxHeight: showAdvanced ? 1000 : 0,
                    mt: "10px",
                  }}
                >
                  <Box
                    display="grid"
                    gridTemplateColumns={{
                      xs: "1fr",
                      sm: "1fr 1fr",
                      md: "repeat(4, 1fr)",
                    }}
                    gap="10px 20px"
                    sx={{
                      opacity: showAdvanced ? 1 : 0,
                      transition: "opacity 0.25s",
                      pointerEvents: showAdvanced ? "auto" : "none",
                      "& .MuiTextField-root, & .MuiInputBase-root, & .MuiOutlinedInput-root":
                        {
                          height: "34px",
                          borderRadius: "4px",
                        },
                      "& .MuiSelect-select, & .MuiInputBase-input": {
                        height: "34px",
                        borderRadius: "4px",
                        padding: "0 14px",
                        display: "flex",
                        alignItems: "center",
                        boxSizing: "border-box",
                      },
                    }}
                  >
                    {fields
                      .filter((field) => !defaultFields.includes(field.name))
                      .map((field) => (
                        <Box key={field.name}>
                          <FieldComponent
                            field={field}
                            control={control}
                            error={formState.errors[field.name]?.message}
                            disabled={loading}
                            i18n={i18n}
                          />
                        </Box>
                      ))}
                  </Box>
                </Box>
              </Box>
              {/* <Collapse in={showAdvanced} timeout="auto" unmountOnExit>
            </Collapse> */}
              <Box sx={{ position: "relative" }}>
                <Box
                  sx={{
                    position: "absolute",
                    top: "50%",
                    left: 0,
                    width: "100%",
                    height: "1px",
                    background: "#e0e0e0",
                    zIndex: 0,
                    transform: "translateY(-50%)",
                  }}
                />
                <button
                  type="button"
                  onClick={handleToggleAdvanced}
                  className={styles.advancedToggleBtn}
                  aria-pressed={showAdvanced}
                  aria-label={
                    showAdvanced
                      ? "Hide advanced filters"
                      : "Show advanced filters"
                  }
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: 4,
                    justifyContent: "center",
                    background: "#fff",
                    padding: "0 12px",
                    borderRadius: 4,
                    zIndex: 1,
                    position: "relative",
                    width: "fit-content",
                    margin: "0 auto",
                    border: "none",
                    cursor: "pointer",
                  }}
                >
                  <Image
                    src={showAdvanced ? hideAdvancedIcon : showAdvancedIcon}
                    alt={
                      showAdvanced
                        ? "hide advanced filters"
                        : "show advanced filters"
                    }
                    width={24}
                    height={24}
                    className={styles.iconHide}
                  />
                  <span className={styles.advancedLabel}>
                    {showAdvanced ? advancedLabel : "Show Advanced Filters"}
                  </span>
                </button>
              </Box>
              <Box
                display={!!infoContent ? "flex" : "block"}
                justifyContent="space-between"
                alignItems={"center"}
              >
                {!!infoContent && (
                  <Typography
                    ml={2.5}
                    display={"flex"}
                    color="rgba(102, 102, 102, 1)"
                    fontSize={14}
                    lineHeight="150%"
                    alignItems={"center"}
                    gap="4px"
                  >
                    <Image
                      src="../images/millsheet/book-info.svg"
                      alt="book info"
                      width={24}
                      height={24}
                    />
                    {infoContent}
                  </Typography>
                )}
                <Box
                  display="flex"
                  gap={2.5}
                  justifyContent="flex-end"
                  sx={{
                    px: "24px",
                    pb: "12px",
                  }}
                >
                  {onClear && (
                    <Button
                      variant="outlined"
                      prefixIcon={
                        <Image
                          src={iconClear}
                          alt="clear"
                          width={20}
                          height={20}
                          className={styles.iconClear}
                        />
                      }
                      onClick={handleClearInternal}
                      isLoading={loadingClear}
                      size="small"
                    >
                      {clearLabel}
                    </Button>
                  )}
                  <Button
                    type="submit"
                    variant="contained"
                    size="small"
                    disabled={hasErrors || loadingSearch}
                    isLoading={loadingSearch}
                    prefixIcon={
                      <Image
                        src={iconSearch}
                        alt="search"
                        width={20}
                        height={20}
                        className={styles.iconSearch}
                      />
                    }
                  >
                    {searchLabel}
                  </Button>
                </Box>
              </Box>
            </>
          );
        }}
      />

      {/* Đã chuyển nút Search và Clear vào trong renderFields của DynamicForm để đảm bảo nằm trong <form> */}
    </Box>
  );
};

export default DynamicSearch;
