"use client";
import ProfileHeader from "@/app/(DashboardLayout)/user-master/ProfileHeader";
import DynamicForm from "@/components/common/DynamicForm/DynamicForm";
import type { FieldConfig } from "@/components/common/DynamicForm/DynamicForm.types";
import { useScreenAccessDecision } from "@/features/auth/hooks/ScreenAccessDecision";
import * as userMasterService from "@/services/userMasterService";
import { hasAllowedExtension, withinSize } from "@/utils/common";
import DeleteOutline from "@mui/icons-material/DeleteOutline";
import { Avatar, Box, IconButton, Tab, Tabs, Typography } from "@mui/material";
import Image from "next/image";
import { usePathname } from "next/navigation";
import React, { useMemo, useRef, useState } from "react";

const ImageAcceptType = Object.freeze(".png,.jpg,.gif");
const ACCEPT_EXTS = [".png", ".jpg", ".gif"];
const IMAGE_MAX_SIZE_MB = 5;

interface UserFormProps {
  fields: FieldConfig[];
  initialValues?: Record<string, any>;
  initialAvatar?: string | null;
  submitLabel: string;
  isEditUser?: boolean;
  onSubmit: (data: any) => void | Promise<any>;
  onCancel: (data?: any) => void;
}

const UserForm: React.FC<UserFormProps> = ({
  fields,
  initialValues = {},
  submitLabel = "Submit",
  onSubmit,
  onCancel,
  isEditUser,
}) => {
  const pathname = usePathname();
  const { has } = useScreenAccessDecision();
  isEditUser = isEditUser ?? pathname.startsWith("/user-master/edit");
  const [avatar, setAvatar] = useState<string>(initialValues.imagePath);
  const [avatarUrl, setAvatarUrl] = useState<string>(initialValues.imageUrl);
  const [imageError, setImageError] = useState<boolean>(false);
  const [imageErrorText, setImageErrorText] = useState<string>("");
  const lockStatus = !initialValues.lockStatus;
  const [hover, setHover] = React.useState(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const { firstName, lastName, userId, roleName, ...defaultValues } = initialValues;
  const formDefaults = useMemo(
    () => ({ ...defaultValues, firstName, lastName, userId }),
    [defaultValues, firstName, lastName, userId]
  );
  const memoFields = React.useMemo(() => fields, [fields]);

  const handleUploadAvatar = (formData: FormData) => {
    userMasterService
      .uploadAvatar(formData)
      .then((res) => {
        setAvatar(res.imagePath);
        setAvatarUrl(res.imageUrl);
        setImageError(false);
      })
      .catch((error) => {
        const code = error?.response?.data?.code;
        if (code === "E_COM002") {
          setImageError(true);
        }
      });
  };

  const handleRemoveAvatar = () => {
    setAvatar("");
    setAvatarUrl("");
    setImageError(false);
    setImageErrorText("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <Box>
      {isEditUser && (
        <ProfileHeader
          name={`${lastName} ${firstName}`}
          roleName={roleName}
          userId={userId}
          lockStatus={lockStatus}
          avatarUrl={avatarUrl}
        />
      )}
      <Tabs value={0} sx={{ mb: 3 }}>
        <Tab
          icon={<Image src="/images/user/user-tab.svg" alt="user tab" width={24} height={24}></Image>}
          iconPosition="start"
          label="Personal Information"
          sx={{
            fontWeight: 600,
            fontSize: 18,
            color: "primary.main",
            minHeight: 48,
            minWidth: 220,
            textTransform: "none",
            "&.Mui-selected": {
              color: "primary.main",
            },
          }}
        />
      </Tabs>
      <DynamicForm
        canSubmit={has(isEditUser ? "update" : "create") && !imageError}
        onCancel={(data) => onCancel({ ...data, imageUrl: avatarUrl })}
        fields={memoFields}
        defaultValues={formDefaults}
        onSubmit={(data) => onSubmit({ ...data, imageUrl: avatar })}
        submitLabel={submitLabel}
        formClassName="user-create-form"
        renderFields={(fields, FieldComponent, control, formState, loading, i18n) => (
          <>
            {!isEditUser && (
              <Box>
                <Box display="flex" alignItems="flex-end" gap={1}>
                  <Box
                    sx={{
                      width: "115px",
                      height: "115px",
                      borderRadius: "10px",
                      border: "1px solid rgba(162, 161, 168, 0.20)",
                      background: "rgba(162, 161, 168, 0.05)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      cursor: "pointer",
                      position: "relative",
                      overflow: "hidden",
                      transition: "background 0.2s, border-color 0.2s",
                      "&:hover": {
                        background: "rgba(162, 161, 168, 0.15)",
                        borderColor: "rgba(162, 161, 168, 0.40)",
                      },
                    }}
                    onMouseEnter={() => setHover(true)}
                    onMouseLeave={() => setHover(false)}
                  >
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept={ImageAcceptType}
                      style={{ display: "none" }}
                      id="avatar-upload-mini"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (!file) return;

                        if (!hasAllowedExtension(file, ACCEPT_EXTS)) {
                          setImageError(true);
                          setImageErrorText("Select a file in JPG, PNG, or GIF format.");
                          e.currentTarget.value = "";
                          return;
                        }
                        if (!withinSize(file, IMAGE_MAX_SIZE_MB)) {
                          setImageError(true);
                          setImageErrorText("File size exceeds 5MB");
                          e.currentTarget.value = "";
                          return;
                        }

                        setImageError(false);
                        setImageErrorText("");
                        const formData = new FormData();
                        formData.append("file", file);
                        handleUploadAvatar(formData);
                      }}
                    />
                    <label
                      htmlFor="avatar-upload-mini"
                      style={{
                        width: "100%",
                        height: "100%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        cursor: "pointer",
                      }}
                    >
                      {avatar ? (
                        <>
                          <Avatar src={avatarUrl} alt="Avatar" sx={{ width: "100%", height: "100%", borderRadius: "10px" }} />
                          {hover && (
                            <Box
                              sx={{
                                position: "absolute",
                                top: 0,
                                left: 0,
                                width: "100%",
                                height: "100%",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                background: "rgba(255,255,255,0.5)",
                                borderRadius: "10px",
                                pointerEvents: "none",
                              }}
                            >
                              <svg width="36" height="36" viewBox="0 0 36 36" fill="none">
                                <circle cx="18" cy="18" r="18" fill="#E4E9F2" />
                                <path
                                  d="M24 23.25C24 24.2165 23.2165 25 22.25 25H13.75C12.7835 25 12 24.2165 12 23.25V16.75C12 15.7835 12.7835 15 13.75 15H15.25L16.25 13H19.75L20.75 15H22.25C23.2165 15 24 15.7835 24 16.75V23.25ZM18 22C19.1046 22 20 21.1046 20 20C20 18.8954 19.1046 18 18 18C16.8954 18 16 18.8954 16 20C16 21.1046 16.8954 22 18 22Z"
                                  fill="#8F9BB3"
                                />
                              </svg>
                            </Box>
                          )}
                        </>
                      ) : (
                        <svg width="36" height="36" viewBox="0 0 36 36" fill="none">
                          <circle cx="18" cy="18" r="18" fill="#E4E9F2" />
                          <path
                            d="M24 23.25C24 24.2165 23.2165 25 22.25 25H13.75C12.7835 25 12 24.2165 12 23.25V16.75C12 15.7835 12.7835 15 13.75 15H15.25L16.25 13H19.75L20.75 15H22.25C23.2165 15 24 15.7835 24 16.75V23.25ZM18 22C19.1046 22 20 21.1046 20 20C20 18.8954 19.1046 18 18 18C16.8954 18 16 18.8954 16 20C16 21.1046 16.8954 22 18 22Z"
                            fill="#8F9BB3"
                          />
                        </svg>
                      )}
                    </label>
                  </Box>
                  {avatar && (
                    <Box display="flex" flexDirection="column" gap={1}>
                      <IconButton
                        onClick={handleRemoveAvatar}
                        sx={{
                          width: 30,
                          height: 30,
                          border: "1px solid rgba(162, 161, 168, 0.5)",
                          "&:hover": {
                            bgcolor: "rgba(202, 0, 46, 1)",
                            color: "#fff",
                          },
                          transition: ".2s",
                        }}
                        aria-label="remove avatar"
                      >
                        <DeleteOutline fontSize="small" />
                      </IconButton>
                    </Box>
                  )}
                </Box>
                <Box sx={{ mt: 1, minHeight: 18 }}>
                  <Typography variant="caption" color="#CA002E" sx={{ visibility: imageError ? "visible" : "hidden" }}>
                    {imageErrorText}
                  </Typography>
                </Box>
              </Box>
            )}
            <Box>
              <Box
                display="grid"
                gridTemplateColumns={{
                  xs: "1fr",
                  md: "1fr 1fr",
                }}
                gap={3}
                rowGap={4}
                mb={4}
                sx={{
                  "& .MuiTextField-root": {
                    height: "56px",
                  },
                  "& .MuiInputBase-root": {
                    height: "56px",
                  },
                  "& .MuiOutlinedInput-root": {
                    height: "56px",
                  },
                  "& .MuiSelect-select": {
                    height: "56px",
                    padding: "16px 14px",
                    display: "flex",
                    alignItems: "center",
                  },
                  "& .MuiInputBase-input": {
                    height: "56px",
                    padding: "16px 14px",
                    boxSizing: "border-box",
                  },
                }}
              >
                {fields.map((field) => (
                  <Box key={field.name}>
                    <FieldComponent
                      field={field}
                      control={control}
                      error={formState.errors[field.name]?.message}
                      disabled={loading}
                      i18n={i18n}
                    />
                  </Box>
                ))}
              </Box>
            </Box>
          </>
        )}
      />
    </Box>
  );
};

export default UserForm;
