{"name": "metal-x-up", "version": "0.1.0", "private": true, "engines": {"node": ">=20.0.0"}, "scripts": {"dev": "next dev", "build": "next build", "build:dev": "cp .env.develop .env && next build", "build:prod": "cp .env.production .env && next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@azure/msal-browser": "^4.18.0", "@azure/msal-react": "^3.0.16", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.14.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.8", "@hookform/resolvers": "^3.3.4", "@microsoft/signalr": "^9.0.6", "@mui/icons-material": "^7.2.0", "@mui/lab": "^7.0.0-beta.10", "@mui/material": "^7.0.1", "@mui/x-data-grid": "^8.10.2", "@mui/x-date-pickers": "^8.10.2", "@reduxjs/toolkit": "^2.2.3", "@tabler/icons-react": "^3.33.0", "@types/node": "^20.2.3", "apexcharts": "4.7.0", "axios": "^1.7.2", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "eslint": "8.46.0", "eslint-config-next": "13.4.12", "hugeicons-react": "^0.3.0", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.536.0", "next": "^15.4.6", "next-i18next": "^15.4.2", "react": "19.1.0", "react-apexcharts": "1.7.0", "react-dom": "19.1.0", "react-hook-form": "^7.51.3", "react-i18next": "^15.5.2", "react-mui-sidebar": "^1.6.3", "react-pdf": "^10.1.0", "react-redux": "^9.1.2", "react-syntax-highlighter": "^15.6.1", "typescript": "5.7.3", "yup": "^1.6.1", "zod": "^3.22.4"}, "devDependencies": {"@types/axios": "^0.14.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "4.14.196", "@types/react-syntax-highlighter": "^15.5.13"}}