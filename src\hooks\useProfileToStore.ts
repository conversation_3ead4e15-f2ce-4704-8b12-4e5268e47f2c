import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { setUserAccessMenus, setUserProfile } from "@/features/users/state/usersSlice";
import { getProfile } from "@/services/profileService";
import { useMsal } from "@azure/msal-react";

export function useProfileToStore() {
  const [isLoaded, setIsLoaded] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    getProfile()
      .then((res) => {
        dispatch(setUserProfile(res.data.data));
        dispatch(setUserAccessMenus(res.data.data.accessMenus));
        if (res.data.data.accessMenus) {
          localStorage.setItem('accessMenus', JSON.stringify(res.data.data.accessMenus));
        }
      })
      .finally(() => setIsLoaded(true))
      .catch((e) => {
        console.error("Error fetching profile:", e);
        dispatch(setUserProfile(null));
        dispatch(setUserAccessMenus([]));
      });
  }, [dispatch]);
  return isLoaded;
}
