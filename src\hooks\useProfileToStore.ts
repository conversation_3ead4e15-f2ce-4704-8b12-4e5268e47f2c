import { setUserAccessMenus, setUserProfile } from "@/features/users/state/usersSlice";
import { getProfile } from "@/services/profileService";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";

export function useProfileToStore() {
  const [isLoaded, setIsLoaded] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    getProfile()
      .then((res) => {
        dispatch(setUserProfile(res.data.data));
        dispatch(setUserAccessMenus(res.data.data.accessMenus));
        if (res.data.data.accessMenus) {
          localStorage.setItem('accessMenus', JSON.stringify(res.data.data.accessMenus));
        }
      })
      .finally(() => setIsLoaded(true))
      .catch((e) => {
        dispatch(setUserProfile(null));
        dispatch(setUserAccessMenus([]));
      });
  }, [dispatch]);
  return isLoaded;
}
