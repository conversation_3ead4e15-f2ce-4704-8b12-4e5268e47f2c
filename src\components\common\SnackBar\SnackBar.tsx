import React from "react";
import Snackbar from "@mui/material/Snackbar";
import <PERSON><PERSON><PERSON><PERSON><PERSON>, { AlertColor, AlertProps } from "@mui/material/Alert";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { Box, Slide } from "@mui/material";
import { Cancel, Error, Info } from "@mui/icons-material";
import {
  alertStyles,
  boxIconStyles,
  titleBoxStyles,
  messageBoxStyles,
  actionStyles,
} from "./Snackbar.styles";

export interface SnackBarProps {
  open: boolean;
  title?: string;
  message: string;
  severity?: AlertColor; // 'success' | 'info' | 'warning' | 'error'
  autoHideDuration?: number;
  onClose?: (event?: React.SyntheticEvent | Event, reason?: string) => void;
  anchorOrigin?: {
    vertical: "top" | "bottom";
    horizontal: "left" | "center" | "right";
  };
}

const Alert = React.forwardRef<HTMLDivElement, AlertProps>(function Alert(
  props,
  ref
) {
  return <MuiAlert elevation={0} ref={ref} variant="outlined" {...props} />;
});

const SnackBar: React.FC<SnackBarProps> = ({
  open,
  title,
  message,
  severity = "success",
  autoHideDuration = 3000,
  onClose,
  anchorOrigin = { vertical: "bottom", horizontal: "center" },
}) => {
  const iconMapping = (severity: AlertColor) => {
    const getIcon = () => {
      switch (severity) {
        case "success":
          return <CheckCircleIcon fontSize="small" />;
        case "info":
          return <Info fontSize="small" />;
        case "warning":
          return <Error fontSize="small" />;
        case "error":
          return <Cancel fontSize="small" />;
        default:
          return <Info fontSize="small" />;
      }
    };

    return <Box sx={boxIconStyles(severity)}>{getIcon()}</Box>;
  };

  const titleMapping = (severity: string) => {
    switch (severity) {
      case "success":
        return "Success!";
      case "info":
        return "Information";
      case "warning":
        return "Warning";
      case "error":
        return "Error";
      default:
        return "Information";
    }
  };

  return (
    <Snackbar
      open={open}
      autoHideDuration={autoHideDuration}
      onClose={onClose}
      anchorOrigin={anchorOrigin}
      slots={{ transition: Slide }}
    >
      <Alert
        severity={severity}
        icon={iconMapping(severity)}
        onClose={onClose}
        sx={alertStyles(severity)}
        slotProps={{
          action: {
            sx: actionStyles,
          },
        }}
      >
        <Box>
          <Box
            title={title || titleMapping(severity)}
            sx={titleBoxStyles(severity)}
          >
            {title || titleMapping(severity)}
          </Box>
          <Box sx={messageBoxStyles}>{message}</Box>
        </Box>
      </Alert>
    </Snackbar>
  );
};

export default SnackBar;
