import { getRoleByOrganizationCode, role } from "@/features/role/state/roleSlice";
import { useEffect } from "react";
import { useDispatch } from "react-redux";

export function useRoleToStore(organizationCode: string) {
  const dispatch = useDispatch();
  useEffect(() => {
    if (!organizationCode) {
      return;
    }
    dispatch(role(organizationCode) as any);
  }, [dispatch, organizationCode]);
}

export function useTempRoleToStore(organizationCode: string) {
  const dispatch = useDispatch();
  useEffect(() => {
    if (!organizationCode) {
      return;
    }
    dispatch(getRoleByOrganizationCode(organizationCode) as any);
  }, [dispatch, organizationCode]);
}
