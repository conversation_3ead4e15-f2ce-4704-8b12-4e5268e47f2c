"use client";
import PageContainer from "@/app/(DashboardLayout)/components/container/PageContainer";
import Button from "@/components/common/button/Button";
import type { FieldConfig } from "@/components/common/DynamicForm/DynamicForm.types";
import UserList from "@/components/feature/UserMaster/UserList/UserList";
import { PAGE_TITLES } from "@/constants/routes";
import { useScreenAccessDecision } from "@/features/auth/hooks/ScreenAccessDecision";
import { useBreadcrumb } from "@/features/breadcrumb/hook/useBreadcrumb";
import AddIcon from "@mui/icons-material/Add";
import { Box } from "@mui/material";
import NextLink from "next/link";
import React from "react";

const UserMaster = () => {
  const { canDecide, canViewByPath } = useScreenAccessDecision();
  const showCreateBtn = canDecide && canViewByPath("/user-master/create");
  useBreadcrumb({
    pageTitle: PAGE_TITLES.USER_LIST,
    items: [
      { id: "user-master", title: PAGE_TITLES.USER_LIST, href: "/user-master" },
    ],
  });
  const defaultValues = React.useMemo(() => ({}), []);

  return (
    <PageContainer
      title={PAGE_TITLES.USER_LIST}
      description={"this is " + PAGE_TITLES.USER_LIST}
    >
      <Box
        sx={{ display: "flex", flexDirection: "column", position: "relative" }}
      >
        {showCreateBtn && (
          <Button
            href="user-master/create"
            LinkComponent={NextLink}
            sx={{ position: "absolute", right: 0, top: -65 }}
            type="button"
            variant="contained"
            size="small"
            startIcon={<AddIcon />}
          >
            Add New User
          </Button>
        )}

        <UserList searchValue={defaultValues} />
      </Box>
    </PageContainer>
  );
};

export default UserMaster;
