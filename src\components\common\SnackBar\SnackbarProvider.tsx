import React, { createContext, useCallback, useContext, useState } from "react";
import SnackBar from "./SnackBar";
import { AlertColor } from "@mui/material";

interface SnackbarContextType {
  showSnackbar: (
    title: string,
    message: string,
    severity?: AlertColor,
    duration?: number
  ) => void;
}

const SnackbarContext = createContext<SnackbarContextType>({
  showSnackbar: () => {},
});

export const useSnackbar = () => useContext(SnackbarContext);

export const SnackbarProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [severity, setSeverity] = useState<AlertColor>("success");
  const [autoHideDuration, setAutoHideDuration] = useState<number>(3000);
  const [title, setTitle] = useState("");
  const showSnackbar = useCallback(
    (
      title: string,
      msg: string,
      sev: AlertColor = "success",
      duration = 3000
    ) => {
      setMessage(msg);
      setTitle(title);
      setSeverity(sev);
      setAutoHideDuration(duration);
      setOpen(true);
    },
    []
  );

  return (
    <SnackbarContext.Provider value={{ showSnackbar }}>
      {children}
      <SnackBar
        open={open}
        title={title}
        message={message}
        severity={severity}
        autoHideDuration={autoHideDuration}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        onClose={() => setOpen(false)}
      />
    </SnackbarContext.Provider>
  );
};
