"use client";

import { showForceUserNotFoundModal } from "@/features/modal/modalSlice";
import { setUserMasterDetail } from "@/features/users/state/usersSlice";
import { UserMaster } from "@/features/users/types/user";
import * as userMasterService from "@/services/userMasterService";
import { InfoFieldData } from "@/types/user";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import PageContainer from "../../components/container/PageContainer";
import Loading from "../../loading";
import UserMasterClient from "./UserMasterClient";

export default function UserMasterDetailPage() {
  const searchParams = useSearchParams();
  const userId = searchParams.get("id");
  const [isLoading, setLoading] = useState(true);
  const [infoFields, setInfoFields] = useState<InfoFieldData[]>([]);
  const [infoUser, setInfoUser] = useState<UserMaster>();
  const [securityFields, setSecurityFields] = useState<InfoFieldData[]>([]);
  const dispatch = useDispatch();

  const [userNotFound, setUserNotFound] = useState(false);

  useEffect(() => {
    if (!userId) {
      return;
    }
    userMasterService
      .getDetailUserMaster(userId)
      .then((res) => {
        const user: UserMaster = res?.["data"] as any;
        dispatch(setUserMasterDetail(user));
        setInfoUser(user);
        const userInforField: InfoFieldData[] = [
          { label: "First Name", value: user.firstName },
          { label: "Last Name", value: user.lastName },
          {
            label: "Company",
            value: user?.organizationName || "",
          },
          { label: "User ID", value: user.userId },
          {
            label: "Department",
            value: user?.departmentName || "",
          },
          {
            label: "Country",
            value: user?.countryName || "",
          },
          {
            label: "Role",
            value: user?.roleName || "",
          },
        ];
        setInfoFields(userInforField);
        const userSecurityField: InfoFieldData[] = [
          { label: "Password", value: "***********" },
          {
            label: "Last Password Updated",
            value: user?.lastPasswordUpdate || "",
          },
        ];
        setSecurityFields(userSecurityField);
      })
      .catch((error) => {
        const code = error?.response?.data?.code || "";
        if (code === "E_UMA006") {
          setUserNotFound(true);
          dispatch(showForceUserNotFoundModal());
        }
        setLoading(false);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [userId, dispatch]);

  if (isLoading) {
    return <Loading />;
  }

  if (userNotFound) {
    return null;
  }

  if (!userId) {
    return <div>User ID is required</div>;
  }

  return (
    <>
      <PageContainer title="User Detail" description="this is User Detail page">
        <UserMasterClient infoUser={infoUser} userId={userId} infoFields={[infoFields]} securityFields={[securityFields]} />
      </PageContainer>
    </>
  );
}
