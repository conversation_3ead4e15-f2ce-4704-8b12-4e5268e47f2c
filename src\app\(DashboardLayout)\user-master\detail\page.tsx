"use client";

import { UserMaster } from "@/features/users/types/user";
import * as userMasterService from "@/services/userMasterService";
import { InfoFieldData } from "@/types/user";
import { useSearchParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import Loading from "../../loading";
import UserMasterClient from "./UserMasterClient";
import { useSelector, useDispatch } from "react-redux";
import { setUserMasterDetail } from "@/features/users/state/usersSlice";
import { getDepartmentList } from "@/features/department/state/departmentSelector";
import { getRoleList } from "@/features/role/state/roleSelector";
import { getCountryList } from "@/features/country/state/countrySelector";
import { getOrganizationList } from "@/features/organization/state/organizationSelector";
import PageContainer from "../../components/container/PageContainer";

export default function UserMasterDetailPage() {
  const searchParams = useSearchParams();
  const userId = searchParams.get("id");
  const [isLoading, setLoading] = useState(true);
  const [infoFields, setInfoFields] = useState<InfoFieldData[]>([]);
  const [infoUser, setInfoUser] = useState<UserMaster>();
  const [securityFields, setSecurityFields] = useState<InfoFieldData[]>([]);
  const departmentList = useSelector(getDepartmentList);
  const roleList = useSelector(getRoleList);
  const countryList = useSelector(getCountryList);
  const organizationList = useSelector(getOrganizationList);
  const dispatch = useDispatch();

  const departmentByCode = useMemo(() => Object.fromEntries(departmentList.map((d) => [d.departmentCode, d])), [departmentList]);
  const roleByCode = useMemo(() => Object.fromEntries(roleList.map((r) => [r.roleCode, r])), [roleList]);
  const countryByCode = useMemo(() => Object.fromEntries(countryList.map((c) => [c.countryCode, c])), [countryList]);
  const organizationByCode = useMemo(
    () => Object.fromEntries(organizationList.map((o) => [o.organizationCode, o])),
    [organizationList]
  );

  useEffect(() => {
    if (!userId) return;
    userMasterService
      .getDetailUserMaster(userId)
      .then((res) => {
        const user: UserMaster = res?.["data"] as any;
        dispatch(setUserMasterDetail(user));
        setInfoUser(user);
        const userInforField: InfoFieldData[] = [
          { label: "First Name", value: user.firstName },
          { label: "Last Name", value: user.lastName },
          {
            label: "Company",
            value: organizationByCode[user?.organizationCode]?.organizationName || "",
          },
          { label: "User ID", value: user.userId },
          {
            label: "Department",
            value: departmentByCode[user?.departmentCode]?.departmentName || "",
          },
          {
            label: "Country",
            value: countryByCode[user?.countryCode]?.countryName || "",
          },
          {
            label: "Role",
            value: roleByCode[user?.roleCode]?.roleName || "",
          },
        ];
        setInfoFields(userInforField);
        const userSecurityField: InfoFieldData[] = [
          { label: "Password", value: "***********" },
          {
            label: "Last Password Updated",
            value: user?.lastPasswordUpdate || "",
          },
        ];
        setSecurityFields(userSecurityField);
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [userId, dispatch, departmentByCode, roleByCode, countryByCode, organizationByCode]);

  if (isLoading) {
    return <Loading />;
  }

  if (!userId) {
    return <div>User ID is required</div>;
  }

  return (
    <>
      <PageContainer title="User Detail" description="this is User Detail page">
        <UserMasterClient infoUser={infoUser} userId={userId} infoFields={[infoFields]} securityFields={[securityFields]} />
      </PageContainer>
    </>
  );
}
