"use client";
import Breadcrumb from "@/components/common/breadcrumb/Breadcrumb";
import { Box, Container, styled } from "@mui/material";
// import { MenuItemsHidden } from "@/app/(DashboardLayout)/layout/sidebar/MenuItems";
import AccessGuard from "./AccessGuard";

import Header from "@/app/(DashboardLayout)/layout/header/Header";
import Sidebar from "@/app/(DashboardLayout)/layout/sidebar/Sidebar";
import ProfileLoader from "@/components/common/ProfileLoader";
import { useScreenAccessDecision } from "@/features/auth/hooks/ScreenAccessDecision";
import { useMsalGuard } from "@/features/auth/hooks/useMsalGuard";
import { useMsalToken } from "@/features/auth/hooks/useMsalToken";
import { useCountryToStore } from "@/hooks/useCountryToStore";
import { useDepartmentToStore } from "@/hooks/useDepartmentToStore";
import { useOrganizationToStore } from "@/hooks/useOrganizationToStore";
import { useProfileToStore } from "@/hooks/useProfileToStore";
import { useRoleToStore } from "@/hooks/useRoleToStore";
import { usePathname } from "next/navigation";
import React, { useEffect, useState } from "react";
import { useUser } from "@/features/users/state/useUser";

const MainWrapper = styled("div")(() => ({
  display: "flex",
  minHeight: "100vh",
  width: "100%",
  backgroundColor: "#ECEFF4",
}));

const HEADER_HEIGHT = 64;
const PageWrapper = styled("div")(() => ({
  display: "flex",
  flexDirection: "column",
  flexGrow: 1,
  height: "100vh",
  backgroundColor: "#FAFAFA",
  borderRadius: "32px 0 0 32px",
  overflow: "hidden",
  position: "relative",
}));

interface Props {
  children: React.ReactNode;
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  const [isSidebarOpen, setSidebarOpen] = useState(true);
  const [isMobileSidebarOpen, setMobileSidebarOpen] = useState(false);
  useProfileToStore();
  const {user} = useUser();
  useDepartmentToStore();
  useCountryToStore();
  useOrganizationToStore();
  useRoleToStore(user?.organizationCode || "");
  const pathname = usePathname();
  const isAuthenticated = useMsalGuard();
  const { hasAccess, canDecide } = useScreenAccessDecision();
  useMsalToken(["openid", "0b135d95-2488-4110-8042-aef3b76766ed/API.Write", "0b135d95-2488-4110-8042-aef3b76766ed/API.Read"]);
  if (!isAuthenticated) return null;
  const hideBreadcrumb = canDecide && !hasAccess;
  return (
    <MainWrapper className="mainwrapper">
      <ProfileLoader />
      <Sidebar
        isSidebarOpen={isSidebarOpen}
        isMobileSidebarOpen={isMobileSidebarOpen}
        onSidebarClose={() => setMobileSidebarOpen(false)}
      />
      <PageWrapper className="page-wrapper">
        <Box
          sx={{
            borderTopLeftRadius: "32px",
            borderTopRightRadius: 0,
            height: `${HEADER_HEIGHT}px`,
            boxShadow: "0 2px 8px 0 rgba(0,0,0,0.03)",
          }}
        >
          <Header toggleMobileSidebar={() => setMobileSidebarOpen(true)} />
        </Box>
        <Box
          sx={{
            flex: 1,
            overflowY: "auto",
            height: `calc(100vh - ${HEADER_HEIGHT}px)`,
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Container sx={{ maxWidth: "1200px" }}>
            {!hideBreadcrumb && <Breadcrumb />}
            <Box sx={{ minHeight: "calc(100vh - 170px)" }}>
                <AccessGuard>{children}</AccessGuard>
            </Box>
          </Container>
        </Box>
      </PageWrapper>
    </MainWrapper>
  );
}
