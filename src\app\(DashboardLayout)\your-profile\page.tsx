"use client";

import Popup<PERSON>hangePassword from "@/components/common/PopupChangePassword";
import { useBreadcrumb } from "@/features/breadcrumb/hook/useBreadcrumb";
import { getCountryList } from "@/features/country/state/countrySelector";
import { getDepartmentList } from "@/features/department/state/departmentSelector";
import { getOrganizationList } from "@/features/organization/state/organizationSelector";
import { getRoleList } from "@/features/role/state/roleSelector";
import { selectUserProfile } from "@/features/users/state/usersSelectors";
import { setUserProfile } from "@/features/users/state/usersSlice";
import { UserMaster } from "@/features/users/types/user";
import { signOutMsal } from "@/services/msalService";
import { getProfile } from "@/services/profileService";
import * as userMasterService from "@/services/userMasterService";
import { InfoFieldData } from "@/types/user";
import { useMsal } from "@azure/msal-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>on, Tab, Ta<PERSON>, Typography } from "@mui/material";
import Image from "next/image";
import React, { useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Loading from "../loading";
import ProfileHeader from "../user-master/ProfileHeader";
import PageContainer from "../components/container/PageContainer";

interface InfoFieldProps {
  label: string;
  value: string;
  onEditPassword?: () => void;
}

const InfoField: React.FC<InfoFieldProps> = ({ label, value, onEditPassword }) => (
  <Box
    sx={{
      width: 374,
      display: "flex",
      flexDirection: "column",
      gap: 1,
      borderBottom: "1px solid rgba(162, 161, 168, 0.2)",
    }}
  >
    <Typography
      sx={{
        color: "#bfc4cc",
        fontSize: 14,
        fontFamily: "Roboto, sans-serif",
        mb: "5px",
      }}
    >
      {label}
    </Typography>
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
      }}
    >
      <Typography
        sx={{
          color: "#222222",
          fontSize: 16,
          fontFamily: "Roboto, sans-serif",
        }}
      >
        {value}
      </Typography>
      {label === "Password" && onEditPassword && (
        <IconButton size="small" sx={{ ml: 1 }} onClick={onEditPassword}>
          <Image src="/images/user/pencil.svg" alt="pencil edit" height={24} width={24} />
        </IconButton>
      )}
    </Box>
  </Box>
);

const YourProfile = () => {
  const userProfile = useSelector(selectUserProfile);
  useBreadcrumb({
    pageTitle: "Your Profile",
    items: [
      {
        id: "your-profile",
        title: `${userProfile?.lastName} ${userProfile?.firstName}`,
      },
    ],
  });
  const departmentList = useSelector(getDepartmentList);
  const roleList = useSelector(getRoleList);
  const countryList = useSelector(getCountryList);
  const organizationList = useSelector(getOrganizationList);
  const departmentByCode = useMemo(() => Object.fromEntries(departmentList.map((d) => [d.departmentCode, d])), [departmentList]);

  const roleByCode = useMemo(() => Object.fromEntries(roleList.map((r) => [r.roleCode, r])), [roleList]);

  const countryByCode = useMemo(() => Object.fromEntries(countryList.map((c) => [c.countryCode, c])), [countryList]);

  const organizationByCode = useMemo(
    () => Object.fromEntries(organizationList.map((o) => [o.organizationCode, o])),
    [organizationList]
  );
  const infoFields = useMemo<InfoFieldData[]>(
    () =>
      userProfile
        ? [
            { label: "First Name", value: userProfile.firstName },
            { label: "Last Name", value: userProfile.lastName },
            {
              label: "Company",
              value: organizationByCode[userProfile?.organizationCode]?.organizationName || "",
            },
            { label: "Operator ID", value: userProfile.userId },
            {
              label: "Department",
              value: departmentByCode[userProfile?.departmentCode]?.departmentName || "",
            },
            {
              label: "Country",
              value: countryByCode[userProfile?.countryCode]?.countryName || "",
            },
            {
              label: "Role",
              value: roleByCode[userProfile?.roleCode]?.roleName || "",
            },
          ]
        : [],
    [userProfile, organizationByCode, departmentByCode, countryByCode, roleByCode]
  );

  const securityFields = useMemo<InfoFieldData[]>(
    () =>
      userProfile
        ? [
            { label: "Password", value: "***********" },
            {
              label: "Last Password Updated",
              value: userProfile?.lastPasswordUpdate || "",
            },
          ]
        : [],
    [userProfile]
  );
  const userClientId = userProfile?.clientId;
  const lockStatus = !userProfile?.lockStatus;
  const securField = useMemo(() => securityFields, [securityFields]);
  const displayInfoFields = Array.isArray(infoFields) && infoFields.length > 0 ? infoFields : [];
  const [openChangePassword, setOpenChangePassword] = useState(false);
  const [tabIndex, setTabIndex] = useState(0);
  const dispatch = useDispatch();
  const { instance, accounts } = useMsal();

  let currentUserId = "";
  if (typeof window !== "undefined") {
    try {
      if (userProfile) {
        currentUserId = userProfile.clientId || "";
      }
    } catch {}
  }
  const showSecurityTab = userClientId === currentUserId;
  const [avatarUrl, setAvatarUrl] = useState<string>(userProfile?.imageUrl || "");

  const handleUploadAvatar = async (formData: FormData) => {
    try {
      const res = await userMasterService.uploadAvatar(formData);
      setAvatarUrl(res.imageUrl);
      const userParsed: UserMaster = {
        countryCode: userProfile?.countryCode || "",
        countryName: userProfile?.countryName || "",
        departmentCode: userProfile?.departmentCode || "",
        departmentName: userProfile?.departmentName || "",
        firstName: userProfile?.firstName || "",
        lastName: userProfile?.lastName || "",
        imageUrl: res.imagePath,
        userId: userProfile?.userId || "",
        organizationCode: userProfile?.organizationCode || "",
        organizationName: userProfile?.organizationName || "",
        roleCode: userProfile?.roleCode || "",
        roleName: userProfile?.roleName || "",
      };
      await userMasterService.updateDetailUserMaster(userProfile?.clientId || "", userParsed);
      await fetchUserProfile();
    } catch (error) {
      console.log(error);
    }
  };
  const fetchUserProfile = async () => {
    getProfile().then((res) => {
      dispatch(setUserProfile(res.data.data));
    });
  };

  if (!userProfile) {
    return <Loading />;
  }

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabIndex(newValue);
  };

  const removeSpace = (str: string) => {
    return str.replace(/ (\w)/g, (_, c) => c.toUpperCase());
  };

  return (
    <>
      <PageContainer title="Your Profile" description="this is Your Profile page">
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: "10px",
            width: "100%",
            height: "100%",
            padding: "20px",
            borderRadius: "12px",
            border: "1px solid rgba(191, 196, 204, 1)",
            position: "relative",
            background: "#fff",
            minHeight: "567px",
          }}
        >
          <PopupChangePassword
            open={openChangePassword}
            onClose={() => setOpenChangePassword(false)}
            onSubmit={(values) => {
              const infoPassword = {
                clientId: userClientId,
                oldPassword: values.oldPassword,
                newPassword: values.newPassword,
                confirmNewPassword: values.confirmNewPassword,
              };
              userMasterService
                .updateChangePassword(infoPassword)
                .then(() => {
                  setOpenChangePassword(false);
                  signOutMsal(instance, accounts[0]);
                })
                .catch((error) => {
                  console.log(error);
                });
            }}
            errors={{}}
          />
          <ProfileHeader
            name={`${userProfile.lastName} ${userProfile.firstName}`}
            roleName={userProfile.roleName}
            userId={userProfile.userId}
            lockStatus={lockStatus}
            avatarUrl={avatarUrl}
            onUpload={handleUploadAvatar}
            mode={"edit"}
          />
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: 2.5,
              width: "100%",
            }}
          >
            {/* Tabbar */}
            <Tabs value={tabIndex} onChange={handleTabChange} sx={{ mb: 3, borderBottom: "1px solid rgba(162,161,168,0.2)" }}>
              <Tab
                icon={
                  <Image
                    src={`/images/user/${tabIndex === 0 ? "user-tab.svg" : "user-tab-inactive.svg"}`}
                    alt="user tab"
                    width={24}
                    height={24}
                  ></Image>
                }
                iconPosition="start"
                label="Personal Information"
                sx={{
                  fontWeight: 400,
                  fontSize: 16,
                  color: "rgba(34, 34, 34, 1)",
                  minHeight: 48,
                  minWidth: 220,
                  textTransform: "none",
                  "&.Mui-selected": {
                    color: "primary.main",
                    borderBottom: "3px solid",
                    fontWeight: 700,
                  },
                }}
              />
              {showSecurityTab && (
                <Tab
                  icon={
                    <Image
                      src={`/images/user/${tabIndex === 1 ? "lock-tab.svg" : "lock-tab-inactive.svg"}`}
                      alt="user tab"
                      width={24}
                      height={24}
                    ></Image>
                  }
                  iconPosition="start"
                  label="Security"
                  sx={{
                    fontWeight: 400,
                    fontSize: 16,
                    color: "rgba(34, 34, 34, 1)",
                    minHeight: 48,
                    minWidth: 220,
                    textTransform: "none",
                    "&.Mui-selected": {
                      color: "primary.main",
                      borderBottom: "3px solid",
                      fontWeight: 700,
                    },
                  }}
                />
              )}
            </Tabs>
            {tabIndex === 0 && (
              <>
                {[displayInfoFields].map((row) => (
                  <Box
                    key={row.map((f) => f.label).join("-")}
                    sx={{ display: "flex", gap: 1.25, columnGap: 3.75, flexWrap: "wrap" }}
                  >
                    {row.map((field) => (
                      <InfoField key={removeSpace(field.label)} label={field.label} value={field.value} />
                    ))}
                    {/* If row has only one field, add empty box for layout */}
                    {row.length === 1 && <Box sx={{ width: 374 }} />}
                  </Box>
                ))}
              </>
            )}
            {tabIndex === 1 && showSecurityTab && (
              <>
                {[securField].map((row) => (
                  <Box key={row.map((f) => f.label).join("-")} sx={{ display: "flex", gap: 1.25, columnGap: 3.75 }}>
                    {row.map((field) => (
                      <InfoField
                        key={removeSpace(field.label)}
                        label={field.label}
                        value={field.value}
                        onEditPassword={field.label === "Password" ? () => setOpenChangePassword(true) : undefined}
                      />
                    ))}
                    {/* If row has only one field, add empty box for layout */}
                    {row.length === 1 && <Box sx={{ width: 374 }} />}
                  </Box>
                ))}
              </>
            )}
          </Box>
        </Box>
      </PageContainer>
    </>
  );
};

export default YourProfile;
