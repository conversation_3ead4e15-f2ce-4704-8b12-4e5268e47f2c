import React from "react";
import { Box, Button, TextField, MenuItem } from "@mui/material";
import type { FieldConfig } from "@/components/common/DynamicForm/DynamicForm.types";

interface UserCreateFormProps {
  fields: FieldConfig[];
  onSubmit: (data: any) => void;
}

const UserCreateForm: React.FC<UserCreateFormProps> = ({
  fields,
  onSubmit,
}) => {
  const [form, setForm] = React.useState<any>({});
  const [errors, setErrors] = React.useState<any>({});

  const handleChange = (name: string, value: any) => {
    setForm((prev: any) => ({ ...prev, [name]: value }));
  };

  const validate = () => {
    const newErrors: any = {};
    fields.forEach((field) => {
      if (field.required && !form[field.name]) {
        newErrors[field.name] = field.validation?.required || "Required";
      }
      if (field.validation?.minLength && form[field.name]) {
        const minLength =
          typeof field.validation.minLength === "number"
            ? field.validation.minLength
            : field.validation.minLength.value;
        const message =
          typeof field.validation.minLength === "object"
            ? field.validation.minLength.message
            : `Minimum length is ${minLength}`;
        if (form[field.name].length < minLength) {
          newErrors[field.name] = message;
        }
      }
      if (field.validation?.maxLength && form[field.name]) {
        const maxLength =
          typeof field.validation.maxLength === "number"
            ? field.validation.maxLength
            : field.validation.maxLength.value;
        const message =
          typeof field.validation.maxLength === "object"
            ? field.validation.maxLength.message
            : `Maximum length is ${maxLength}`;
        if (form[field.name].length > maxLength) {
          newErrors[field.name] = message;
        }
      }
      if (field.validation?.pattern && form[field.name]) {
        const patternRule = field.validation.pattern;
        let pattern: RegExp;
        let message: string;

        if (patternRule instanceof RegExp) {
          pattern = patternRule;
          message = "Invalid format";
        } else {
          pattern = patternRule.value;
          message = patternRule.message;
        }

        if (!pattern.test(form[field.name])) {
          newErrors[field.name] = message;
        }
      }
      if (field.customValidation) {
        const result = field.customValidation(form[field.name], form);
        if (result !== true) newErrors[field.name] = result;
      }
    });
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validate()) {
      onSubmit(form);
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
          gap: 2,
        }}
      >
        {fields.map((field) => (
          <Box
            key={field.name}
            sx={{ gridColumn: field.type === "textarea" ? "1 / -1" : "auto" }}
          >
            {field.type === "select" ? (
              <TextField
                select
                fullWidth
                label={field.label}
                value={form[field.name] ?? field.defaultValue ?? ""}
                onChange={(e) => handleChange(field.name, e.target.value)}
                error={!!errors[field.name]}
                helperText={errors[field.name]}
                required={field.required}
              >
                {field.options?.map((opt) => (
                  <MenuItem key={String(opt.value)} value={String(opt.value)}>
                    {opt.label}
                  </MenuItem>
                ))}
              </TextField>
            ) : field.type === "textarea" ? (
              <TextField
                multiline
                rows={field.rows || 3}
                fullWidth
                label={field.label}
                value={form[field.name] ?? ""}
                onChange={(e) => handleChange(field.name, e.target.value)}
                error={!!errors[field.name]}
                helperText={errors[field.name]}
                required={field.required}
              />
            ) : (
              <TextField
                type={field.type}
                fullWidth
                label={field.label}
                placeholder={field.placeholder}
                value={form[field.name] ?? ""}
                onChange={(e) => handleChange(field.name, e.target.value)}
                error={!!errors[field.name]}
                helperText={errors[field.name]}
                required={field.required}
              />
            )}
          </Box>
        ))}
      </Box>
      <Box sx={{ mt: 3, display: "flex", justifyContent: "flex-end" }}>
        <Button variant="contained" color="primary" type="submit">
          Create User
        </Button>
      </Box>
    </Box>
  );
};

export default UserCreateForm;
