import React from "react";
import { Grid, Box, Card, Typography, IconButton } from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import MenuCard from "./MenuCard";

interface MenuItem {
  id: string;
  title: string;
  icon: React.ReactNode;
  color: string;
  bgGradient: string;
  href?: string;
  screenCode?: string;
  children?: MenuItem[];
}

interface GridMenuProps {
  showChildren: null | string;
  setShowChildren: (val: null | string) => void;
  millsheet?: MenuItem;
  filteredMenuItems: MenuItem[];
}

const GridMenu: React.FC<GridMenuProps> = ({
  showChildren,
  setShowChildren,
  millsheet,
  filteredMenuItems,
}) => {
  return (
    <Box sx={{ position: "relative", minHeight: "80vh" }}>
      <Box sx={{ p: 2.8 }}>
        <Box sx={{ mx: "auto" }}>
          {/* Main menu or children */}
          {showChildren === "millsheet-parent" && millsheet ? (
            <>
              <Grid
                container
                spacing={4}
                mb={8}
                justifyContent="flex-start"
                alignItems="center"
              >
                {Array.from({ length: 4 }).map((_, i) => {
                  const child = millsheet.children && millsheet.children[i];
                  if (child) {
                    return (
                      <Grid
                        size={3}
                        key={child.id}
                        sx={{ display: "flex", justifyContent: "center" }}
                      >
                        <MenuCard item={child} />
                      </Grid>
                    );
                  } else {
                    return (
                      <Grid
                        key={"empty-child-" + i}
                        sx={{ display: "flex", justifyContent: "center" }}
                      />
                    );
                  }
                })}
              </Grid>
              <Box
                sx={{
                  position: "fixed",
                  bottom: 40,
                  right: 32,
                  zIndex: 1300,
                }}
              >
                <IconButton
                  sx={{
                    color: "#CA002E",
                    border: "1px solid #CA002E",
                    borderRadius: 2,
                    px: 2,
                    py: 1,
                  }}
                  onClick={() => setShowChildren(null)}
                  aria-label="Back"
                >
                  <ExpandMoreIcon sx={{ transform: "rotate(90deg)" }} />
                  <span style={{ marginLeft: 8, fontWeight: 600 }}>Back</span>
                </IconButton>
              </Box>
            </>
          ) : (
            <Grid container spacing={4} mb={8}>
              {[0, 1, 2, 3].map((i) => {
                if (i < 2 && filteredMenuItems[i]) {
                  const item = filteredMenuItems[i];
                  if (item.id === "millsheet-parent") {
                    return (
                      <Grid
                        key={item.id}
                        sx={{ display: "flex", justifyContent: "center" }}
                      >
                        <Box sx={{ maxWidth: 240 }}>
                          <Card
                            sx={{
                              position: "relative",
                              overflow: "visible",
                              borderRadius: "16px",
                              padding: "25px",
                              boxShadow:
                                "0px 2px 4px -2px rgba(0,0,0,0.1), 0px 4px 6px -1px rgba(0,0,0,0.1)",
                              cursor: "pointer",
                              minHeight: 148,
                              minWidth: 240,
                              maxWidth: 270,
                              height: 148,
                              display: "flex",
                              flexDirection: "column",
                              alignItems: "center",
                              justifyContent: "center",
                              textAlign: "center",
                              border: "1px solid rgba(229,231,235,1)",
                              "&:hover": {
                                border: "none",
                                transform: "scale(1.03) translateY(-4px)",
                                borderBottom: "4px solid #CA002E",
                                boxShadow:
                                  "0px 2px 4px -2px rgba(0,0,0,0.1), 0px 4px 6px -1px rgba(0,0,0,0.1)",
                              },
                            }}
                            onClick={() => setShowChildren("millsheet-parent")}
                          >
                            <Box
                              sx={{
                                width: 64,
                                height: 64,
                                backgroundColor: "#CA002E",
                                borderRadius: "12px",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                mb: 2,
                                transition: "transform 0.3s",
                                padding: "8px 8px 4px 8px",
                              }}
                            >
                              <Box sx={{ color: "#fff" }}>{item.icon}</Box>
                            </Box>
                            <Typography
                              variant="subtitle1"
                              fontWeight={600}
                              color="grey.800"
                              fontSize={14}
                              gutterBottom
                            >
                              {item.title}
                            </Typography>
                            <IconButton
                              sx={{
                                position: "absolute",
                                right: 12,
                                top: 12,
                                color: "#CA002E",
                              }}
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                setShowChildren("millsheet-parent");
                              }}
                              aria-label="Expand"
                            >
                              <MoreVertIcon />
                            </IconButton>
                          </Card>
                        </Box>
                      </Grid>
                    );
                  } else {
                    return (
                      <Grid
                        key={item.id}
                        sx={{ display: "flex", justifyContent: "center" }}
                      >
                        <MenuCard item={item} />
                      </Grid>
                    );
                  }
                } else {
                  return (
                    <Grid
                      key={"empty-parent-" + i}
                      sx={{ display: "flex", justifyContent: "center" }}
                    />
                  );
                }
              })}
            </Grid>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default GridMenu;