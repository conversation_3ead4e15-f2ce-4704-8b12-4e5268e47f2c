import CloseIcon from "@mui/icons-material/Close";
import GppGoodIcon from "@mui/icons-material/GppGood";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import React from "react";
import DynamicForm from "./DynamicForm";
import changePasswordConfig from "./DynamicForm/changePasswordConfig";

interface PopupChangePasswordProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: any) => void;
  errors: {}
}

const PopupChangePassword: React.FC<PopupChangePasswordProps> = ({
  open,
  onClose,
  onSubmit,
  errors,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="xs"
      fullWidth
      PaperProps={{
        sx: {
          width: 400,
          minHeight: 680,
          borderRadius: "16px",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          position: "relative",
          pb: 0,
        }}
      >
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{ position: "absolute", right: 10, top: 10 }}
        >
          <CloseIcon />
        </IconButton>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            width: "100%",
          }}
        >
          {/* Icon */}
          <div
            style={{
              marginBottom: 16,
              width: 40,
              height: 40,
              border: "1px solid #EAECF0",
              borderRadius: "12px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              marginTop: "16px",
            }}
          >
            <GppGoodIcon sx={{ fontSize: 24, color: "#FF9100" }} />
          </div>
          <Typography
            variant="h4"
            fontWeight={700}
            textAlign="center"
            color="#39000b"
            mb="12px"
          >
            Change Password
          </Typography>
        </div>
      </DialogTitle>
      <DialogContent sx={{ textAlign: "start", pt: 1 }}>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          ※Please set your password to at least 8 characters, include at least
          one letter or number, and do not use your user ID as your password.
        </Typography>
        <DynamicForm
          formClassName="changePasswordForm"
          fieldClassName="changePasswordField"
          hideCancel={true}
          fields={changePasswordConfig}
          onSubmit={onSubmit}
          submitLabel="Change Password"
          errors={errors}
        />
        <Button
          onClick={onClose}
          variant="outlined"
          color="primary"
          sx={{
            minWidth: 120,
            fontWeight: 600,
            width: "100%",
            marginY: "12px",
          }}
        >
          Cancel
        </Button>
      </DialogContent>
    </Dialog>
  );
};

export default PopupChangePassword;
