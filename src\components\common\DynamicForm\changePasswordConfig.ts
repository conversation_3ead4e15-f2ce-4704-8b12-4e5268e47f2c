import { FieldValues } from "react-hook-form";
import { FieldConfig } from "./DynamicForm.types";

const changePasswordConfig: FieldConfig[] = [
  {
    name: "oldPassword",
    label: "Old Password",
    type: "password",
    placeholder: "Enter your old password",
    customValidation: (
      value: string,
      values: FieldValues | undefined
    ): string | boolean | undefined => {
      if (!value || value.trim() === "") {
        return "Enter a current password.";
      }
    },
  },
  {
    name: "newPassword",
    label: "New Password",
    type: "password",
    placeholder: "Enter your new password",
    customValidation: (
      value: string,
      values: FieldValues | undefined
    ): string | boolean | undefined => {
      if (!value || value.trim() === "") {
        return "Enter a new password.";
      }

      if (value.trim() === values?.oldPassword.trim()) {
        return "The new password you entered is the same as your old password. Enter a different password.";
      }
      if (value.trim().length < 8) {
        return "Password must be at least 8 characters.";
      }

      const hasUpper = /[A-Z]/.test(value.trim());
      const hasLower = /[a-z]/.test(value.trim());
      const hasNumber = /[0-9]/.test(value.trim());
      const hasSpecial = /[^A-Za-z0-9]/.test(value.trim());
      const typesCount = [hasUpper, hasLower, hasNumber, hasSpecial].filter(
        Boolean
      ).length;

      if (typesCount < 3) {
        return "Passwords can't contain your user ID, and need to be at least 8 characters long, with at least 3 of the following: uppercase letters, lowercase letters, numbers, and symbols.";
      }

      if (value.trim().length === 0) {
        return "Please include both letters and numbers in your password.";
      }
      return true;
    },
  },
  {
    name: "confirmNewPassword",
    label: "Confirm New Password",
    type: "password",
    placeholder: "Confirm your new password",
    customValidation: (
      value: string,
      values: FieldValues | undefined
    ): string | boolean | undefined => {
      if (!value || value.trim() === "") {
        return "Enter a confirm password.";
      }
      if (value.trim() !== values?.newPassword.trim()) {
        return "These passwords don't match.";
      }
      return true;
    },
  },
  {
    name: "autoLogout",
    label:
      "I understand that I will be automatically logged out after changing my password.",
    type: "checkbox",
    customValidation: (value: boolean) => {
      if (!value) return "You must agree to continue";
      return true;
    },
  },
];

export default changePasswordConfig;
