import * as Yup from 'yup';
import { FieldConfig } from '../DynamicForm.types';

export const getYupValidation = (field: FieldConfig) => {
  let schema: Yup.AnySchema;
  switch (field.type) {
    case 'text': 
    case 'email':
    case 'password':
    case 'textarea': {
      let s = Yup.string();
      if (field.min) s = s.min(field.min, `Min ${field.min} characters`);
      if (field.max) s = s.max(field.max, `Max ${field.max} characters`);
      if (field.pattern) s = s.matches(field.pattern, 'Invalid format');
      schema = s;
      break;
    }
    case 'number': {
      let s = Yup.number();
      if (field.min) s = s.min(field.min, `Min value is ${field.min}`);
      if (field.max) s = s.max(field.max, `Max value is ${field.max}`);
      schema = s;
      break;
    }
    case 'date': {
      schema = Yup.date();
      break;
    }
    case 'checkbox': {
    schema = Yup.boolean();
      break;
    }
    case 'file': {
      schema = Yup.mixed();
      break;
    }
    case 'autocomplete': {
       schema = Yup.mixed();
      break;
    }
    default: {
      schema = Yup.mixed();
    }
  }
  if (field.required) {
    schema = schema.required(`${field.label} is a required field. Please enter it`);
  }
  if (field.customValidation) {
    schema = schema.test('custom', 'Invalid', function (value) {
      const result = field.customValidation?.(value, this.parent);
      if (typeof result === 'string') {
        return this.createError({ message: result });
      }
      return result ?? true;
    });
  }
  return schema;
};
