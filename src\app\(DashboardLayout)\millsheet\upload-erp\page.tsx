"use client";
import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Button,
  styled,
  Grid,
  Stack,
  Typography,
  CircularProgress,
} from "@mui/material";
import Image from "next/image";

import { useBreadcrumb } from "@/features/breadcrumb/hook/useBreadcrumb";
import PopupModal from "@/components/common/PopupModal";
import { useSelector } from "react-redux";
import {
  checkUpLoadERPFile,
  upLoadAllFile,
} from "@/features/millsheet/service/uploadService";
import { selectUserProfile } from "@/features/users/state/usersSelectors";
import UploadArea from "../upload-millsheet/UploadArea";

import WithUploadRequirements from "../upload-millsheet/WithUploadRequirements";
import SnackbarUpload from "../upload-millsheet/Snackbar";
import PaperUploadErp from "./PaperErp";
import { FileUpload, UploadFiles } from "@/features/millsheet/api/uploadApi";
import { HTTP_STATUS } from "@/constants/api";
import { PAGE_TITLES } from "@/constants/routes";

const CustomListRow = styled(Box)(({}) => ({
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  width: "100%",
  marginBottom: "5px",
  gap: "10px",
}));

const UploadAllIcon = () => (
  <Image
    src="/images/millsheet/upload-all.svg"
    alt="icon upload"
    width={20}
    height={20}
  />
);

const UploadDisableIcon = () => (
  <Image
    src="/images/millsheet/upload-disable.svg"
    alt="icon upload disable"
    width={20}
    height={20}
  />
);

const RecycleIcon = () => (
  <Image
    src="/images/millsheet/delete.svg"
    alt="icon recycle"
    width={20}
    height={20}
  />
);

type GroupedResult = {
  [key: string]: {
    item: File[];
  };
};

type SheetData = {
  item: File[];
};

type FilesMap = Record<string, SheetData>;

type ErrorResponse = {
  fields: string[];
  code: string;
  message: string;
  params: string[];
};

const ERP = "ERP_PROCESSING_JOB_ONLINE";

const MAX_FILE_SIZE_MB = 30;
const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

const ERROR_E_MILL004 = "E_MILL004";

const ERPDataUpload = () => {
  useBreadcrumb({
    pageTitle: PAGE_TITLES.ERP_DATA_UPLOAD,
    items: [
      { id: "millsheet", title: PAGE_TITLES.MILL_SHEET },
      { id: "millsheet-erp", title: PAGE_TITLES.ERP_DATA_UPLOAD },
    ],
  });

  const [dragOver, setDragOver] = useState(false);

  const [fileLength, setFileLength] = useState(0);

  const [fileLengthSuccess, setFileLengthSuccess] = useState(0);

  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const [open, setOpen] = useState(false);

  const [duplicateFile, setDuplicateFile] = useState(false);

  const [uploadSuccess, setUploadSuccess] = useState(false);

  const [fileProgress, setFileProgress] = useState<Record<string, number>>({});

  const [showUploadFile, setShowUploadFile] = useState(false);

  const [shouldDisableUploadAll, setShouldDisableUploadAll] = useState(true);

  const [isUploading, setIsUploading] = useState(false);

  const [files, setFiles] = useState<{}>({});

  const [errorFiles, setErrorFiles] = useState<ErrorResponse[]>([]);

  const [listFilesUpload, setListFilesUpload] = useState<FileUpload[]>([]);

  const userProfile = useSelector(selectUserProfile);

  useEffect(() => {
    document.title = PAGE_TITLES.ERP_DATA_UPLOAD;
  }, []);

  useEffect(() => {
    const errorMap = getErrorMap(errorFiles);

    let hasAnyError = false;
    let allFilesCompleted = true;
    let hasFiles = false;

    Object.entries(files as FilesMap).forEach(([_, sheetData]) => {
      if (sheetData.item.length === 0) return;

      hasFiles = true;

      const hasErrorFiles = sheetData.item.some((file) => {
        const error = errorMap.get(file.name);
        return !!error;
      });

      const hasFrontendError = sheetData.item.some((file) => {
        const isNotAcceptedFileType = ![
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ].includes(file.type);
        const isTooLarge = file.size > 30 * 1024 * 1024;

        return isNotAcceptedFileType || isTooLarge;
      });

      const allCompleted = sheetData.item.every(
        (file) => fileProgress[file.name] === 100
      );
      if (hasErrorFiles || hasFrontendError) {
        hasAnyError = true;
      }
      if (!allCompleted) {
        allFilesCompleted = false;
      }
    });

    const shouldDisable = !hasFiles || hasAnyError || !allFilesCompleted;
    setShouldDisableUploadAll(shouldDisable);
  }, [files, errorFiles, fileProgress]);

  useEffect(() => {
    const totalFilesCount = Object.values(files as FilesMap).reduce(
      (acc, group) => {
        return acc + group.item.length;
      },
      0
    );

    setFileLength(totalFilesCount);
    const haveFileUpload = countItemsWithValue100(fileProgress);
    if (haveFileUpload > 0) {
      setShowUploadFile(true);
    }
  }, [fileProgress, files]);

  const handleUpload = async (formData: FormData) => {
    try {
      const response = await checkUpLoadERPFile(formData);
      if (Array.isArray(response) && response.length > 0) {
        // Add new items to the existing list
        setListFilesUpload([...response]);
        setErrorFiles([]);
      }
    } catch (error: any) {
      if (error.status === HTTP_STATUS.INTERNAL_SERVER_ERROR) {
        removeListUpload();
      }
      const { data } = error.response || {};
      if (!data.success && data.code === ERROR_E_MILL004) {
        setOpen(true); // Show warning popup
        return;
      }
      if (Array.isArray(data?.errors)) {
        setErrorFiles((prevList) => [...prevList, ...data.errors]);
      }
    }
  };

  const handleUploadAll = async () => {
    setIsUploading(true);
    try {
      const convertFilesList = listFilesUpload.map((item) => {
        return {
          fileId: item.fileId,
          fileName: item.fileName,
          filePath: item.filePath,
          fileSize: item.fileSize,
        };
      });
      const finalUploadFiles: UploadFiles = {
        userId: userProfile?.userId ?? null,
        customerId: null,
        jobType: "0",
        jobName: ERP,
        organizationCode: userProfile?.organizationCode ?? "",
        files: convertFilesList,
      };

      const response = await upLoadAllFile(finalUploadFiles); // your upload function
      if (response.success) {
        // Set file count for Snackbar
        setFileLengthSuccess(listFilesUpload.length); // or formData.getAll('files').length
        // Show success Snackbar
        setUploadSuccess(true);
        // Reset file list
        removeListUpload();
      }
    } catch (error: any) {
    } finally {
      setIsUploading(false);
    }
  };

  const countItemsWithValue100 = (data: {}) => {
    return Object.values(data).filter((value) => value === 100).length;
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = () => {
    setDragOver(false);
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragOver(false);

    const droppedFiles = Array.from(e.dataTransfer.files) as File[];

    // Convert newly dropped files into grouped object format
    const groupedFromInput = convertToGroupedObject(droppedFiles);
    const mergedFiles: GroupedResult = { ...files };

    // Calculate the total number of existing files
    let totalFiles = Object.values(mergedFiles).reduce(
      (sum, group) => sum + group.item.length,
      0
    );

    const newFilesToUpload: File[] = [];
    let hasDuplicate = false;

    Object.entries(groupedFromInput).forEach(([key, value]) => {
      if (!mergedFiles[key]) {
        mergedFiles[key] = { item: [] };
      }

      const existingNames = new Set(mergedFiles[key].item.map((f) => f.name));
      const newItems = value.item.filter((f) => {
        if (existingNames.has(f.name)) {
          hasDuplicate = true;
          return false;
        }
        return true;
      });

      totalFiles += newItems.length;
      newFilesToUpload.push(...newItems);
    });

    // Show warning if duplicate file names are found
    if (hasDuplicate) {
      setDuplicateFile(true); // Show warning popup
      return;
    }

    // Check if total files after adding new ones exceed the limit
    if (totalFiles > 10) {
      setOpen(true); // Show warning popup
      return;
    }

    // Merge new files into existing list if within limit
    Object.entries(groupedFromInput).forEach(([key, value]) => {
      const existingNames = new Set(mergedFiles[key].item.map((f) => f.name));
      const newItems = value.item.filter((f) => !existingNames.has(f.name));
      //handle case for new item loading progress
      newItems.forEach((file) => simulateProgress(file));
      //push new item in list files
      mergedFiles[key].item.push(...newItems);
    });

    setFiles(mergedFiles);
    const allFilesToUpload: File[] = [];

    Object.values(mergedFiles).forEach((group) => {
      allFilesToUpload.push(...group.item);
    });

    // Filter files larger than 30MB
    const filesToUpload = allFilesToUpload?.filter(
      (file) => file.size <= MAX_FILE_SIZE_BYTES
    );

    if (filesToUpload.length > 0) {
      const formData = new FormData();
      filesToUpload.forEach((file) => {
        formData.append("files", file);
      });

      handleUpload(formData);
    }
  };

  const convertToGroupedObject = (data: File[]): GroupedResult => {
    const result: GroupedResult = {};
    data.forEach((entry) => {
      // Get the name before the last dot
      const key = entry.name.substring(0, entry.name.lastIndexOf("."));
      if (!result[key]) {
        result[key] = { item: [] };
      }
      result[key].item.push(entry);
    });

    return result;
  };

  const simulateProgress = (file: File) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      setFileProgress((prev) => ({
        ...prev,
        [file.name]: Math.min(progress, 100),
      }));
      if (progress >= 100) clearInterval(interval);
    }, 200);
  };

  const handleRemoveFile = (fileName: string) => {
    let remainingFiles: File[] = [];
    setFiles((prev) => {
      const updated: GroupedResult = { ...prev };
      Object.keys(updated).forEach((key) => {
        // for case delete item in list files
        updated[key].item = updated[key].item.filter(
          (f) => f.name !== fileName
        );

        // If no items left, delete the key
        if (updated[key].item.length === 0) {
          delete updated[key];
        }
      });
      // Reset file input value so selecting the same file again will trigger onChange

      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
      // Collect remaining files for upload
      remainingFiles = Object.values(updated).flatMap((group) => group.item);
      return updated;
    });

    setFileProgress((prev) => {
      const updated = { ...prev };
      delete updated[fileName];
      return updated;
    });
    // Remove from uploaded files list
    setListFilesUpload((prev) =>
      prev.filter((file) => file.fileName !== fileName)
    );
    // Delay the upload to ensure state is updated
    // Filter files larger than 30MB
    const filesToUpload = remainingFiles.filter(
      (file) => file.size <= MAX_FILE_SIZE_BYTES
    );
    if (filesToUpload.length > 0) {
      setTimeout(() => {
        const formData = new FormData();
        filesToUpload.forEach((file) => {
          formData.append("files", file);
        });
        handleUpload(formData);
      }, 0);
    }
  };

  const closeLimitExceededPopup = () => {
    setOpen(false);
  };

  const closeDuplicateFilesPopup = () => {
    setDuplicateFile(false);
  };

  const closeUploadSuccess = () => {
    setUploadSuccess(false);
  };

  const removeListUpload = () => {
    setFiles({});
    setFileLength(0);
    setFileProgress({});
    setShowUploadFile(false);
    setShouldDisableUploadAll(true);
    setErrorFiles([]);
    setListFilesUpload([]);

    // Reset file input value so selecting the same file again will trigger onChange
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleFileInput = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || []);
    if (selectedFiles.length === 0) return;
    const groupedFromInput = convertToGroupedObject(selectedFiles);

    // Merge with existing files
    const mergedFiles: GroupedResult = { ...files };

    // Calculate total existing files
    let totalFiles = Object.values(mergedFiles).reduce(
      (sum, group) => sum + group.item.length,
      0
    );

    // Filter out duplicates and count new files
    let newFileCount = 0;
    const newFilesToUpload: File[] = [];
    let hasDuplicate = false;
    Object.entries(groupedFromInput).forEach(([key, value]) => {
      if (!mergedFiles[key]) {
        mergedFiles[key] = { item: [] };
      }

      const existingNames = new Set(mergedFiles[key].item.map((f) => f.name));
      const newItems = value.item.filter((f) => {
        if (existingNames.has(f.name)) {
          hasDuplicate = true;
          return false;
        }
        return true;
      });

      newFileCount += newItems.length;
      newFilesToUpload.push(...newItems);
    });

    // Show warning if duplicate file names are found
    if (hasDuplicate) {
      setDuplicateFile(true); // Show warning popup
      return;
    }

    // Check file limit
    if (totalFiles + newFileCount > 10) {
      setOpen(true); // Show warning popup
      return;
    }

    Object.entries(groupedFromInput).forEach(([key, value]) => {
      if (!mergedFiles[key]) {
        mergedFiles[key] = { item: [] };
      }

      // Avoid duplicates
      const existingNames = new Set(mergedFiles[key].item.map((f) => f.name));
      const newItems = value.item.filter((f) => !existingNames.has(f.name));
      newItems.forEach((file) => simulateProgress(file));
      mergedFiles[key].item.push(...newItems);
    });

    setFiles(mergedFiles);
    const allFilesToUpload: File[] = [];

    Object.values(mergedFiles).forEach((group) => {
      allFilesToUpload.push(...group.item);
    });

    // Filter files larger than 30MB
    const filesToUpload = allFilesToUpload.filter(
      (file) => file.size <= MAX_FILE_SIZE_BYTES
    );
    if (filesToUpload.length > 0) {
      const formData = new FormData();
      filesToUpload.forEach((file) => {
        formData.append("files", file);
      });

      handleUpload(formData);
    }
  };

  const getErrorMap = (errorFiles: ErrorResponse[]) => {
    const map = new Map();
    errorFiles?.forEach((err) => {
      const fileName = err.params[0];
      map.set(fileName, err);
    });
    return map;
  };

  const checkFileStatus = (
    sheetData: SheetData,
    errorFiles: ErrorResponse[]
  ) => {
    const errorMap = getErrorMap(errorFiles);

    // Check for any error in completed files (BE)
    const hasErrorFiles = sheetData.item.some((file) => {
      const error = errorMap.get(file.name);
      return !!error;
    });

    // Check for any error in completed files (FE)
    const hasError = sheetData.item.some((file) => {
      const isNotAcceptedFileType = ![
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ].includes(file.type);

      const isTooLarge = file.size > 30 * 1024 * 1024;
      return isNotAcceptedFileType || isTooLarge;
    });
    return {
      hasErrorFiles,
      hasError,
    };
  };

  return (
    <Grid
      container
      spacing={4}
      sx={{ minWidth: "500px", display: "flex", flexWrap: "wrap" }}
    >
      {/* Upload Area */}
      <Grid
        size={{ xs: 12, md: 8.3 }}
        sx={{
          display: "flex",
          gap: "24.5px",
          flexDirection: "column",
        }}
      >
        <Box
          sx={{
            width: "780px",
            padding: 2,
            backgroundColor: "#FFFFFF",
            borderRadius: 2,
            boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.08)",
          }}
        >
          {/* Upload Area */}
          <UploadArea
            type="erp-data"
            handleDragOver={handleDragOver}
            handleDragLeave={handleDragLeave}
            dragOver={dragOver}
            handleDrop={handleDrop}
            handleFileInput={handleFileInput}
            fileInputRef={fileInputRef}
          />
        </Box>
        {/* Upload Files */}

        {/*  handle for case show upload file name mapping to group name if have 2 file same name */}
        {fileLength > 0 && (
          <>
            <Box
              sx={{
                width: "777px",
                padding: 2,
                backgroundColor: "#FFFFFF",
                borderRadius: 2,
                boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.08)",
                margin: "0 auto 10px",
              }}
            >
              {showUploadFile && (
                <CustomListRow>
                  <Typography variant="h6">
                    Files to Upload ({fileLength}/10)
                  </Typography>
                  <Button
                    sx={{
                      color: "#000000",
                    }}
                    onClick={removeListUpload}
                  >
                    <RecycleIcon></RecycleIcon>
                    Remove All
                  </Button>
                </CustomListRow>
              )}
              <Stack spacing={2}>
                {Object.entries(files as FilesMap).map(
                  ([sheetName, sheetData], index) => {
                    const { hasErrorFiles, hasError } = checkFileStatus(
                      sheetData,
                      errorFiles
                    );
                    return (
                      <PaperUploadErp
                        key={sheetName}
                        index={index}
                        sheetName={sheetName}
                        sheetData={sheetData}
                        hasErrorFiles={hasErrorFiles}
                        hasError={hasError}
                        errorFiles={errorFiles}
                        fileProgress={fileProgress}
                        getErrorMap={getErrorMap}
                        handleRemoveFile={handleRemoveFile}
                      />
                    );
                  }
                )}
              </Stack>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "right",
                  mt: 4,
                  gap: 2,
                }}
              >
                {isUploading && (
                  <Button
                    sx={{
                      display: "flex",
                      gap: "10px",
                    }}
                    variant="contained"
                    color="primary"
                  >
                    {/* handle show icon upload */}
                    <CircularProgress
                      size={20}
                      sx={{
                        color: "white",
                      }}
                    />
                    <Typography
                      sx={{
                        fontFamily: "Roboto, sans-serif",
                        fontWeight: 600,
                        fontStyle: "normal", // 'SemiBold' is not valid in CSS
                        fontSize: "14px",
                        lineHeight: "26px",
                        letterSpacing: 0,
                        verticalAlign: "middle",
                      }}
                    >
                      Upload All
                    </Typography>
                  </Button>
                )}
                {!isUploading && (
                  <Button
                    sx={{
                      display: "flex",
                      gap: "10px",
                      backgroundColor: shouldDisableUploadAll
                        ? "#FFFFFF !important"
                        : undefined,
                      border: shouldDisableUploadAll
                        ? "1px solid #ECEFF4 !important"
                        : undefined,
                    }}
                    variant="contained"
                    color={shouldDisableUploadAll ? "inherit" : "primary"}
                    disabled={shouldDisableUploadAll}
                    onClick={handleUploadAll}
                  >
                    {shouldDisableUploadAll ? (
                      <UploadDisableIcon />
                    ) : (
                      <UploadAllIcon />
                    )}
                    <Typography
                      sx={{
                        fontFamily: "Roboto, sans-serif",
                        fontWeight: 600,
                        fontStyle: "normal", // 'SemiBold' is not valid in CSS
                        fontSize: "14px",
                        lineHeight: "26px",
                        letterSpacing: 0,
                        verticalAlign: "middle",
                      }}
                    >
                      Upload All
                    </Typography>
                  </Button>
                )}
              </Box>
            </Box>
          </>
        )}
      </Grid>
      {/* Upload Requirements */}
      <WithUploadRequirements type="erp-upload" />

      <PopupModal
        open={open}
        title="You can upload up to 10 files at a time."
        description="Please reduce the number of files and try again."
        type="error"
        isOKOnly
        hideCloseButton
        disableBackdropClick
        onSubmit={closeLimitExceededPopup}
      />
      <PopupModal
        open={duplicateFile}
        title="The same file is currently being uploaded."
        description="Please check again."
        type="error"
        isOKOnly
        hideCloseButton
        disableBackdropClick
        onSubmit={closeDuplicateFilesPopup}
      />
      {/* Snackbar success */}
      <SnackbarUpload
        fileLengthSuccess={fileLengthSuccess}
        uploadSuccess={uploadSuccess}
        closeUploadSuccess={closeUploadSuccess}
      />
    </Grid>
  );
};

export default ERPDataUpload;
