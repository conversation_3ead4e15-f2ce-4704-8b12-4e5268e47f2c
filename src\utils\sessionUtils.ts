/**
 * Ki<PERSON><PERSON> tra trạng thái hết hạn mật khẩu dựa vào userData.
 * @param userData object chứa daysUntilPasswordExpiry (số ngày, int) và passwordExpired (bool)
 * @returns { passwordWillExpireSoon: boolean, forceChangePasswordExpired: boolean, passwordExpiryDate?: string }
 */
export function getPasswordExpiryStatus(userData: { daysUntilPasswordExpiry?: number; passwordExpired?: boolean; lastPasswordChange?: string }): {
  passwordWillExpireSoon: boolean;
  forceChangePasswordExpired: boolean;
  passwordExpiryDate?: string;
} {
  const days = userData?.daysUntilPasswordExpiry;
  const expired = userData?.passwordExpired;
  let passwordExpiryDate: string | undefined = undefined;
  let passwordWillExpireSoon = false;
  let forceChangePasswordExpired = false;

  if (expired) {
    forceChangePasswordExpired = true;
  } else if (typeof days === 'number' && days > 0) {
    passwordWillExpireSoon = true;
    // Tính ngày hết hạn dựa vào ngày hiện tại
    const now = new Date();
    const expiry = new Date(now.getFullYear(), now.getMonth(), now.getDate() + days);
    // Định dạng dd/MM/yyyy
    const dd = String(expiry.getDate()).padStart(2, '0');
    const mm = String(expiry.getMonth() + 1).padStart(2, '0');
    const yyyy = expiry.getFullYear();
    passwordExpiryDate = `${dd}/${mm}/${yyyy}`;
  }

  return { passwordWillExpireSoon, forceChangePasswordExpired, passwordExpiryDate };
}
/**
 * Kiểm tra session hết hạn dựa vào userData và lastLogin.
 * @param userData object chứa logoutTime (phút, int)
 * @param lastLogin string dạng "dd/MM/yyyy HH:mm:ss"
 * @returns true nếu session đã hết hạn, false nếu còn hạn hoặc thiếu dữ liệu
 */
export function isSessionExpired(userData: { logoutTime?: number }, lastLogin?: string): boolean {
  const logoutTime = userData?.logoutTime || 0;
  if (!lastLogin || !logoutTime) return false;
  try {
    const [date, time] = lastLogin.split(" ");
    const [day, month, year] = date.split("/").map(Number);
    const [hour, minute, second] = time.split(":").map(Number);
    const lastLoginDate = new Date(year, month - 1, day, hour, minute, second);
    const expireDate = new Date(lastLoginDate.getTime() + logoutTime * 60 * 1000);
    return Date.now() > expireDate.getTime();
  } catch {
    return false;
  }
}