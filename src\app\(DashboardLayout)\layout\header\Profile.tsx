import React, { useRef, useState } from "react";
import Link from "next/link";
import {
  Avatar,
  Box,
  Menu,
  Button,
  IconButton,
  MenuItem,
  ListItemIcon,
  ListItemText,
} from "@mui/material";

import { useSelector } from "react-redux";
import { selectUserProfile } from "@/features/users/state/usersSelectors";
import type { User } from "@/types/auth";
import { useMsal } from "@azure/msal-react";
import { signOutMsal } from "@/services/msalService";

import Image from "next/image";
import { useRouter } from "next/navigation";

const Profile = () => {
  const router = useRouter();
  const [anchorEl2, setAnchorEl2] = useState(null);
  const { instance, accounts } = useMsal();
  const profileRef = useRef<HTMLDivElement>(null);
  const [profileWidth, setProfileWidth] = useState<number>();
  const user: User | null = useSelector(selectUserProfile);

  const handleClick2 = (event: any) => {
    setAnchorEl2(event.currentTarget);
    if (profileRef.current) {
      setProfileWidth(profileRef.current.offsetWidth);
    }
  };
  const handleClose2 = () => {
    setAnchorEl2(null);
  };

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "flex-end",
        minHeight: "50px",
      }}
    >
      <Box
        onClick={handleClick2}
        ref={profileRef}
        sx={{
          display: "flex",
          alignItems: "center",
          bgcolor: "#ECEFF4",
          borderRadius: 1,
          padding: "4px",
          cursor: "pointer",
          minWidth: 184,
          position: "relative",
          border: "1px solid rgba(162, 161, 168, 0.2)",
        }}
      >
        <Avatar
          src={user && user.imageUrl ? user.imageUrl : "/images/profile/your-profile.svg"}
          alt="image"
          sx={{
            width: 40,
            // height: 32,
            border: "2px solid #fff",
            mr: 2,
          }}
        />
        <Box sx={{ flex: 1 }}>
          <Box
            sx={{
              fontWeight: 700,
              fontSize: 16,
              color: "#222222",
              fontFamily: "Roboto, sans-serif",
              lineHeight: 1,
            }}
          >
            {user ? ` ${user.lastName || ""} ${user.firstName || ""}`.trim() : ""}
          </Box>
          <Box
            sx={{
              fontWeight: 400,
              fontSize: 14,
              color: "#44565B",
              fontFamily: "Roboto, sans-serif",
              lineHeight: 1.5,
            }}
          >
            {user ? `${user.roleName || ""}`.trim() : ""}
          </Box>
        </Box>
        <Box sx={{ ml: 2, display: "flex", alignItems: "center" }}>
          {Boolean(anchorEl2) ? (
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              {/* Up arrow */}
              <path
                d="M5 12L10 7L15 12"
                stroke="#44565B"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ) : (
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              {/* Down arrow */}
              <path
                d="M5 8L10 13L15 8"
                stroke="#44565B"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          )}
        </Box>
      </Box>
      <Menu
        // disableAutoFocus
        id="msgs-menu"
        anchorEl={anchorEl2}
        keepMounted
        open={Boolean(anchorEl2)}
        onClose={handleClose2}
        anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
        transformOrigin={{ horizontal: "right", vertical: "top" }}
        sx={{
          "& .MuiMenu-paper": {
            width: profileWidth,
            bgcolor: "#ECEFF4",
            borderRadius: "10px",
            marginTop: "4px",
            boxShadow: "0 4px 24px rgba(0,0,0,0.10)",
            p: 0,
          },
          ".MuiMenuItem-gutters": {
            "&:hover": {
              backgroundColor: "rgba(225, 228, 234, 1)",
            },
          },
        }}
      >
        <MenuItem
          sx={{
            height: 39,
            px: 2.5,
            py: 0,
            fontFamily: "Roboto, sans-serif",
            fontSize: 16,
            color: "#000",
            gap: 2,
          }}
          onClick={() => router.push("/your-profile")
          }
        >
          <ListItemIcon  sx={{ minWidth: 0 }}>
            <Image
              src="/images/profile/your-profile.svg"
              alt="your profile"
              height={24}
              width={24}
            />
          </ListItemIcon>
          Your profile
        </MenuItem>
        <Box sx={{ height: 0, mx: "auto", width: "90%" }}>
          <hr
            style={{
              border: "none",
              borderTop: "1px solid #e0e0e0",
              margin: 0,
            }}
          />
        </Box>
        <Box sx={{ height: 0, mx: "auto", width: "90%" }}>
          <hr
            style={{
              border: "none",
              borderTop: "1px solid #e0e0e0",
              margin: 0,
            }}
          />
        </Box>
        <MenuItem
          sx={{
            height: 39,
            px: 3,
            py: 0,
            fontFamily: "Roboto, sans-serif",
            fontSize: 16,
            color: "#000",
            gap: 2,
          }}
          onClick={() => signOutMsal(instance, accounts[0])}
        >
          <ListItemIcon sx={{ minWidth: 0 }}>
            <Image
              src="/images/profile/logout.svg"
              alt="your profile"
              height={24}
              width={24}
            />
          </ListItemIcon>
          Sign out
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default Profile;
