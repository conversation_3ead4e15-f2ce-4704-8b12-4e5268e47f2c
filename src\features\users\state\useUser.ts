// src/features/auth/hooks/useAuth.ts
import { useDispatch, useSelector } from 'react-redux';

import {
  selectUserProfile,
} from '@/features/users/state/usersSelectors';
import { AppDispatch } from '@/store/store';
import { LoginRequest, RegisterRequest, User } from '@/types/auth';

export function useUser() {
  // const dispatch = useDispatch<AppDispatch>();
  const user = useSelector(selectUserProfile);

  return {
    user,
  };
}
