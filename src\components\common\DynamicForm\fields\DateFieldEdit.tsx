import React from "react";
import { Tooltip } from "@mui/material";
import { Controller } from "react-hook-form";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { FieldConfig } from "../DynamicForm.types";
import Image from "next/image";
import dayjs from "dayjs";
import MuiErrorCustomStyles from "@/utils/theme/muiErrorStyles";
import { InfoOutlined } from "@mui/icons-material";

interface Props {
  field: FieldConfig;
  control: any;
  error?: string;
  disabled?: boolean;
  i18n?: (key: string) => string;
}

const CalendarIcon = () => (
  <Image
    src="/images/icons/calendar.svg"
    alt="calendar"
    width={18}
    height={18}
    style={{ marginRight: 2 }}
  />
);
const DateFieldEdit: React.FC<Props> = ({
  field,
  control,
  error,
  disabled,
  i18n,
}) => {
  const labelText = i18n ? i18n(field.label) : field.label;
  const requiredAsterisk =
    field.required && !field.disabled ? (
      <span style={{ color: "#CA002E", marginLeft: 4 }}>*</span>
    ) : null;
  const labelWithTooltip = (
    <span style={{ display: "inline-flex", alignItems: "center", gap: 6 }}>
      <span>
        {labelText}
        {requiredAsterisk}
      </span>
      {field.labelTooltipContent ? (
        <Tooltip
          title={
            typeof field.labelTooltipContent === "string"
              ? i18n
                ? i18n(field.labelTooltipContent)
                : field.labelTooltipContent
              : field.labelTooltipContent
          }
          {...field.labelTooltipProps}
        >
          <span
            style={{ display: "inline-flex", color: "rgba(0,0,0,0.54)" }}
            onMouseDown={(e) => e.preventDefault()}
          >
            {field.labelTooltipIcon ?? <InfoOutlined fontSize="small" />}
          </span>
        </Tooltip>
      ) : null}
    </span>
  );
  return (
    <Controller
      name={field.name}
      control={control}
      render={({ field: controllerField }) => (
        <DatePicker
          label={labelWithTooltip}
          value={controllerField.value ? dayjs(controllerField.value) : null}
          onChange={(date) => controllerField.onChange(date)}
          disabled={disabled || field.disabled}
          format="DD/MM/YYYY"
          disableFuture={field.disableFuture}
          slots={{ openPickerIcon: CalendarIcon }}
          slotProps={{
            textField: {
              error: !!error,
              helperText: error || field.helperText,
              fullWidth: true,
              sx: {
                ...field.style,
                ...MuiErrorCustomStyles,
                "& .Mui-focused:not(.Mui-error) .MuiPickersOutlinedInput-notchedOutline":
                  {
                    borderColor: "#222222",
                  },
                "& .MuiOutlinedInput-root, & .MuiPickersOutlinedInput-root, & .MuiPickersInputBase-root":
                  {
                    borderRadius: "8px",
                  },

                "& .Mui-error .MuiPickersOutlinedInput-notchedOutline": {
                  borderColor: error ? "#CA002E" : undefined,
                  borderWidth: error ? "1px" : undefined,
                },

                "& .MuiFormControl-root-MuiPickersTextField-root .MuiFormLabel-root.Mui-error":
                  {
                    color: "#CA002E",
                  },
              },
              className: field.className,
              InputLabelProps: {
                sx: {
                  "&.MuiInputLabel-shrink": {
                    fontSize: 18,
                    backgroundColor: "rgba(255, 255, 255, 1)",
                  },
                },
                shrink: true,
              },
              inputProps: {
                "aria-label": field.label,
              },
            },
          }}
        />
      )}
    />
  );
};

export default DateFieldEdit;
