"use client";
import { useMsal, UnauthenticatedTemplate } from "@azure/msal-react";
import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import Button from "@mui/material/Button";
import AuthLayout from "@/components/common/AuthLayout";
import PageContainer from "@/app/(DashboardLayout)/components/container/PageContainer";

// Define MSAL scopes as a constant outside the component
const SCOPES = [
  "openid",
  "0b135d95-2488-4110-8042-aef3b76766ed/API.Write",
  "0b135d95-2488-4110-8042-aef3b76766ed/API.Read",
];


const LoginPage: React.FC = () => {
  const { instance, accounts } = useMsal();
  const router = useRouter();
  const [lastLogin, setLastLogin] = useState<string | null>(null);

  // Memoized pad function for date formatting
  const pad = useCallback((n: number) => n.toString().padStart(2, "0"), []);

  useEffect(() => {
    if (accounts.length !== 0) {
      const now = new Date();
      const formatted = `${pad(now.getDate())}/${pad(
        now.getMonth() + 1
      )}/${now.getFullYear()} ${pad(now.getHours())}:${pad(
        now.getMinutes()
      )}:${pad(now.getSeconds())}`;
      localStorage.setItem("lastLogin", formatted);
      setLastLogin(formatted);
      router.replace("/");
    } else {
      // Get lastLogin from localStorage if available
      if (typeof window !== "undefined") {
        setLastLogin(localStorage.getItem("lastLogin"));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [accounts, router, pad]);

  return (
    <PageContainer title="Login" description="Login to your company account">
      <UnauthenticatedTemplate>
        <AuthLayout lastLogin={lastLogin || undefined}>
          <Button
            variant="contained"
            color="primary"
            sx={{
              mt: 3,
              px: 4,
              py: 1.5,
              fontWeight: 600,
              fontSize: 16,
              borderRadius: "10px",
              boxShadow: 2,
            }}
            onClick={() => instance.loginPopup({ scopes: SCOPES,prompt: "login",  })}
          >
            Sign in with your company account
          </Button>
        </AuthLayout>
      </UnauthenticatedTemplate>
    </PageContainer>
  );
};

export default LoginPage;
