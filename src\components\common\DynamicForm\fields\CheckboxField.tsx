import React from 'react';
import { Box } from '@mui/material';
import { Controller } from 'react-hook-form';
import MuiCheckbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import { FieldConfig } from '../DynamicForm.types';

interface Props {
  field: FieldConfig;
  control: any;
  error?: string;
  disabled?: boolean;
  i18n?: (key: string) => string;
}

const CheckboxField: React.FC<Props> = ({ field, control, error, disabled, i18n }) => (
  <Controller
    name={field.name}
    control={control}
    render={({ field: controllerField }) => (
        field.hiddenLabelNormal ? (
          <Box sx={{ position: 'relative', width: '100%' }}>
            <Box
              sx={{
                position: 'absolute',
                left: 0,
                right: 0,
                top: 2,
                color: '#222222',
                fontFamily: 'Roboto',
                fontWeight: 400,
                fontSize: 14,
                lineHeight: 1.5,
                zIndex: 2,
              }}
            >
              {i18n ? i18n(field.label) : field.label}
            </Box>
            <FormControlLabel
              control={<MuiCheckbox {...controllerField} disabled={disabled || field.disabled} />}
              label=""
              sx={{ ...field.style, mt: 4 }}
              className={field.className}
            />
          </Box>
        ) : (
          <FormControlLabel
            control={<MuiCheckbox {...controllerField} disabled={disabled || field.disabled} />}
            label={i18n ? i18n(field.label) : field.label}
            sx={field.style}
            className={field.className}
          />
        )
    )}
  />
);

export default CheckboxField;
