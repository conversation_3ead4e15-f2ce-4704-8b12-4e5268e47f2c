export const MILLSHEET_STATUS = {
  NO_ERP: 2,
  ERROR: 3,
  IMPORTED: 4,
};

export type MillSheetStatusType = typeof MILLSHEET_STATUS[keyof typeof MILLSHEET_STATUS];

export interface MillSheet {
  id: number;
  mill: string;
  customer: string;
  invoiceNo: string;
  poNo: string;
  serialNumber: string;
  standard: string;
  heatNo: string;
  size: string;
  weight: string;
  dateOfIssue: string;
  pdfDownloadFlag: 0 | 1;
  pdfContainerPath: string;
  metadataContainerPath: string;
  metadataDownloadFlag: 0 | 1;
  isConfirmed: 0 | 1;
  status: number;
}

export interface MillSheetEdit {
  id: number;
  mill: string;
  customer: string;
  invoiceNo: string;
  poNo: string;
  serialNumber: string;
  standard: string;
  heatNo: string;
  size: string;
  weight: string;
  dateOfIssue: string;
  status: number;
  lastEdited: string;
}

export interface MillSheetResponse {
  items: MillSheet[] | MillSheetEdit[];
  currentPage: number;
  pageSize: number;
  totalItem: number;
  totalPage: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export interface Sort {
  sortColumn?: string;
  sortDirection?: "asc" | "desc";
}

export interface Pagination {
  pageNumber?: number;
  pageSize?: number;
}

export interface ConditionSearch {
  mill?: string;
  customerId?: string;
  invoiceNo?: string;
  poNo?: string;
  serialNumber?: string;
  standard?: string;
  heatNo?: string;
  size?: string;
  fromWeight?: number;
  toWeight?: number;
  fromIssueDate?: string;
  toIssueDate?: string;
  isConfirmed?: 0 | 1;
  viewMode?: number;
  status?: MillSheetStatusType;
  fileId?: string | string[];
  millSheetId?: string | string[];
}

export interface MillSheetSearchParams {
  condition?: ConditionSearch;
  sort?: Sort[];
  pagination?: Pagination;
}

export enum FlagStatus {
  On = 1,
  Off = 0,
}

export interface Customer {
  id: number;
  customerId: string;
  customerName: string;
}

export interface Supplier {
  id: number;
  supplierId: string;
  supplierName: string;
}

export interface MillSheetDownloadParams {
  millSheetId: number;
  pathFile: string;
}

export interface InfoFile {
  file: Blob;
  fileName: string;
}

export interface MillSheetBulkDownloadParams {
  millSheetId: number;
  pathFiles: string[];
}

export const MappingColumnSort = {
  mill: "supplier_name",
  customer: "customer_name",
  invoiceNo: "invoice_no",
  poNo: "invoice_no",
  serialNumber: "serial_no",
  standard: "standard",
  heatNo: "heat_no",
  size: "size",
  weight: "weight",
  dateOfIssue: "order_date",
  isConfirmed: "is_confirmed",
  status: "status",
  editedAt: "edited_at"
};

export enum ConfirmStatus {
  Unconfirm = 0,
  Confirmed = 1,
}

export interface PreviewPDF {
  fileUrl: string;
  confirmAt: string;
  firstName: string;
  lastName: string;
  isConfirmed: ConfirmStatus.Unconfirm | ConfirmStatus.Confirmed;
}

export interface MillSheetPreviewFileParams {
  id: number;
  fileType: 0 | 1; // 0: pdf, 1: metadata
  status: number; // 2: no ERP, 3: error, 4: imported
}

export interface MillSheetConfirmParams {
  id: number;
  status: number; // 2: no ERP, 3: error, 4: imported
}

export interface MillSheetRecord {
  id: number | string;
  status: number;
  serialNumber: string;
  mill: string;
  customer: string;
  invoiceNo?: string;
  poNo?: string;
  heatNo?: string;
  standard: string;
  size: string;
  weight: string;
  dateOfIssue: string;
}

export interface MillSheetResponseEdit {
  millSheetId: number; 
}


