export interface MillSheet {
  id: number;
  mill: string;
  customer: string;
  invoiceNo: string;
  poNo: string;
  serialNumber: string;
  standard: string;
  heatNo: string;
  size: string;
  weight: string;
  dateOfIssue: string;
  pdfDownloadFlag: 0 | 1;
  pdfContainerPath: string;
  metadataContainerPath: string;
  metadataDownloadFlag: 0 | 1;
  isConfirmed: 0 | 1;
}

export interface MillSheetResponse {
  items: MillSheet[];
  currentPage: number;
  pageSize: number;
  totalItem: number;
  totalPage: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export interface Sort {
  sortColumn?: string;
  sortDirection?: "asc" | "desc";
}

export interface Pagination {
  pageNumber?: number;
  pageSize?: number;
}

export interface ConditionSearch {
  mill?: string;
  customerId?: string;
  invoiceNo?: string;
  poNo?: string;
  serialNumber?: string;
  standard?: string;
  heatNo?: string;
  size?: string;
  fromWeight?: number;
  toWeight?: number;
  fromIssueDate?: string;
  toIssueDate?: string;
  isConfirmed?: 0 | 1;
  millSheetSearchFlag?: number;
}

export interface MillSheetSearchParams {
  condition?: ConditionSearch;
  sort?: Sort[];
  pagination?: Pagination;
}

export enum FlagStatus {
  On = 1,
  Off = 0,
}

export interface Customer {
  id: number;
  customerId: string;
  customerName: string;
}

export interface Supplier {
  id: number;
  supplierId: string;
  supplierName: string;
}

export interface MillSheetDownloadParams {
  millSheetId: number;
  pathFile: string;
}

export interface MillSheetBulkDownloadParams {
  millSheetId: number;
  pathFileMetadata: string;
  pathFilePdf: string;
}

export interface PreviewPDF {
  fileUrl: string;
}