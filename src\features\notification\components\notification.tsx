import { convertToLocalTimezone } from "@/utils/dateUtils";
import { parseDateString } from "@/utils/dateUtils";
import {
  Avatar,
  Badge,
  Box,
  ButtonBase,
  Chip,
  Divider,
  IconButton,
  Menu,
  Typography,
  Skeleton,
} from "@mui/material";
import Image from "next/image";
import React, { useState, useCallback, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { useAppDispatch } from "@/hooks/useStore";
import { setUnreadCount } from "../state/notificationSlice";
import {
  countUnreadNotifications,
  searchNotifications,
  updateNotificationReadStatus,
  markAllNotificationsRead,
} from "../state/notificationApi";
import { useAppSelector } from "@/hooks/useStore";
import { selectUnreadCount } from "../state/notificationSelector";
import * as styles from "./notification.styles";
import { useUser } from "@/features/users/state/useUser";
import * as signalR from "@microsoft/signalr";
import { formatDistanceToNow } from "date-fns";
// Add global type for window.signalR
declare global {
  interface Window {
    signalR: typeof signalR;
  }
}
import type { Notification, NotificationProps } from "@/types/notification";
import { toCamelCase } from "@/utils/common";

// Factory to create sample notifications (keeps ids stable per module load)
// Generate a larger set so pagination (PAGE_SIZE = 20) can load ~3 times
// const createInitialNotifications = (): Notification[] => {
//   const TOTAL = 20 * 3; // 3 pages of 20
//   return Array.from({ length: TOTAL }).map((_, i) => ({
//     id: uniqueId("notif_"),
//     title: `Notification ${i + 1}`,
//     category: i % 2 === 0 ? "User List" : "Millsheet",
//     body: "This is a sample notification body for preview and testing.",
//     // spread createdAt times so minutesAgo varies
//     createdAt: new Date(Date.now() - (i + 1) * 60 * 1000),
//     // make about a third unread to simulate realistic data
//     read: i % 3 !== 0,
//   }));
// };

const Notification: React.FC<NotificationProps> = ({
  externalUnreadCount,
  onUnreadChange,
}) => {
  // ====== Hooks: refs, state, selectors ======
  const router = useRouter();
  const { user } = useUser();
  const dispatch = useAppDispatch();
  const connectionRef = useRef<signalR.HubConnection | null>(null); // SignalR connection instance
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null); // Notification menu anchor
  const [items, setItems] = useState<Notification[]>([]); // Notification list
  const [initialLoaded, setInitialLoaded] = useState(false); // Initial load state
  const initialLoadedRef = useRef(false); // Ref for initial load
  const fetchTimerRef = useRef<number | null>(null); // Ref for fetch timer
  const loadMoreTimerRef = useRef<number | null>(null); // Ref for load more timer
  const unreadCount = useAppSelector(selectUnreadCount); // Unread notification count from store

  // ====== Derived values ======
  const open = Boolean(anchorEl); // Is notification menu open
  const PAGE_SIZE = 20; // Page size for pagination
  const [currentPage, setCurrentPage] = useState(1); // Current page
  const [hasNextPage, setHasNextPage] = useState(true); // Is there next page
  const [loading, setLoading] = useState(false); // Loading state
  const itemsDisplay = items;
  const hasMore = hasNextPage; // Alias for hasNextPage

  // ====== SignalR connection and event registration ======
  /**
   * Connect to SignalR and register notification event
   */
  const connectToSignalR = useCallback(async () => {
    try {
      const signalRBaseUrl = process.env.NEXT_PUBLIC_SIGNALR_URL;
      const hubName = process.env.NEXT_PUBLIC_SIGNALR_HUB_NOTIFICATION;
      const signalRUrl = `${signalRBaseUrl}?hub=${hubName}`;
      const connection = new signalR.HubConnectionBuilder()
        .withUrl(signalRUrl, {
          accessTokenFactory: () => user?.notification?.token || "",
        })
        .withAutomaticReconnect()
        .build();
      // On reconnect, refresh unread count
      connection.onreconnected(async () => {
        try {
          const res: any = await countUnreadNotifications();
          dispatch(setUnreadCount(res.data?.data || 0));
        } catch {}
      });
      await connection.start();
      connectionRef.current = connection;

      // Register notification event after connection is started
      connection.on("ReceiveNotification", async (notification: any) => {
        const notificationPayload = JSON.parse(notification);
        const body = notificationPayload.body || {};
        const payload = body.payload || {};
        const notificationItem: Notification = {
          id:
            body.id ||
            notificationPayload.notificationEventId ||
            notificationPayload.id,
          icon: payload.icon || "",
          title: payload.title || "",
          content: payload.content || "",
          tag: payload.tag || "",
          link: payload.link || "",
          createdAt: body.createdAt
            ? convertToLocalTimezone(parseDateString(body.createdAt))
            : convertToLocalTimezone(new Date()),
          isRead: !!body.isRead,
        };
        setItems((prev) => [notificationItem, ...prev]);
        try {
          const res: any = await countUnreadNotifications();
          dispatch(setUnreadCount(res.data?.data || 0));
        } catch {}
      });

      // On first connect, get unread count
      try {
        const res: any = await countUnreadNotifications();
        dispatch(setUnreadCount(res.data?.data || 0));
      } catch {}
    } catch (error) {
      console.log(error);
    }
  }, [dispatch, user]);
  const fetchNotifications = useCallback(
    async (page = 1) => {
      setLoading(true);
      try {
        const res = await searchNotifications({
          pagination: { pageSize: PAGE_SIZE, pageNumber: page },
          sort: [{ field: "createdAt", order: "desc" }],
        });
        const data = res.data?.data?.items || [];
        const total = res.data?.data?.totalItem || 0;
        setItems((prev) => {
          if (page === 1) {
            return data.map((n: any) => ({
              ...n,
              ...n.payload,
              createdAt: n.createdAt
                ? parseDateString(n.createdAt)
                : new Date(),
            }));
          } else {
            // Avoid duplicates when fetching more
            const existingIds = new Set(prev.map((item: any) => item.id));
            const newItems = data
              .map((n: any) => ({
                ...n,
                ...n.payload,
                createdAt: n.createdAt
                  ? parseDateString(n.createdAt)
                  : new Date(),
              }))
              .filter((item: any) => !existingIds.has(item.id));
            return [...prev, ...newItems];
          }
        });
        setCurrentPage(page);
        setHasNextPage(page * PAGE_SIZE < total);
      } catch (error) {
        if (page === 1) setItems([]);
      } finally {
        setLoading(false);
        if (page === 1) {
          initialLoadedRef.current = true;
          setInitialLoaded(true);
        }
      }
    },
    [PAGE_SIZE]
  );

  // ====== Effects ======
  // Connect to SignalR when user/token is available
  useEffect(() => {
    if (user && user.notification?.token) {
      connectToSignalR();
      return () => {
        if (connectionRef.current) {
          connectionRef.current.stop();
        }
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  // Fetch notifications when menu is opened
  useEffect(() => {
    if (open && !initialLoadedRef.current) {
      setItems([]);
      setCurrentPage(1);
      setHasNextPage(true);
      fetchNotifications(1);
    }
  }, [open, fetchNotifications]);

  // Cleanup timers on unmount
  useEffect(() => {
    const fetchTimer = fetchTimerRef.current;
    const loadMoreTimer = loadMoreTimerRef.current;
    return () => {
      if (fetchTimer) {
        clearTimeout(fetchTimer as unknown as number);
      }
      if (loadMoreTimer) {
        clearTimeout(loadMoreTimer as unknown as number);
      }
    };
  }, []);

  // ====== Memoized components ======
  // Skeleton rows for loading state
  const SkeletonRows = React.useMemo(() => {
    const Row: React.FC<{ count?: number; rowHeight?: number }> = ({
      count = 1,
      rowHeight = 64,
    }) => (
      <>
        {Array.from({ length: count }).map((_, i) => (
          <Box
            key={i}
            sx={{
              display: "flex",
              gap: 1.5,
              py: 1.25,
              minHeight: rowHeight,
              alignItems: "center",
              opacity: 0,
              animation: "fadeIn 220ms ease forwards",
              "@keyframes fadeIn": {
                to: { opacity: 1 },
              },
            }}
          >
            <Skeleton variant="circular" width={32} height={32} />
            <Box sx={{ flex: 1 }}>
              <Skeleton width="60%" height={16} />
              <Skeleton width="80%" height={14} sx={{ mt: 0.5 }} />
              <Skeleton width="30%" height={12} sx={{ mt: 0.5 }} />
            </Box>
          </Box>
        ))}
      </>
    );
    return React.memo(Row);
  }, []);

  // ====== Data fetching and pagination ======
  /**
   * Fetch notifications from API (with pagination)
   */
  /**
   * Fetch next page of notifications (for infinite scroll)
   */
  const fetchMoreNotifications = useCallback(async () => {
    if (!hasNextPage || loading) return;
    await fetchNotifications(currentPage + 1);
  }, [hasNextPage, loading, fetchNotifications, currentPage]);

  // ====== Actions and handlers ======
  /**
   * Mark all notifications as read
   */
  const markAll = useCallback(() => {
    setItems((prev) => {
      const updated = prev.map((n) => ({ ...n, isRead: true }));
      if (onUnreadChange) onUnreadChange(0);
      dispatch(setUnreadCount(0));
      markAllNotificationsRead().catch(() => {});
      return updated;
    });
  }, [onUnreadChange, dispatch]);

  /**
   * Mark a single notification as read
   */
  const markRead = useCallback(
    (id: string) => {
      setItems((prev) => {
        let wasUnread = false;
        const updated = prev.map((n) => {
          if (n.id === id && !n.isRead) {
            wasUnread = true;
            return { ...n, isRead: true };
          }
          return n;
        });
        if (wasUnread) {
          const newUnread = unreadCount > 0 ? unreadCount - 1 : 0;
          dispatch(setUnreadCount(newUnread));
          if (onUnreadChange) onUnreadChange(newUnread);
          updateNotificationReadStatus(Number(id)).catch(() => {});
        }
        return updated;
      });
    },
    [dispatch, unreadCount, onUnreadChange]
  );

  /**
   * Load more notifications (infinite scroll)
   */
  const loadMore = useCallback(() => {
    fetchMoreNotifications();
  }, [fetchMoreNotifications]);

  /**
   * Handle scroll event for infinite loading
   */
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLElement>) => {
      if (!hasMore || loading) return;
      const target = e.target as HTMLElement;
      const { scrollTop, scrollHeight, clientHeight } = target;
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        loadMore();
      }
    },
    [hasMore, loading, loadMore]
  );

  /**
   * Format time ago for notification
   */
  const timeAgo = useCallback((d: Date) => {
    return formatDistanceToNow(d, { addSuffix: true }).replace(/^about /, "");
  }, []);

  /**
   * Get route for notification (based on icon or link)
   */
  const getNotificationRoute = (n: Notification) => {
    if (n.link) {
      return n.link.startsWith("/") ? n.link : "/" + n.link;
    }
    const iconMap: Record<string, string> = {
      millSheetList: "/millsheet/list",
      millSheetUpload: "/millsheet/upload-millsheet",
      millSheetEdit: "/millsheet/modification",
      profile: "/your-profile",
      userList: "/user-master",
    };
    return iconMap[n.icon] || "/";
  };

  /**
   * Handle click on notification (mark as read and navigate)
   */
  const handleNotificationClick = (n: Notification) => {
    markRead(n.id);
    const url = getNotificationRoute(n);
    router.push(url);
  };

  /**
   * Utility: mapping camelCase Icon
   */
  const getNotificationIconPath = (icon: string): string => {
    const iconCamelCase = toCamelCase(icon);
    const availableIcons = [
      "millSheetList",
      "millSheetUpload",
      "millSheetEdit",
      "profile",
      "userList",
    ];

    const fileName = availableIcons.includes(iconCamelCase)
      ? iconCamelCase
      : "list";

    return `/images/notification/${fileName}.svg`;
  };

  return (
    <>
      <IconButton
        sx={styles.iconButton}
        color="inherit"
        aria-controls="msgs-menu"
        aria-haspopup="true"
        onClick={(e) => setAnchorEl(e.currentTarget)}
      >
        <Badge badgeContent={unreadCount} max={99} sx={styles.badge}>
          <Image
            src={
              open
                ? "/images/header/active/notification.svg"
                : "/images/header/notification.svg"
            }
            alt="notification"
            width={24}
            height={24}
          />
        </Badge>
      </IconButton>

      <Menu
        id="notification-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={() => setAnchorEl(null)}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        transformOrigin={{ vertical: "top", horizontal: "right" }}
        slotProps={{ paper: { sx: styles.menuPaper } }}
      >
        {/* Header */}
        <Box sx={{ px: 2, py: 1.75 }}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              mb: 0.5,
            }}
          >
            <Typography
              variant="subtitle1"
              fontWeight={600}
              fontSize={18}
              lineHeight="130%"
            >
              Notification
            </Typography>

            <Box
              onClick={unreadCount ? markAll : undefined}
              sx={{
                display: "inline-flex",
                alignItems: "center",
                gap: 0.5,
                cursor: unreadCount ? "pointer" : "default",
              }}
            >
              <Image
                src="/images/notification/mark.svg"
                alt="mark"
                height={12}
                width={12}
              />
              <Typography variant="overline" color="#2563ebff">
                Mark all as read
              </Typography>
            </Box>
          </Box>

          <Typography variant="caption" color="#44565bff">
            You have {unreadCount} unread notifications
          </Typography>
        </Box>
        <Divider sx={styles.divider} />

        {/* List */}
        {loading && items.length === 0 ? (
          // Reserve vertical space to prevent layout jumps when items arrive
          <Box
            sx={{
              maxHeight: 400,
              overflowY: "auto",
              px: 2,
              py: 1,
              minHeight: Math.min(PAGE_SIZE, 6) * 64,
            }}
          >
            <SkeletonRows count={Math.min(PAGE_SIZE, 6)} />
          </Box>
        ) : items.length > 0 ? (
          <Box
            sx={{ maxHeight: 400, overflowY: "auto" }}
            onScroll={handleScroll}
          >
            {itemsDisplay.map((n) => {
              return (
                <ButtonBase
                  key={n.id}
                  onClick={() => handleNotificationClick(n)}
                  sx={{
                    "&:hover": {
                      backgroundColor: "#dde6f5ff",
                    },
                    width: "100%",
                    textAlign: "left",
                    alignItems: "stretch",
                    display: "block",
                    borderBottom: "1.25px solid #eceff4ff",
                    px: 2,
                    py: 1,
                    position: "relative",
                    bgcolor: n.isRead ? undefined : "rgba(236, 239, 244, 1)",
                  }}
                >
                  <Box sx={{ display: "flex", gap: 1.5 }}>
                    <Avatar
                      variant="circular"
                      sx={{
                        width: 32,
                        height: 32,
                        bgcolor: "rgba(202, 0, 46, 1)",
                        fontSize: 14,
                        fontWeight: 600,
                      }}
                    >
                      <Image
                        src={getNotificationIconPath(n.icon)}
                        alt={n.icon}
                        width={24}
                        height={24}
                      />
                    </Avatar>

                    <Box sx={{ flex: 1, minWidth: 0 }}>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          gap: 1,
                          justifyContent: "space-between",
                          width: "100%",
                        }}
                      >
                        <Typography
                          variant="subtitle2"
                          color="#222222ff"
                          lineHeight="150%"
                          sx={{
                            flexGrow: 1,
                            whiteSpace: "wrap",
                          }}
                        >
                          {n.title}
                        </Typography>

                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            gap: !n.isRead ? 1.5 : 0,
                            flexShrink: 0,
                          }}
                        >
                          <Chip
                            label={n.tag}
                            size="small"
                            sx={{
                              marginRight: "16px",
                              lineHeight: "120%",
                              fontSize: 12,
                              bgcolor: "rgba(220, 252, 231, 1)",
                              color: "rgba(122, 0, 28, 1)",
                            }}
                          />
                          {!n.isRead && (
                            <Box
                              sx={{
                                position: "absolute",
                                right:"12px",
                                width: 10,
                                height: 10,
                                bgcolor: "rgba(59, 130, 246, 1)",
                                borderRadius: "50%",
                                flexShrink: 0,
                                mt: "2px",
                              }}
                            />
                          )}
                        </Box>
                      </Box>

                      <Typography
                        variant="body2"
                        color="rgba(71, 74, 81, 1)"
                        mt={0.5}
                        sx={{
                          display: "block",
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: "vertical",
                          whiteSpace: "pre-line",
                          overflowWrap: "break-word",
                        }}
                      >
                        {n.content}
                      </Typography>

                      <Typography
                        variant="caption"
                        color="rgba(68, 86, 91, 1)"
                        fontSize={12}
                        lineHeight="16px"
                        mt={0.75}
                        display="block"
                      >
                        {timeAgo(n.createdAt)}
                      </Typography>
                    </Box>
                  </Box>
                </ButtonBase>
              );
            })}

            {hasMore && loading && (
              // Append small skeleton rows inline so scroll position doesn't jump
              <Box sx={{ maxHeight: 400, overflowY: "auto", px: 2, py: 1 }}>
                <SkeletonRows count={2} />
              </Box>
            )}

            {/* All loaded indicator when there are items and no more to load */}
            {!hasMore && items.length > 0 && (
              <Box
                sx={{
                  // position: "sticky",
                  bottom: 0,
                  left: 0,
                  right: 0,
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  py: 1.25,
                  background: "linear-gradient(to top, rgba(0,0,0,0.03) 0%)",
                  borderBottomLeftRadius: 12,
                  // subtle fade-in
                  transition: "opacity 300ms ease",
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Image
                    src="/images/notification/mark.svg"
                    alt="done"
                    width={14}
                    height={14}
                  />
                  <Typography
                    variant="overline"
                    sx={{
                      color: "rgba(99,115,129,1)",
                    }}
                  >
                    You&apos;re all caught up
                  </Typography>
                </Box>
              </Box>
            )}
          </Box>
        ) : (
          <Box
            height={125}
            display="flex"
            gap={1.25}
            py={2}
            alignItems="center"
            justifyContent="center"
          >
            <Typography variant="body1" color="text.secondary">
              No notifications
            </Typography>
          </Box>
        )}
      </Menu>
    </>
  );
};

export default Notification;
