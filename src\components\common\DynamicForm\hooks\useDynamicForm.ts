import { useForm, UseFormReturn } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { buildYupSchema } from "../validation/schemas";
import { FieldConfig } from "../DynamicForm.types";

export const useDynamicForm = (
  fields: FieldConfig[],
  defaultValues?: Record<string, any>
): UseFormReturn<any> => {
  const schema = buildYupSchema(fields);
  return useForm({
    resolver: yupResolver(schema),
    defaultValues,
    mode: 'all',
  });
};
