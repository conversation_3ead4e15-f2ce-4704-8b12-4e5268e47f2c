import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface NotificationPayload {
  icon: string;
  title: string;
  content: string;
  tag: string;
  link: string;
}

export interface NotificationItem {
  id: number;
  payload: NotificationPayload;
  isRead: number;
  deliveryType: number;
  createdAt: string;
}

export interface NotificationState {
  items: NotificationItem[];
  currentPage: number;
  pageSize: number;
  totalItem: number;
  totalPage: number;
  hasNext: boolean;
  hasPrevious: boolean;
  unreadCount: number;
  loading: boolean;
}

const initialState: NotificationState = {
  items: [],
  currentPage: 1,
  pageSize: 10,
  totalItem: 0,
  totalPage: 0,
  hasNext: false,
  hasPrevious: false,
  unreadCount: 0,
  loading: false,
};

const notificationSlice = createSlice({
  name: 'notification',
  initialState,
  reducers: {
    setNotifications(state, action: PayloadAction<Partial<NotificationState>>) {
      return { ...state, ...action.payload };
    },
    setUnreadCount(state, action: PayloadAction<number>) {
      state.unreadCount = action.payload;
    },
    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
  },
});

export const { setNotifications, setUnreadCount, setLoading } = notificationSlice.actions;
export default notificationSlice.reducer;
