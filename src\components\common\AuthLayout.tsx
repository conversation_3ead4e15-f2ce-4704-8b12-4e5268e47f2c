import { Box, Card, Grid, Typography, <PERSON> } from "@mui/material";
import React, { ReactNode } from "react";
import Logo from "@/app/(DashboardLayout)/layout/shared/logo/Logo";
import Image from "next/image";

interface AuthLayoutProps {
  children: ReactNode;
  title?: string;
  backgroundImage?: string;
  lastLogin?: string;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({
  children,
  title = "Login",
  backgroundImage = "/images/backgrounds/login-bg.svg",
  lastLogin,
}) => {
  return (
    <Grid
      container
      columns={10}
      sx={{ minHeight: "100vh", position: "relative" }}
    >
      {/* Left: Background image only */}
      <Grid
        sx={{
          display: { xs: "none", md: "block" },
          p: 0,
          width: { md: "60%", xs: 0 },
          flexBasis: { md: "60%", xs: 0 },
          maxWidth: { md: "60%", xs: 0 },
        }}
      >
        <Box
          sx={{
            width: "100%",
            height: "100vh",
            background: `url(${backgroundImage}) no-repeat center center`,
            backgroundSize: "cover",
          }}
        />
        {/* Privacy policy link bottom center */}
      </Grid>
      {/* Right: Form card centered */}
      <Grid
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          minHeight: "100vh",
          position: "relative",
          background: '#F5F6FA',
          width: { md: "40%", xs: "100%" },
          flexBasis: { md: "40%", xs: "100%" },
          maxWidth: { md: "40%", xs: "100%" },
        }}
      >
        {/* Last login top right */}
        {lastLogin && (
          <Box
            sx={{
              position: "absolute",
              top: 24,
              right: 32,
              display: "flex",
              alignItems: "center",
              gap: 1,
            }}
          >
            <Box
              component="span"
              sx={{
                color: "text.primary",
                display: "flex",
                alignItems: "center",
                fontSize: 16,
              }}
            >
              <Box
                component="span"
                sx={{ mr: 1, display: 'flex', alignItems: 'center' }}
                aria-label="last-login-icon"
              >
                <Image src="/images/icons/notes-check.svg" alt="last login" width={20} height={20} />
              </Box>
              Last login: {lastLogin}
            </Box>
          </Box>
        )}
        <Box sx={{ width: "100%", maxWidth: 420, mx: 2, zIndex: 1 }}>
          <Box display="flex" alignItems="top" justifyContent="center" >
            {/* <Logo /> */}
                <Image src="/images/logos/dark-logo.svg" alt="logo" height={100} width={174} priority />
          </Box>
          <Typography variant="h4" align="center" fontWeight={700} mb={2} mt={2 }>
            Login
          </Typography>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="center"
            mb={2}
          >
            {/* <Logo /> */}
            {children}
          </Box>
        </Box>
        <Link
          href="/privacy-policy"
          underline="always"
          sx={{
            position: "absolute",
            left: 0,
            right: 0,
            bottom: 30,
            textAlign: "center",
            fontSize: 14,
            color: "text.primary",
          }}
        >
          Privacy policy
        </Link>
      </Grid>
    </Grid>
  );
};

export default AuthLayout;
