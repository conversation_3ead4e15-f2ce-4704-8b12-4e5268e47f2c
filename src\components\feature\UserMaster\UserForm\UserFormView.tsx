import React from 'react';
import { Box, Typography, Avatar, Paper } from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';

interface UserFormViewProps {
  fields: { name: string; label: string }[];
  values: Record<string, any>;
  avatar?: string | null;
}

const UserFormView: React.FC<UserFormViewProps> = ({
  fields,
  values,
  avatar,
}) => {
  return (
    <Paper
      elevation={0}
      sx={{
        p: 0,
        borderRadius: '20px',
        background: '#FFF',
        display: 'flex',
        gap: 0,
        alignItems: 'center',
        boxShadow: '0px 8px 32px rgba(51, 102, 255, 0.08)',
        minHeight: 220,
        overflow: 'hidden',
      }}
    >
      {/* Avatar bên trái, có viền và nền */}
      <Box
        sx={{
          width: 180,
          minWidth: 180,
          height: '100%',
          background: 'linear-gradient(180deg, #F7F9FC 0%, #E3E8F0 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRight: '1px solid #E3E8F0',
          px: 4,
          py: 5,
        }}
      >
        <Box
          sx={{
            width: 120,
            height: 120,
            borderRadius: '16px',
            border: '2px solid #3366FF',
            background: '#FFF',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'hidden',
          }}
        >
          {avatar ? (
            <Avatar
              src={avatar}
              alt="Avatar"
              sx={{ width: 120, height: 120, borderRadius: '16px' }}
            />
          ) : (
            <PersonIcon sx={{ fontSize: 56, color: '#3366FF' }} />
          )}
        </Box>
      </Box>
      {/* Thông tin cá nhân bên phải */}
      <Box sx={{ flex: 1, px: 5, py: 5 }}>
        <Typography
          variant="h6"
          sx={{
            mb: 3,
            fontWeight: 700,
            color: '#222B45',
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            fontSize: 22,
          }}
        >
          <PersonIcon sx={{ fontSize: 28, color: '#3366FF' }} />
          Personal Information
        </Typography>
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: {
              xs: '1fr',
              md: '1fr 1fr'
            },
            gap: 3
          }}
        >
          {fields.map((field) => (
            <Box key={field.name}>
              <Typography
                variant="subtitle2"
                sx={{
                  color: '#8F9BB3',
                  mb: 0.5,
                  fontSize: 14,
                }}
              >
                {field.label}
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: '#222B45',
                  fontWeight: 500,
                  fontSize: 16,
                }}
              >
                {values[field.name] ?? '--'}
              </Typography>
            </Box>
          ))}
        </Box>
      </Box>
    </Paper>
  );
};

export default UserFormView;