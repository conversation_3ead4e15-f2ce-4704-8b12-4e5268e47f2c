// src/features/auth/api/authApi.ts
import axiosInstance from "@/lib/axiosInstance";
import {
  LoginRequest,
  RegisterRequest,
  LoginResponse,
  RegisterResponse,
} from "@/types/auth";

export const loginUser = async (
  credentials: LoginRequest
): Promise<LoginResponse> => {
  const { data } = await axiosInstance.post<LoginResponse>(
    "/auth/login",
    credentials
  );
  return data;
};

export const registerUser = async (
  userData: RegisterRequest
): Promise<RegisterResponse> => {
  const { data } = await axiosInstance.post<RegisterResponse>(
    "/auth/register",
    userData
  );
  return data;
};

export const logoutUser = async (): Promise<void> => {
  await axiosInstance.post("/auth/logout");
};

export const forgotPasswordRequest = async (email: string): Promise<void> => {
  await axiosInstance.post("/auth/forgot-password", { email });
};

export const resetPasswordRequest = async (
  token: string,
  password: string
): Promise<void> => {
  await axiosInstance.post("/auth/reset-password", {
    token,
    newPassword: password,
  });
};

export const changePasswordRequest = async (changePasswordRequestBody: {
  clientId: string | undefined;
  userId: string | undefined;
  oldPassword: string;
  newPassword: string;
  confirmNewPassword: string;
}): Promise<void> => {
  await axiosInstance.put("/users/change-password", changePasswordRequestBody);
};

export const refreshTokenRequest = async (): Promise<{
  accessToken: string;
  refreshToken: string;
}> => {
  const { data } = await axiosInstance.post("/auth/refresh");
  return data;
};
