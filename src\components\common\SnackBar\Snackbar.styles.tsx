import { SxProps, Theme } from "@mui/material";

export const alertStyles = (severity: string): SxProps<Theme> => ({
  borderRadius: "4px",
  border: "1px solid",
  px: 2,
  py: 1.5,
  display: "flex",
  alignItems: "center",
  boxShadow: 3,
  width: "100%",
  maxWidth: "336px",
  overflow: "hidden",
  color: "customGrey.main",
  borderColor: severity + ".main",
  backgroundColor: severity + ".light",
});

export const boxIconStyles = (severity: string): SxProps<Theme> => ({
  width: 24,
  height: 24,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  borderRadius: "50%",
  backgroundColor: severity + ".contrastText",
  color: severity + ".main",
});

export const titleBoxStyles = (severity: string): SxProps<Theme> => ({
  fontWeight: "bold",
  marginBottom: "6px",
  whiteSpace: "nowrap",
  textOverflow: "ellipsis",
  overflow: "hidden",
  color: severity + ".main",
});

export const messageBoxStyles: SxProps<Theme> = {
  color: "customGrey.light",
};

export const actionStyles: SxProps<Theme> = {
  paddingTop: "0px",
};
