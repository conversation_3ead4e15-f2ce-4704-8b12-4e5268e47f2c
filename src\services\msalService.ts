import type { IPublicClientApplication, AccountInfo } from '@azure/msal-browser';

export function signOutMsal(instance: IPublicClientApplication, account: AccountInfo, redirectUri?: string) {
  const logoutRequest = {
    account,
    postLogoutRedirectUri: redirectUri || window.location.origin + '/authentication/login',
  };
  sessionStorage.removeItem('accessToken')
  instance.clearCache();
  window.location.href = "/authentication/login";
}
