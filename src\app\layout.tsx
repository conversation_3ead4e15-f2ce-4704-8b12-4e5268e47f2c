"use client";
import { SnackbarProvider } from "@/components/common/SnackBar/SnackbarProvider";
import { baseLightTheme } from "@/utils/theme/DefaultColors";
import { ThemeProvider } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";
import "./global.css";
import "../i18n";
import MSALProvider from "@/providers/MSALProvider";
import ReduxProvider from '@/providers/ReduxProvider';
import MuiDateLocalizationProvider from '@/providers/MuiDateLocalizationProvider';
import "@fontsource/roboto/300.css";
import "@fontsource/roboto/400.css";
import "@fontsource/roboto/500.css";
import "@fontsource/roboto/700.css";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <ReduxProvider>
          <MSALProvider>
            <MuiDateLocalizationProvider>
              <ThemeProvider theme={baseLightTheme}>
                <SnackbarProvider>
                  {/* CssBaseline kickstart an elegant, consistent, and simple baseline to build upon. */}
                  <CssBaseline />
                  {children}
                </SnackbarProvider>
              </ThemeProvider>
            </MuiDateLocalizationProvider>
          </MSALProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
