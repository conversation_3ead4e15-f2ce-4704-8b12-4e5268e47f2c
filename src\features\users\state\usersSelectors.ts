// src/features/users/state/usersSelectors.ts
import { RootState } from '@/store/reducers';

export const selectUsers = (state: RootState) => state.users.users;
export const selectUserProfile = (state: RootState) => state.users.userProfile;
export const selectUserAccessMenus = (state: RootState) => state.users.userAccessMenus;
export const selectUsersPagination = (state: RootState) => state.users.pagination;
export const selectUsersLoading = (state: RootState) => state.users.isLoading;
export const selectUsersError = (state: RootState) => state.users.error;
export const selectUsersSearch = (state: RootState) => state.users.search;
export const selectUserMasterDetail = (state: RootState) => state.users.userMasterDetail;
