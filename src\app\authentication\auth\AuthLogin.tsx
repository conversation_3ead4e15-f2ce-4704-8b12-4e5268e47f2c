// import React, { useEffect } from "react";
// import {
//   Box,
//   Typography,
//   FormGroup,
//   FormControlLabel,
//   Button,
//   Stack,
//   Checkbox,
// } from "@mui/material";
// import Link from "next/link";

// import CustomTextField from "@/app/(DashboardLayout)/components/forms/theme-elements/CustomTextField";
// import DynamicForm from '@/components/common/DynamicForm';

// interface loginType {
//   title?: string;
//   subtitle?: React.ReactNode;
//   subtext?: React.ReactNode;
// }
// const formFields = [
//   {
//     name: 'email',
//     type: 'email',
//     label: 'Email Address',
//     required: true,
//     gridSize: { xs: 12, md: 6 },
//     hiddenLabelNormal:true,
//     validation: { email: 'Invalid email format' }
//   },
//   {
//     name: 'password',
//     type: 'password',
//     label: 'Password',
//     required: true,
//     hiddenLabelNormal:true,
//     gridSize: { xs: 12, md: 6 },
//     validation: { minLength: { value: 6, message: 'Password must be at least 6 characters' } }
//   }
// ];


// function AuthLogin({ title, subtitle, subtext }: loginType) {
//   useEffect(() => {
//     const { PublicClientApplication } = require('@azure/msal-browser');
//     const { msalConfig } = require('@/providers/msalConfig');
//     const msalInstance = new PublicClientApplication(msalConfig);
//     msalInstance.handleRedirectPromise().then((response: any) => {
//       if (!response) {
//         msalInstance.loginRedirect({ scopes: ["user.read",] });
//       } else {
//         window.location.href = "/";
//       }
//     });
//   }, []);

//   function handleAzureLogin() {
//     const { PublicClientApplication } = require('@azure/msal-browser');
//     const { msalConfig } = require('@/providers/msalConfig');
//     const msalInstance = new PublicClientApplication(msalConfig);
//     msalInstance.loginPopup({ scopes: ["user.read"] })
//       .then((response: any) => {
//         window.location.href = "/";
//       })
//       .catch((e: any) => {
//         alert("Đăng nhập Microsoft thất bại: " + e.message);
//       });
//   }

//   return (
//     <>
//       {title ? (
//         <Typography fontWeight="700" variant="h2" mb={1}>
//           {title}
//         </Typography>
//       ) : null}

//       {subtext}

//       {/* <DynamicForm fields={formFields} hiddenLabelNormal={true} /> */}

//       <Box mb={2}>
//         <Button
//           color="primary"
//           variant="contained"
//           size="large"
//           fullWidth
//           component={Link}
//           href="/"
//           type="submit"
//         >
//           Sign In
//         </Button>
//       </Box>
//       <Box>
//         <Button
//           color="secondary"
//           variant="outlined"
//           size="large"
//           fullWidth
//           onClick={handleAzureLogin}
//         >
//           Đăng nhập với Microsoft
//         </Button>
//       </Box>
//       {subtitle}
//     </>
//   );
// }

// function handleAzureLogin() {
//   const { PublicClientApplication } = require('@azure/msal-browser');
//   const { msalConfig } = require('@/providers/msalConfig');
//   const msalInstance = new PublicClientApplication(msalConfig);
//   msalInstance.loginPopup({ scopes: ["user.read"] })
//     .then(response => {
//       window.location.href = "/";
//     })
//     .catch(e => {
//       alert("Đăng nhập Microsoft thất bại: " + e.message);
//     });
// }

// // Tự động đăng nhập khi vào form
// useEffect(() => {
//   const { PublicClientApplication } = require('@azure/msal-browser');
//   const { msalConfig } = require('@/providers/msalConfig');
//   const msalInstance = new PublicClientApplication(msalConfig);
//   msalInstance.handleRedirectPromise().then((response: any) => {
//     if (!response) {
//       msalInstance.loginRedirect({ scopes: ["user.read"] });
//     } else {
//       window.location.href = "/";
//     }
//   });
// }, []);

// export default AuthLogin;
