import { createUserMaster, detailUserMaster, updateUserMaster, changePassword, uploadImageFile } from "@/features/users/api/userMasterApi"
import { UserMaster } from "@/features/users/types/user";

export const getUserMaster = () => {
    
}

export const createNewUserMaster = (user: UserMaster) => {
    return createUserMaster(user);
}

export const getDetailUserMaster =  (clientId: string) => {
    return detailUserMaster(clientId);
}

export const updateDetailUserMaster = (clientId: string, user: UserMaster) => {
    return updateUserMaster(clientId, user);
}

export const updateChangePassword = (infoPassword: any) => {
    return changePassword(infoPassword);
}

export const uploadAvatar = (image: FormData) => {
    return uploadImageFile(image);
}