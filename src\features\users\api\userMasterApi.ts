import axiosInstance from "@/lib/axiosInstance";
import { UserMaster } from "../types/user";
type ApiResponse<T> = {
  success: boolean;
  code: string;
  message: string;
  params: any[];
  data: T;
};

async function apiRequest<T>(promise: Promise<any>): Promise<T> {
  const response = await promise;
  const apiResponse: ApiResponse<T> = response.data;
  if (!apiResponse.success) {
    throw new Error(apiResponse.message || "API Error");
  }
  return apiResponse.data;
}


type UploadImageFileResponse = {
  imageUrl: string;
  imagePath: string;
};

export const searchUserMaster = async (dataSearch: any): Promise<void> => {
  await axiosInstance.post("/users/search", dataSearch);
};

export const createUserMaster = async (user: UserMaster): Promise<void> => {
  return await axiosInstance.post("/users", user);
};

export const detailUserMaster = async (clientId: string): Promise<void> => {
  return (await axiosInstance.get(`/users/${clientId}`)).data;
};

export const updateUserMaster = async (
  clientId: string,
  user: UserMaster
): Promise<void> => {
  return (await axiosInstance.put(`/users/${clientId}`, user)).data;
};

export const changePassword = async (infoPassword: any): Promise<void> => {
  return await axiosInstance.put('/users/change-password', infoPassword)
}

export const uploadImageFile = async (image: FormData): Promise<UploadImageFileResponse> => {
  return (apiRequest<UploadImageFileResponse>(axiosInstance.post('users/avatar/upload', image)))
}
