import React, { ReactNode } from 'react';
import { PublicClientApplication } from '@azure/msal-browser';
import { MsalProvider } from '@azure/msal-react';
import { msalConfig } from './msalConfig';

const msalInstance = new PublicClientApplication(msalConfig);

interface MSALProviderProps {
  children: ReactNode;
}

const MSALProvider = ({ children }: MSALProviderProps) => (
  <MsalProvider instance={msalInstance}>
    {children}
  </MsalProvider>
);

export default MSALProvider;
