// src/lib/axiosInstance.ts
import axios from "axios";
import { API_BASE_URL, REQUEST_TIMEOUT, HTTP_STATUS } from "@/constants/api";
import { PublicClientApplication } from "@azure/msal-browser";
import { msalConfig } from "@/providers/msalConfig";
import { signOutMsal } from "@/services/msalService";
import { isSessionExpired } from "@/utils/sessionUtils";
import store from '@/store/store';
import { selectUserProfile } from '@/features/users/state/usersSelectors';
import { showSessionExpiredModal, showInternalServerErrorModal } from '@/features/modal/modalSlice';

// Loading event emitter
type LoadingListener = (loading: boolean) => void;
const listeners: LoadingListener[] = [];
let requestCount = 0;

export function subscribeLoading(listener: LoadingListener) {
  listeners.push(listener);
  return () => {
    const idx = listeners.indexOf(listener);
    if (idx > -1) listeners.splice(idx, 1);
  };
}

function setLoading(loading: boolean) {
  listeners.forEach((fn) => fn(loading));
}

// MSAL instance (singleton)
const msalInstance = new PublicClientApplication(msalConfig);
let msalInitialized = false;
// Axios instance
const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: REQUEST_TIMEOUT.DEFAULT,
});

// Request interceptor
axiosInstance.interceptors.request.use(
  async (config) => {
    requestCount++;
    setLoading(true);
    if (!msalInitialized) {
      await msalInstance.initialize();
      msalInitialized = true;
    }

    if (typeof window !== "undefined") {
      let token = sessionStorage.getItem("accessToken");
      let needRefresh = false;

      // Check session expired
      const user = selectUserProfile(store.getState());
      if (
        user &&
        isSessionExpired(
          { logoutTime: user.logoutTime },
          localStorage.getItem("lastLogin") || undefined
        )
      ) {
        // Instead of throwing, show the session expired modal globally
        store.dispatch(showSessionExpiredModal());
        setLoading(false);
        // Optionally, you can still sign out here if you want
        // const accounts = msalInstance.getAllAccounts();
        // if (accounts && accounts[0]) {
        //   signOutMsal(msalInstance, accounts[0]);
        // }
        // throw new Error("Session expired. Please login again.");
        // Or just return a rejected promise to stop the request
        // return Promise.reject(new Error("Session expired. Please login again."));
      }
      // Token refresh logic
      if (token) {
        try {
          const payload = JSON.parse(atob(token.split(".")[1]));
          const now = Math.floor(Date.now() / 1000);
          if (payload.exp - now < 120) needRefresh = true;
        } catch {
          needRefresh = true;
        }
      } else {
        needRefresh = true;
      }

      if (needRefresh) {
        try {
          const accounts = msalInstance.getAllAccounts();
          if (!accounts || accounts.length === 0) {
            signOutMsal(msalInstance, accounts[0]);
            setLoading(false);
            throw new Error("No MSAL account found. Please login again.");
          }
          const loginRequest = {
            scopes: [
              "openid",
              "0b135d95-2488-4110-8042-aef3b76766ed/API.Write",
              "0b135d95-2488-4110-8042-aef3b76766ed/API.Read",
            ],
            account: accounts[0],
          };
          const response = await msalInstance.acquireTokenSilent(loginRequest);
          token = response.accessToken;
          sessionStorage.setItem("accessToken", token);
        } catch (e) {
          const accounts = msalInstance.getAllAccounts();
          signOutMsal(msalInstance, accounts[0]);
          setLoading(false);
          throw e;
        }
      }

      if (token) {
        config.headers["Authorization"] = `Bearer ${token}`;
      }
    }

    return config;
  },
  (error) => {
    requestCount = Math.max(0, requestCount - 1);
    if (requestCount === 0) setLoading(false);
    return Promise.reject(new Error(error?.message || 'Request error'));
  }
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => {
    requestCount = Math.max(0, requestCount - 1);
    if (requestCount === 0) setLoading(false);
    return response;
  },
  async (error) => {
    requestCount = Math.max(0, requestCount - 1);
    if (requestCount === 0) setLoading(false);
    const originalRequest = error.config;

    // Show internal server error modal for 500 responses
    if (error.response?.status === HTTP_STATUS.INTERNAL_SERVER_ERROR) {
      store.dispatch(showInternalServerErrorModal());
    }

    if (
      (error.response?.status === HTTP_STATUS.UNAUTHORIZED ||
        error.response?.status === HTTP_STATUS.FORBIDDEN) &&
      !originalRequest._retry
    ) {
      originalRequest._retry = true;
      if (typeof window !== "undefined") {
        sessionStorage.removeItem("accessToken");
        sessionStorage.removeItem("refreshToken");
        window.location.href = '/authentication/login';
      }
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
