import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import KeyboardDoubleArrowRightIcon from '@mui/icons-material/KeyboardDoubleArrowRight';
import React from 'react';

import {
  Box,
  Checkbox,
  Pagination,
  PaginationItem,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography
} from '@mui/material';
import { MenuItem } from 'react-mui-sidebar';

export interface DynamicTableColumn {
  key: string;
  label: string;
  align?: 'left' | 'center' | 'right';
  render?: (row: any) => React.ReactNode;
  width?: string | number;
}

export interface DynamicTableProps {
  columns: DynamicTableColumn[];
  data: any[];
  page?: number;
  pageSize?: number;
  total?: number;
  onPageChange?: (page: number) => void;
  showCheckbox?: boolean;
  selectedRows?: string[];
  onSelectRow?: (rowId: string, checked: boolean) => void;
  rowKey?: string;
  renderActions?: (row: any) => React.ReactNode;
  onRowClick?: (row: any) => void;
}

const DynamicTable: React.FC<DynamicTableProps> = ({
  columns,
  data,
  page = 1,
  pageSize = 10,
  total = 0,
  onPageChange,
  showCheckbox = false,
  selectedRows = [],
  onSelectRow,
  rowKey = 'id',
  renderActions,
  onRowClick,
}) => {
  const handleCheckboxChange = (rowId: string, checked: boolean) => {
    if (onSelectRow) {
      onSelectRow(rowId, checked);
    }
  };

  // Check all logic
  const allVisibleIds = data.map((row) => row[rowKey]);
  const allChecked = allVisibleIds.length > 0 && allVisibleIds.every((id) => selectedRows.includes(id));
  const someChecked = allVisibleIds.some((id) => selectedRows.includes(id));
  const handleCheckAll = (checked: boolean) => {
    if (onSelectRow) {
      allVisibleIds.forEach((id) => {
        // Only trigger if state would change
        const isChecked = selectedRows.includes(id);
        if (checked && !isChecked) onSelectRow(id, true);
        if (!checked && isChecked) onSelectRow(id, false);
      });
    }
  };

  return (
    <Box>
      {/* Table first, pagination below */}
      <TableContainer component={Paper} sx={{ boxShadow: 'none', borderRadius: 2, border: '1px solid #E0E3E7' }}>
        <Table sx={{ minWidth: 900, borderCollapse: 'separate', borderSpacing: 0 }}>
          <TableHead>
            <TableRow sx={(theme) => ({ background: theme.palette.grey[200], height: 40, minHeight: 40 })}>
              {showCheckbox && (
                <TableCell padding="checkbox" sx={(theme) => ({ background: theme.palette.grey[200], borderTopLeftRadius: 8, /* no borderBottom, no borderRight */ })}>
                  <Checkbox
                    indeterminate={someChecked && !allChecked}
                    checked={allChecked}
                    onChange={(e) => handleCheckAll(e.target.checked)}
                    color="primary"
                  />
                </TableCell>
              )}
              {columns.map((col, idx) => (
                <TableCell
                  key={col.key}
                  align={col.align || 'left'}
                  sx={(theme) => ({
                    fontWeight: 700,
                    color: '#212B36',
                    fontSize: 14,
                    background: theme.palette.grey[200],
                    borderRight: idx !== columns.length - 1 ? '1px solid #E0E3E7' : undefined,
                    height: 40,
                    minHeight: 40,
                    py: 0,
                    textAlign: 'center',
                    // No borderBottom
                    // ...(col.width ? { width: col.width } : {}),
                    ...(showCheckbox && idx === 0 ? { borderTopLeftRadius: 8 } : {}),
                  })}
                >
                  {col.label}
                </TableCell>
              ))}
              <TableCell align="center" sx={(theme) => ({ background: theme.palette.grey[200], borderTopRightRadius: 8, width: 40, /* no borderBottom, no borderRight */ })} />
            </TableRow>
          </TableHead>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length + (showCheckbox ? 2 : 1)} align="center" sx={{ py: 6 }}>
                  <Typography color="text.secondary">No data</Typography>
                </TableCell>
              </TableRow>
            ) : (
              data.map((row, rowIdx) => (
                <TableRow
                      key={`${row[rowKey]}-${rowIdx}`} // Ensures uniqueness even if IDs repeat

                  hover
                  sx={{
                    background: rowIdx % 2 === 0 ? '#fff' : '#F5F6FA',
                    '&:hover': { background: '#F1F5F9', cursor: onRowClick ? 'pointer' : undefined },
                    height: 40,
                  }}
                  onClick={onRowClick ? () => onRowClick(row) : undefined}
                >
                  {showCheckbox && (
                    <TableCell padding="checkbox" sx={{ /* no borderBottom */ }} onClick={e => e.stopPropagation()}>
                      <Checkbox
                        checked={selectedRows.includes(row[rowKey])}
                        onChange={(e) => handleCheckboxChange(row[rowKey], e.target.checked)}
                        color="primary"
                      />
                    </TableCell>
                  )}
                  {columns.map((col) => (
                    <TableCell
                      key={col.key}
                      align={col.align || 'left'}
                      sx={{
                        // no borderBottom
                        fontSize: 14,
                        py: 0,
                        px: 2,
                        color: '#212B36',
                        background: 'inherit',
                        height: 40,
                        minHeight: 40,
                      }}
                    >
                      {col.render ? col.render(row) : row[col.key]}
                    </TableCell>
                  ))}
                  <TableCell
                    align="center"
                    sx={{ background: 'inherit', py: 0 }}
                    onClick={e => e.stopPropagation()}
                  >
                    {renderActions ? renderActions(row) : null}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      {/* Figma-style pagination below table */}
      <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ my: 2, px: 2, }}>
        {/* Left: summary text + page size selector */}
        <Box display="flex" alignItems="center" gap={3}>
          {/* Summary text */}
          <Typography fontSize={14} color="#637381">
            {(() => {
              const start = total === 0 ? 0 : (page - 1) * pageSize + 1;
              const end = Math.min(page * pageSize, total);
              return `Showing ${start}-${end} of ${total} users`;
            })()}
          </Typography>
          {/* Page size selector */}
          <Box display="flex" alignItems="center" gap={1}>
            <Typography fontSize={14} color="text.primary">Results per page</Typography>
            <Select variant='outlined'
              IconComponent={ExpandMoreIcon}
              aria-label="Chọn số dòng trên mỗi trang"
              title="Chọn số dòng trên mỗi trang"
              value={pageSize}
              onChange={e => onPageChange && onPageChange(1)} // reset to page 1 on size change
              size="small"
              sx={{
                '& .MuiListItemIcon-root': { display: 'none' },
                border: 'none'
              }}
            >
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={20}>20</MenuItem>
              <MenuItem value={50}>50</MenuItem>
            </Select>
          </Box>
        </Box>
        {/* Pagination controls */}
        <Pagination
          count={Math.max(1, Math.ceil(total / pageSize))}
          page={page}
          onChange={(_, value) => onPageChange && onPageChange(value)}
          color="primary"
          shape="rounded"
          size="medium"
          showFirstButton
          showLastButton
          siblingCount={1}
          boundaryCount={1}
          renderItem={(item) => (
            <PaginationItem
              slots={{ first: KeyboardDoubleArrowLeftIcon, last: KeyboardDoubleArrowRightIcon }}
              {...item}
            />
          )}

          sx={{
            ml: 'auto',
            '& .MuiPagination-ul': { gap: 1 }, // 8px = theme.spacing(1)
            '& .MuiPaginationItem-root': {
              borderRadius: '6px',
              border: '1px solid #E0E3E7',
              minWidth: 32,
              height: 32,
              fontSize: 14,
              boxSizing: 'border-box',
            },
          }}
        />
      </Box>
    </Box>
  );
};

export default DynamicTable;
