import Button from "@/components/common/button/Button";
import PopupChangePassword from "@/components/common/PopupChangePassword";
import { useScreenAccessDecision } from "@/features/auth/hooks/ScreenAccessDecision";
import { useBreadcrumb } from "@/features/breadcrumb/hook/useBreadcrumb";
import { selectUserProfile } from "@/features/users/state/usersSelectors";
import * as userMasterService from "@/services/userMasterService";
import { Box, Divider, IconButton, Tab, Tabs, Typography } from "@mui/material";
import Image from "next/image";
import NextLink from "next/link";
import React, { useMemo, useState } from "react";
import { useSelector } from "react-redux";
import ProfileHeader from "../ProfileHeader";
import { PAGE_TITLES } from "@/constants/routes";

interface InfoFieldData {
  label: string;
  value: string;
}

interface UserDetailClientProps {
  userId: string;
  infoFields: InfoFieldData[][]; // Array of rows, each row is array of InfoFieldData
  infoUser: any;
  securityFields: InfoFieldData[][];
}

interface InfoFieldProps {
  label: string;
  value: string;
  onEditPassword?: () => void;
}

const InfoField: React.FC<InfoFieldProps> = ({ label, value, onEditPassword }) => (
  <Box
    sx={{
      width: 374,
      display: "flex",
      flexDirection: "column",
      gap: 1,
      borderBottom: "1px solid rgba(162, 161, 168, 0.2)",
    }}
  >
    <Typography
      sx={{
        color: "#bfc4cc",
        fontSize: 14,
        fontFamily: "Roboto, sans-serif",
        mb: "5px",
      }}
    >
      {label}
    </Typography>
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
      }}
    >
      <Typography
        sx={{
          color: "#222222",
          fontSize: 16,
          fontFamily: "Roboto, sans-serif",
        }}
      >
        {value}
      </Typography>
      {label === "Password" && onEditPassword && (
        <IconButton size="small" sx={{ ml: 1 }} onClick={onEditPassword}>
          <Image src="/images/user/pencil.svg" alt="pencil edit" height={24} width={24} />
        </IconButton>
      )}
    </Box>
  </Box>
);

const UserDetailClient: React.FC<UserDetailClientProps> = ({ userId, infoFields = [], infoUser, securityFields = [] }) => {
  const { canDecide, canViewByPath } = useScreenAccessDecision();
  const showEditBtn = canDecide && canViewByPath("/user-master/edit");
  const user = useMemo(
    () => ({
      ...infoUser,
    }),
    [infoUser]
  );
  useBreadcrumb({
    pageTitle: "User Detail",
    items: [
      { id: "user-master", title: PAGE_TITLES.USER_LIST, href: "/user-master" },
      { id: `user-detail`, title: `${user.lastName} ${user.firstName}` },
    ],
  });
  const lockStatus = !user.lockStatus;
  const securityField = useMemo(() => securityFields, [securityFields]);
  const displayInfoFields = Array.isArray(infoFields) && infoFields.length > 0 ? infoFields : [];
  const [openChangePassword, setOpenChangePassword] = useState(false);
  const [tabIndex, setTabIndex] = useState(0);
  const userProfile = useSelector(selectUserProfile);
  const [errorForm, SetErrorForm] = useState({});

  // Lấy current user id từ localStorage (nếu có lưu userProfile)
  let currentClientId = "";
  let currentUserId = "";
  if (typeof window !== "undefined") {
    try {
      if (userProfile) {
        currentClientId = userProfile.clientId || "";
        currentUserId = userProfile?.userId || "";
      }
    } catch {}
  }
  const showSecurityTab = userId === currentClientId;

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabIndex(newValue);
  };

  const removeSpace = (str: string) => {
    return str.replace(/ (\w)/g, (_, c) => c.toUpperCase());
  };

  return (
    <Box
      sx={{
        minHeight: "calc(100vh - 220px)",
        display: "flex",
        flexDirection: "column",
        gap: "10px",
        width: "100%",
        height: "100%",
        padding: "20px",
        borderRadius: "12px",
        border: "1px solid rgba(191, 196, 204, 1)",
        position: "relative",
        background: "#fff",
      }}
    >
      <PopupChangePassword
        open={openChangePassword}
        onClose={() => setOpenChangePassword(false)}
        onSubmit={(values) => {
          if (values.newPassword.includes(currentUserId.split("@")[0])) {
            SetErrorForm({
              newPassword: "The new password you entered is the same as your user Id. Enter a different password.",
            });
          } else {
            const infoPassword = {
              clientId: userId,
              oldPassword: values.oldPassword,
              newPassword: values.newPassword,
              confirmNewPassword: values.confirmNewPassword,
            };
            userMasterService
              .updateChangePassword(infoPassword)
              .then(() => {
                setOpenChangePassword(false);
              })
              .catch((error) => {
                SetErrorForm({ oldPassword: "Try again—that's not your old password." });
              });
          }
        }}
        errors={errorForm}
      />
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "flex-start",
        }}
      >
        <ProfileHeader
          name={`${user.lastName} ${user.firstName}`}
          roleName={user.roleName}
          userId={user.userId}
          lockStatus={lockStatus}
          avatarUrl={user.imageUrl}
        />
        <Divider sx={{ width: "100%", bgcolor: "rgba(162,161,168,0.2)" }} />
        {showEditBtn && (
          <Box position={"absolute"} right={20} top={166} zIndex={1} display="flex" justifyContent="flex-end">
            <Button
              href={`/user-master/edit?id=${userId}`}
              LinkComponent={NextLink}
              type="button"
              variant="contained"
              size="small"
              startIcon={<Image src={"/images/user/edit.svg"} alt="edit user" height={20} width={20} />}
            >
              Edit Profile
            </Button>
          </Box>
        )}
      </Box>

      <Box>
        {/* <button
          style={{ padding: '8px 16px', borderRadius: '4px', background: '#a20025', color: '#fff', border: 'none', cursor: 'pointer' }}
          onClick={() => setOpenPopup(true)}
        >
          Action
        </button> */}
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: 2.5,
            width: "100%",
          }}
        >
          {/* Tabbar */}
          <Tabs value={tabIndex} onChange={handleTabChange} sx={{ mb: 3, borderBottom: "1px solid rgba(162,161,168,0.2)" }}>
            <Tab
              icon={
                <Image
                  src={`/images/user/${tabIndex === 0 ? "user-tab.svg" : "user-tab-inactive.svg"}`}
                  alt="user tab"
                  width={24}
                  height={24}
                ></Image>
              }
              iconPosition="start"
              label="Personal Information"
              sx={{
                fontWeight: 400,
                fontSize: 16,
                color: "rgba(34, 34, 34, 1)",
                minHeight: 48,
                minWidth: 220,
                textTransform: "none",
                "&.Mui-selected": {
                  color: "primary.main",
                  borderBottom: "3px solid",
                  fontWeight: 700,
                },
              }}
            />
            {showSecurityTab && (
              <Tab
                icon={
                  <Image
                    src={`/images/user/${tabIndex === 1 ? "lock-tab.svg" : "lock-tab-inactive.svg"}`}
                    alt="user tab"
                    width={24}
                    height={24}
                  ></Image>
                }
                iconPosition="start"
                label="Security"
                sx={{
                  fontWeight: 400,
                  fontSize: 16,
                  color: "rgba(34, 34, 34, 1)",
                  minHeight: 48,
                  minWidth: 220,
                  textTransform: "none",
                  "&.Mui-selected": {
                    color: "primary.main",
                    borderBottom: "3px solid",
                    fontWeight: 700,
                  },
                }}
              />
            )}
          </Tabs>
          {tabIndex === 0 && (
            <>
              {displayInfoFields.map((row) => (
                <Box
                  key={row.map((f) => f.label).join("-")}
                  sx={{ display: "flex", gap: 1.25, columnGap: 3.75, flexWrap: "wrap" }}
                >
                  {row.map((field) => (
                    <InfoField key={removeSpace(field.label)} label={field.label} value={field.value} />
                  ))}
                  {/* If row has only one field, add empty box for layout */}
                  {row.length === 1 && <Box sx={{ width: 374 }} />}
                </Box>
              ))}
            </>
          )}
          {tabIndex === 1 && showSecurityTab && (
            <>
              {securityField.map((row) => (
                <Box key={row.map((f) => f.label).join("-")} sx={{ display: "flex", gap: 1.25, columnGap: 3.75 }}>
                  {row.map((field) => (
                    <InfoField
                      key={removeSpace(field.label)}
                      label={field.label}
                      value={field.value}
                      onEditPassword={field.label === "Password" ? () => setOpenChangePassword(true) : undefined}
                    />
                  ))}
                  {/* If row has only one field, add empty box for layout */}
                  {row.length === 1 && <Box sx={{ width: 374 }} />}
                </Box>
              ))}
            </>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default UserDetailClient;
