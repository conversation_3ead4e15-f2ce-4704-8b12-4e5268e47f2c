import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';

const Loading = () => {
    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '100vh',
                background: 'linear-gradient(135deg, #e3f2fd 0%, #fce4ec 100%)',
                animation: 'fadeIn 1.2s',
                '@keyframes fadeIn': {
                    from: { opacity: 0 },
                    to: { opacity: 1 },
                },
            }}
        >
            <Box sx={{ mb: 3}}>
                <Box
                    component="img"
                    src="/images/gif/loadingAll.gif"
                    alt="Loading"
                    sx={{
                        width: 140,
                        height: 140,
                        borderRadius: '50%',
                        objectFit: 'cover',
                        display: 'block',
                    }}
                />
            </Box>
            {/* <Typography variant="h6" color="primary" sx={{ fontWeight: 500, letterSpacing: 1 }}>
                Loading...
            </Typography> */}
        </Box>
    );
};

export default Loading;