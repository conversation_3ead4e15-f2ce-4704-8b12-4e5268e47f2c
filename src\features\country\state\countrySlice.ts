import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import * as countryService from "@/services/countryService";
import { CountryState } from "../types/country";

const initialState: CountryState = {
  list: [],
  error: null,
  isLoading: true,
};
const nameState = "country";

export const country = createAsyncThunk(`${nameState}/list`, async () => {
  try {
    const res = await countryService.getCountryList();
    return res;
  } catch (err: any) {
    console.log(err);
  }
});

const countrySlice = createSlice({
  name: nameState,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(country.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(country.fulfilled, (state, action: any) => {
        state.isLoading = false;
        state.list = action.payload;
      })
      .addCase(country.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as any;
      });
  },
});

export default countrySlice.reducer;
