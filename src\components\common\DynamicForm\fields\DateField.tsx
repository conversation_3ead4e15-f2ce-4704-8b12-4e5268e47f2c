import React from "react";
import { Box, TextField } from "@mui/material";
import { Controller } from "react-hook-form";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { FieldConfig } from "../DynamicForm.types";
import Image from "next/image";

interface Props {
  field: FieldConfig;
  control: any;
  error?: string;
  disabled?: boolean;
  i18n?: (key: string) => string;
}

const CalendarIcon = () => (
  <Image src="/images/icons/calendar.svg" alt="calendar" width={18} height={18} style={{ marginRight: 2 }} />
);
const DateField: React.FC<Props> = ({ field, control, error, disabled, i18n }) => (
  <Controller
    name={field.name}
    control={control}
    render={({ field: controllerField }) => {
      const isRequired = field.required;
      const handleChange = (date: any) => {
        if (date === null) {
          if (isRequired) {
            return;
          }
          controllerField.onChange(undefined);
          return;
        }
        controllerField.onChange(date);
      };

      return field.hiddenLabelNormal ? (
        <Box
          sx={{
            position: "relative",
            width: "100%",
            ".Mui-focused .MuiPickersOutlinedInput-notchedOutline": {
              borderColor: "#000000ff !important",
            },
          }}
        >
          <Box
            sx={{
              position: "absolute",
              left: 0,
              right: 0,
              top: 2,
              color: "#222222",
              fontFamily: "Roboto",
              fontWeight: 400,
              fontSize: 14,
              lineHeight: 1.5,
              zIndex: 2,
            }}
          >
            {i18n ? i18n(field.label) : field.label}
          </Box>
          <DatePicker
            label=""
            value={controllerField.value || null}
            onChange={handleChange}
            disabled={disabled || field.disabled}
            format="DD/MM/YYYY"
            slots={{ openPickerIcon: CalendarIcon }}
            disableFuture={field.disableFuture}
            slotProps={{
              field: { clearable: true },
              textField: {
                InputProps: {
                  onBeforeInput: (e: any) => e.preventDefault(),
                  onPaste: (e: React.ClipboardEvent) => e.preventDefault(),
                  onDrop: (e: React.DragEvent) => e.preventDefault(),
                  onKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => e.preventDefault(),
                },
                error: !!error,
                helperText: error || field.helperText,
                fullWidth: true,
                sx: {
                  ...field.style,
                  mt: 3,
                  // Target the generated MUI class for OutlinedInput root
                  "& .MuiOutlinedInput-root, & .MuiPickersOutlinedInput-root, & .MuiPickersInputBase-root": {
                    height: "34px",
                    borderRadius: "4px",
                  },
                  "& .MuiPickersInputBase-sectionsContainer, & .MuiPickersOutlinedInput-sectionsContainer": {
                    width: "auto",
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#A2A1A8",
                    borderWidth: 1,
                  },
                },
                className: field.className,
                InputLabelProps: { shrink: true },
                inputProps: { "aria-label": field.label },
              },
            }}
          />
        </Box>
      ) : (
        <DatePicker
          label={i18n ? i18n(field.label) : field.label}
          value={controllerField.value || null}
          onChange={(date) => controllerField.onChange(date)}
          disabled={disabled || field.disabled}
          format="DD/MM/YYYY"
          disableFuture={field.disableFuture}
          slotProps={{
            textField: {
              error: !!error,
              helperText: error || field.helperText,
              fullWidth: true,
              sx: {
                ...field.style,
                "& .MuiOutlinedInput-root, & .MuiPickersOutlinedInput-root, & .MuiPickersInputBase-root": {
                  height: "34px",
                  borderRadius: "4px",
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#A2A1A8",
                  borderWidth: 1,
                },
              },
              className: field.className,
              InputLabelProps: { shrink: true },
              inputProps: { "aria-label": field.label },
            }
          }}
        />
      );
    }}
  />
);

export default DateField;
