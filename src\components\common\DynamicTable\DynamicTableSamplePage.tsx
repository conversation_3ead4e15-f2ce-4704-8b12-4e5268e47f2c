import React, { useState } from 'react';
import DynamicTable, { DynamicTableColumn } from '@/components/common/DynamicTable/DynamicTable';
import { Box, Typography } from '@mui/material';

const columns: DynamicTableColumn[] = [
  { key: 'order', label: 'STT', width: 60, align: 'center' },
  { key: 'code', label: 'Số chứng chỉ', width: 180 },
  { key: 'product', label: 'Tên sản phẩm', width: 200 },
  { key: 'customer', label: 'Khách hàng', width: 200 },
  { key: 'createdAt', label: 'Ngày tạo', width: 140 },
  { key: 'createdBy', label: 'Người tạo', width: 140 },
  { key: 'status', label: 'Trạng thái', width: 120, render: (row) => (
    <Typography color={row.status === 'Đã phát hành' ? 'primary' : 'text.secondary'} fontWeight={500}>
      {row.status}
    </Typography>
  ) },
];

const sampleData = [
  {
    id: '1',
    order: 1,
    code: 'CC-001',
    product: 'Thép cuộn cán nóng',
    customer: 'Công ty A',
    createdAt: '2024-06-01',
    createdBy: 'Nguyễn Văn A',
    status: 'Đã phát hành',
  },
  {
    id: '2',
    order: 2,
    code: 'CC-002',
    product: 'Thép tấm',
    customer: 'Công ty B',
    createdAt: '2024-06-02',
    createdBy: 'Trần Thị B',
    status: 'Chờ duyệt',
  },
  {
    id: '3',
    order: 3,
    code: 'CC-003',
    product: 'Thép hình',
    customer: 'Công ty C',
    createdAt: '2024-06-03',
    createdBy: 'Lê Văn C',
    status: 'Đã phát hành',
  },
];

const PAGE_SIZE = 10;

const DynamicTableSamplePage: React.FC = () => {
  const [page, setPage] = useState(1);
  const [selectedRows, setSelectedRows] = useState<string[]>([]);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleSelectRow = (rowId: string, checked: boolean) => {
    setSelectedRows((prev) =>
      checked ? [...prev, rowId] : prev.filter((id) => id !== rowId)
    );
  };

  return (
    <Box p={4}>
      <DynamicTable
        columns={columns}
        data={sampleData}
        page={page}
        pageSize={PAGE_SIZE}
        total={25}
        onPageChange={handlePageChange}
        showCheckbox
        selectedRows={selectedRows}
        onSelectRow={handleSelectRow}
        rowKey="id"
      />
    </Box>
  );
};

export default DynamicTableSamplePage;
