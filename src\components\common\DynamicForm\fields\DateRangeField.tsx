import React from "react";
import dayjs from "dayjs";
import Box from "@mui/material/Box";
import { Controller } from "react-hook-form";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { FieldConfig } from "../DynamicForm.types";
import Image from "next/image";
import { IconButton, InputAdornment, Typography } from "@mui/material";
import { ClearIcon } from "@mui/x-date-pickers";

// Common calendar icon component for DatePicker
const CalendarIcon = () => (
  <Image src="/images/icons/calendar.svg" alt="calendar" width={18} height={18} style={{ marginRight: 2 }} />
);

interface Props {
  field: FieldConfig;
  control: any;
  error?: string;
  disabled?: boolean;
  i18n?: (key: string) => string;
}

const DateRangeField: React.FC<Props> = ({ field, control, error, disabled, i18n }) => (
  <Controller
    name={field.name}
    control={control}
    render={({ field: controllerField }) => {
      // Destructure start and end date from value
      const [start, end] = controllerField.value || [null, null];

      // Validation: end >= start, start <= end
      let startError = "";
      let endError = "";
      if (start && end) {
        if (end < start) {
          // endError = i18n
          //   ? i18n("End date must be after start date")
          //   : "End date must be after start date";
          startError = i18n ? i18n("Start date cannot be later than end date") : "Start date cannot be later than end date";
        }
      }

      // Handle change for start or end date
      const handleChange = (idx: 0 | 1, date: any) => {
        const next = [...(controllerField.value || [null, null])] as [any, any];
        next[idx] = date;
        controllerField.onChange(next);
      };

      // Không cho chọn ngày tương lai
      const today = dayjs();

      return (
        <Box sx={{ display: "flex", maxWidth: "260px", flexDirection: "column" }}>
          <Box style={{ position: "relative", width: "260px" }}>
            {/* Label above the date range fields */}
            <Box
              style={{
                position: "absolute",
                left: 0,
                right: 0,
                top: 0,
                color: "#222222",
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: 14,
                lineHeight: 1.5,
                zIndex: 2,
              }}
            >
              {i18n ? i18n(field.label) : field.label}
            </Box>
            <Box
              sx={{
                mt: 3,
                display: "flex",
                flexDirection: "row",
                alignItems: "flex-end",
                gap: 0.75,
              }}
            >
              {/* Start date picker */}
              <Box
                sx={{
                  width: "100%",
                  minWidth: 0,
                  flex: 1,
                  display: "flex",
                  flexDirection: "column",
                  ".Mui-focused .MuiPickersOutlinedInput-notchedOutline": {
                    borderColor: "#000000ff !important",
                  },
                }}
              >
                <DatePicker
                  label=""
                  value={start || null}
                  onChange={(date) => handleChange(0, date)}
                  disabled={disabled || field.disabled}
                  format="DD/MM/YYYY"
                  // Không cho chọn ngày tương lai
                  maxDate={end && end.isBefore(today) ? end : today}
                  slots={{ openPickerIcon: CalendarIcon }}
                  slotProps={{
                    field: { clearable: true },

                    textField: {
                      InputProps: {
                        onBeforeInput: (e: any) => e.preventDefault(),
                        onPaste: (e: React.ClipboardEvent) => e.preventDefault(),
                        onDrop: (e: React.DragEvent) => e.preventDefault(),
                        onKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => e.preventDefault(),
                      },
                      error: !!error || !!startError,
                      // helperText: startError || undefined,
                      fullWidth: true,
                      placeholder: "DD/MM/YYYY",
                      sx: {
                        ...field.style,
                        "& .MuiOutlinedInput-root, & .MuiPickersOutlinedInput-root, & .MuiPickersInputBase-root": {
                          height: "34px",
                          borderRadius: "4px",
                          padding: "0px 4px",
                        },
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#A2A1A8",
                          borderWidth: 1,
                        },
                        "& .MuiIconButton-root": {
                          padding: 0,
                          marginRight: 0,
                        },
                        "& .MuiInputAdornment-root": {
                          marginLeft: 0,
                        },
                      },
                      className: field.className,
                      InputLabelProps: { shrink: true },
                      inputProps: {
                        "aria-label": i18n ? i18n("Start date") : "Start date",
                        placeholder: "DD/MM/YYYY",
                      },
                    },
                  }}
                />
                {/* <Box sx={{ fontSize: 12, color: "#888", mt: 0.5, ml: 0.5 }}>
                {i18n ? i18n("From") : "From"}
              </Box> */}
              </Box>
              {/* End date picker */}
              <Box
                sx={{
                  width: "100%",
                  minWidth: 0,
                  flex: 1,
                  display: "flex",
                  flexDirection: "column",
                  ".Mui-focused .MuiPickersOutlinedInput-notchedOutline": {
                    borderColor: "#000000ff !important",
                  },
                }}
              >
                <DatePicker
                  label=""
                  value={end || null}
                  onChange={(date) => handleChange(1, date)}
                  disabled={disabled || field.disabled}
                  format="DD/MM/YYYY"
                  // Không cho chọn ngày tương lai
                  minDate={start || undefined}
                  maxDate={today}
                  slots={{ openPickerIcon: CalendarIcon }}
                  slotProps={{
                    field: { clearable: true },

                    textField: {
                      InputProps: {
                        onBeforeInput: (e: any) => e.preventDefault(),
                        onPaste: (e: React.ClipboardEvent) => e.preventDefault(),
                        onDrop: (e: React.DragEvent) => e.preventDefault(),
                        onKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => e.preventDefault(),
                      },
                      error: !!error || !!startError,
                      // helperText: endError || error || field.helperText,
                      fullWidth: true,
                      placeholder: "DD/MM/YYYY",
                      // readOnly: true,
                      sx: {
                        ...field.style,
                        "& .MuiOutlinedInput-root, & .MuiPickersOutlinedInput-root, & .MuiPickersInputBase-root": {
                          height: "34px",
                          borderRadius: "4px",
                          padding: "0px 4px",
                        },
                        "&.MuiOutlinedInput-notchedOutline": {
                          borderColor: "#A2A1A8",
                          borderWidth: 1,
                        },
                        "& .MuiIconButton-root": {
                          padding: 0,
                          marginRight: 0,
                        },
                        "& .MuiInputAdornment-root": {
                          marginLeft: 0,
                        },
                      },
                      className: field.className,
                      InputLabelProps: { shrink: true },
                      inputProps: {
                        "aria-label": i18n ? i18n("End date") : "End date",
                        placeholder: "DD/MM/YYYY",
                      },
                    },
                  }}
                />
                {/* <Box sx={{ fontSize: 12, color: "#888", mt: 0.5, ml: 0.5 }}>
                {i18n ? i18n("To") : "To"}
              </Box> */}
              </Box>
            </Box>
          </Box>
          {startError && <Typography color="rgb(250, 137, 107)">{startError}</Typography>}
        </Box>
      );
    }}
  />
);

export default DateRangeField;
