import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import * as roleService from "@/services/roleService";
import { RoleState } from "../types/role";

const initialState: RoleState = {
  tempList: [],
  list: [],
  error: null,
  isLoading: true,
};
const nameState = "role";

export const role = createAsyncThunk(`${nameState}/list`, async (organizationCode: string) => {
  try {
    const res = await roleService.getRoleList(organizationCode);
    return res;
  } catch (err: any) {}
});

export const getRoleByOrganizationCode = createAsyncThunk(`${nameState}/tempList`, async (organizationCode: string) => {
  try {
    const res = await roleService.getRoleList(organizationCode);
    return res;
  } catch (error) {}
});

const roleSlice = createSlice({
  name: nameState,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(role.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(role.fulfilled, (state, action: any) => {
        state.isLoading = false;
        state.list = action.payload;
      })
      .addCase(role.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as any;
      })
      // For Temp Role List By Org Code
      .addCase(getRoleByOrganizationCode.fulfilled, (state, action: any) => {
        state.isLoading = false;
        state.tempList = action.payload;
      });
  },
});

export default roleSlice.reducer;
