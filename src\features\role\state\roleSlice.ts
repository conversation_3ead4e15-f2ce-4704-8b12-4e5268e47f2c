import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import * as roleService from "@/services/roleService";
import { RoleState } from "../types/role";
import { useUser } from "@/features/users/state/useUser";

const initialState: RoleState = {
  list: [],
  error: null,
  isLoading: true,
};
const nameState = "role";

export const role = createAsyncThunk(`${nameState}/list`, async (organizationCode: string) => {
  try {
    const res = await roleService.getRoleList(organizationCode);
    return res;
  } catch (err: any) {
    console.log(err);
  }
});

const roleSlice = createSlice({
  name: nameState,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(role.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(role.fulfilled, (state, action: any) => {
        state.isLoading = false;
        state.list = action.payload;
      })
      .addCase(role.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as any;
      });
  },
});

export default roleSlice.reducer;
