import { SxProps, Theme } from '@mui/material';

export const iconButton: SxProps<Theme> = {
  borderRadius: '10px',
  padding: '13px',
  backgroundColor: '#ECEFF4',
  '&:hover': { backgroundColor: '#ECEFF4' },
  height: '50px',
  width: '50px',
};

export const badge: SxProps<Theme> = {
  '.MuiBadge-standard': {
    border: '0.5px solid #A20025',
    backgroundColor: '#CA002E',
    color: '#fff',
    height: '18px',
    width: '18px',
    minWidth: 'auto',
    top: '1px',
    right: '3px',
    fontSize: '9px',
  },
};

export const menuPaper: SxProps<Theme> = {
  borderRadius: '12px',
  overflow: 'hidden',
  mt: 1,
  ml: 27,
  width: '360px',
  '.MuiList-padding': {
    paddingTop: 'unset',
    paddingBottom: 'unset',
  },
  border: '1px solid rgba(229, 231, 235, 1)',
};

export const divider: SxProps<Theme> = {
  border: '1.25px solid rgba(236, 239, 244, 1)',
};

export const skeletonRow: SxProps<Theme> = {
  display: 'flex',
  gap: 1.5,
  py: 1.25,
  minHeight: 64,
  alignItems: 'center',
  opacity: 0,
  animation: 'fadeIn 220ms ease forwards',
  '@keyframes fadeIn': {
    to: { opacity: 1 },
  },
};
