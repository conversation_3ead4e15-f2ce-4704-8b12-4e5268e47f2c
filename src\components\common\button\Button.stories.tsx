
import React from 'react';
import Button from './Button';
import { Add, ArrowForward } from '@mui/icons-material';

const story = {
  title: 'Common/Button',
  component: Button,
};

export default story;

export const Primary = () => (
  <Button prefixIcon={<Add />} suffixIcon={<ArrowForward />}>Primary Button</Button>
);

export const Secondary = () => (
  <Button variant="outlined" prefixIcon={<Add />} suffixIcon={<ArrowForward />}>Secondary Button</Button>
);

export const Disabled = () => (
  <Button disabled prefixIcon={<Add />}>Disabled Button</Button>
);

export const WithAriaLabel = () => (
  <Button ariaLabel="Add item" prefixIcon={<Add />}>Add Item</Button>
);
