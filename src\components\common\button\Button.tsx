import React from "react";
import CircularProgress from "@mui/material/CircularProgress";
import MuiButton, { ButtonProps as MuiButtonProps } from "@mui/material/Button";
import { SxProps, Theme } from "@mui/material/styles";
import { useTheme } from "@mui/material/styles";

export interface ButtonProps extends MuiButtonProps {
  prefixIcon?: React.ReactNode;
  suffixIcon?: React.ReactNode;
  ariaLabel?: string;
  sx?: SxProps<Theme>;
  size?: "small" | "medium" | "large";
  isLoading?: boolean;
  iconSize?: number;
}

const Button: React.FC<ButtonProps> = ({
  children,
  prefixIcon,
  suffixIcon,
  variant = "contained",
  color = "primary",
  ariaLabel,
  sx,
  size = "medium",
  isLoading = false,
  iconSize = 20,
  ...rest
}) => {
  const theme = useTheme();

  // Determine height based on size
  const buttonHeight = size === "small" ? "34px" : "50px";

  // Custom styles for primary and secondary variants
  const getButtonStyles = () => {
    if (variant === "contained" && color === "primary") {
      return {
        backgroundColor: theme.palette.primary.main, // Sử dụng primary mới
        color: theme.palette.common.white,
        "&:hover": {
          backgroundColor: theme.palette.primary.dark,
          boxShadow: "none",
        },
        borderRadius: theme.shape.borderRadius,
        fontWeight: 700, // Đúng với typography Figma
        padding: "10px",
        fontSize: "14px", // Đúng với typography Figma
        lineHeight: 1.5,
        fontFamily: "Roboto, Helvetica, Arial, sans-serif",
        height: buttonHeight,
        boxShadow: "none",
      };
    }
    if (variant === "outlined" && color === "primary") {
      return {
        borderColor: theme.palette.grey[500], // Sử dụng màu xám 500 cho border
        color: theme.palette.text.primary,
        backgroundColor: "transparent",
        "&:hover": {
          backgroundColor: theme.palette.grey[100],
          borderColor: theme.palette.grey[600],
        },
        borderRadius: theme.shape.borderRadius,
        fontWeight: 600,
        fontSize: "14px", // Đúng với typography Figma,
        padding: "10px",
        height: buttonHeight,
      };
    }
    return {
      height: buttonHeight,
    };
  };

  return (
    <MuiButton
      variant={variant}
      color={color}
      aria-label={
        ariaLabel || (typeof children === "string" ? children : undefined)
      }
      startIcon={
        isLoading && prefixIcon ? (
          <CircularProgress size={iconSize} color="inherit" />
        ) : (
          prefixIcon
        )
      }
      endIcon={
        isLoading && suffixIcon ? (
          <CircularProgress size={iconSize} color="inherit" />
        ) : (
          suffixIcon
        )
      }
      sx={{
        ...getButtonStyles(),
        ...sx,
        "& .MuiButton-startIcon": {
          marginLeft: 0, // Remove default -4px margin
        },
      }}
      size={size}
      disabled={isLoading || rest.disabled}
      {...rest}
    >
      {!prefixIcon && !suffixIcon && isLoading ? (
        <CircularProgress size={iconSize} color="inherit" />
      ) : (
        children
      )}
    </MuiButton>
  );
};

export default Button;
