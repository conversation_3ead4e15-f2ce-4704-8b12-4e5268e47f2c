"use client";
import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import PageContainer from "@/app/(DashboardLayout)/components/container/PageContainer";
import GridMenu from "@/components/common/GridMenu";
import { selectUserAccessMenus } from "@/features/users/state/usersSelectors";
import Image from "next/image";
// components
import { ViewPermission } from "@/features/auth/hooks/ScreenAccessDecision";
import { useBreadcrumb } from "@/features/breadcrumb/hook/useBreadcrumb";
import { selectUserProfile } from "@/features/users/state/usersSelectors";
import "./index.css";
import { PAGE_TITLES } from "@/constants/routes";
interface MenuItem {
  id: string;
  title: string;
  icon: React.ReactNode;
  color: string;
  bgGradient: string;
  href?: string;
  screenCode?: string;
  children?: MenuItem[];
}
const menuItems: MenuItem[] = [
  {
    id: "user-master",
    title: PAGE_TITLES.USER_LIST,
    icon: (
      <Image
        src="/images/home/<USER>"
        alt="user master"
        height={48}
        width={48}
      />
    ),
    color: "text-red-600",
    bgGradient: "from-red-50 to-red-100",
    screenCode: "SUMA01",
    href: "user-master",
  },
  {
    id: "millsheet-parent",
    title: PAGE_TITLES.MILL_SHEET,
    icon: (
      <Image
        src="/images/icons/millsheet-list.svg"
        alt="mill sheet"
        height={48}
        width={48}
      />
    ),
    color: "text-red-600",
    bgGradient: "from-red-50 to-red-100",
    screenCode: "SMIL01",
    children: [
      {
        id: "millsheet-list",
        title: PAGE_TITLES.MILL_SHEET_LIST,
        icon: (
          <Image
            src="/images/icons/millsheet-list.svg"
            alt="mill sheet list"
            height={48}
            width={48}
          />
        ),
        color: "text-red-600",
        bgGradient: "from-red-50 to-red-100",
        screenCode: "SMIL01",
        href: "/millsheet/list",
      },
      {
        id: "millsheet-modification-list",
        title: PAGE_TITLES.MILL_SHEET_EDIT,
        icon: (
          <Image
            src="/images/icons/notes-edit.svg"
            alt="mill sheet edit"
            height={48}
            width={48}
          />
        ),
        color: "text-red-600",
        bgGradient: "from-red-50 to-red-100",
        screenCode: "SMIL04",
        href: "millsheet/modification",
      },
      {
        id: "millsheet-upload",
        title: PAGE_TITLES.MILL_SHEET_UPLOAD,
        icon: (
          <Image
            src="/images/icons/upload.svg"
            alt="mill sheet upload"
            height={48}
            width={48}
          />
        ),
        color: "text-red-600",
        bgGradient: "from-red-50 to-red-100",
        screenCode: "SMIL03",
        href: "millsheet/upload-millsheet",
      },
      {
        id: "erp-data-upload",
        title: PAGE_TITLES.ERP_DATA_UPLOAD,
        icon: (
          <Image
            src="/images/icons/upload.svg"
            alt="erp data upload"
            height={48}
            width={48}
          />
        ),
        color: "text-red-600",
        bgGradient: "from-red-50 to-red-100",
        screenCode: "SMIL02",
        href: "millsheet/upload-erp",
      },
    ],
  },
];

function Dashboard() {
  const userAccessMenus = useSelector(selectUserAccessMenus);
  const userProfile = useSelector(selectUserProfile);
  // Filter menuItems based on user access
  const filteredMenuItems = useMemo(() => {
    const hasAccess = (code?: string) => {
      if (!code) return true;
      if (code === "SMIL02") {
        return userAccessMenus?.some(
          (a) =>
            a.screenCode === code &&
            a.canView === ViewPermission.Granted &&
            userProfile?.organizationCode === "MOT"
        );
      }
      return userAccessMenus?.some(
        (a) => a.screenCode === code && a.canView === ViewPermission.Granted
      );
    };

    const filterRecursive = (items: MenuItem[]): MenuItem[] =>
      items
        .map((item) => {
          if (item.children && item.children.length) {
            const children = filterRecursive(item.children);
            if (!hasAccess(item.screenCode) && children.length === 0)
              return null;
            return { ...item, children };
          }
          return hasAccess(item.screenCode) ? item : null;
        })
        .filter(Boolean) as MenuItem[];

    return filterRecursive(menuItems);
  }, [userAccessMenus, userProfile?.organizationCode]);

  useBreadcrumb({
    pageTitle: PAGE_TITLES.MAIN_MENU,
    items: [{ id: "main-menu", title: PAGE_TITLES.MAIN_MENU }],
  });
  // </AccessGuard>
  // State: null = show parent, 'millsheet-parent' = show children
  const [showChildren, setShowChildren] = useState<null | string>(null);

  // Esc key handler
  const handleEsc = useCallback((e: KeyboardEvent) => {
    if (e.key === "Escape") setShowChildren(null);
  }, []);
  useEffect(() => {
    if (showChildren) {
      window.addEventListener("keydown", handleEsc);
      return () => window.removeEventListener("keydown", handleEsc);
    }
  }, [showChildren, handleEsc]);

  // Find millsheet children for rendering
  const millsheet = filteredMenuItems.find(
    (item) => item.id === "millsheet-parent"
  );

  return (
    <PageContainer
      title={PAGE_TITLES.MAIN_MENU}
      description={"this is " + PAGE_TITLES.MAIN_MENU}
    >
      <GridMenu
        showChildren={showChildren}
        setShowChildren={setShowChildren}
        millsheet={millsheet}
        filteredMenuItems={filteredMenuItems}
      />
    </PageContainer>
  );
}

export default Dashboard;
