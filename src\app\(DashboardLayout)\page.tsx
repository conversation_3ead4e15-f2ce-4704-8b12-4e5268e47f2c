"use client";
import React, { useState, useCallback, useEffect } from "react";
import PageContainer from "@/app/(DashboardLayout)/components/container/PageContainer";
import GridMenu from "@/components/common/GridMenu";
import { useSelector } from "react-redux";
import { selectUserAccessMenus } from "@/features/users/state/usersSelectors";
import Image from "next/image";
// components
import "./index.css";
interface MenuItem {
  id: string;
  title: string;
  icon: React.ReactNode;
  color: string;
  bgGradient: string;
  href?: string;
  screenCode?: string;
  children?: MenuItem[];
}
const menuItems: MenuItem[] = [
  {
    id: "user-master",
    title: "User List",
    icon: (
      <Image
        src="/images/home/<USER>"
        alt="user master"
        height={48}
        width={48}
      />
    ),
    color: "text-red-600",
    bgGradient: "from-red-50 to-red-100",
    screenCode: "SUMA01",
    href: "user-master",
  },
  {
    id: "millsheet-parent",
    title: "Mill Sheet",
    icon: (
      <Image
        src="/images/icons/millsheet-list.svg"
        alt="mill sheet"
        height={48}
        width={48}
      />
    ),
    color: "text-red-600",
    bgGradient: "from-red-50 to-red-100",
    screenCode: "SMIL01",
    children: [
      {
        id: "millsheet-list",
        title: "Mill Sheet List",
        icon: (
          <Image
            src="/images/icons/millsheet-list.svg"
            alt="mill sheet list"
            height={48}
            width={48}
          />
        ),
        color: "text-red-600",
        bgGradient: "from-red-50 to-red-100",
        screenCode: "SMIL01",
        href: "/millsheet",
      },
      {
        id: "millsheet-modification-list",
        title: "Mill Sheet Edit",
        icon: (
          <Image
            src="/images/icons/notes-edit.svg"
            alt="Mill Sheet Edit"
            height={48}
            width={48}
          />
        ),
        color: "text-red-600",
        bgGradient: "from-red-50 to-red-100",
        screenCode: "SMIL03",
        href: "millsheet/modification",
      },
      {
        id: "millsheet-upload",
        title: "Mill Sheet Upload",
        icon: (
          <Image
            src="/images/icons/upload.svg"
            alt="mill sheet upload"
            height={48}
            width={48}
          />
        ),
        color: "text-red-600",
        bgGradient: "from-red-50 to-red-100",
        screenCode: "SMIL02",
        href: "millsheet/upload-pdf",
      },
      {
        id: "erp-data-upload",
        title: "ERP Data Upload",
        icon: (
          <Image
            src="/images/icons/upload.svg"
            alt="erp data upload"
            height={48}
            width={48}
          />
        ),
        color: "text-red-600",
        bgGradient: "from-red-50 to-red-100",
        screenCode: "SMIL03",
        href: "millsheet/upload-erp",
      },
    ],
  },
];
import { Card, CardContent, Typography } from "@mui/material";
import { useBreadcrumb } from "@/features/breadcrumb/hook/useBreadcrumb";

function Dashboard() {
  const userAccessMenus = useSelector(selectUserAccessMenus);
  // Filter menuItems based on user access
  const filteredMenuItems = menuItems.filter((item) => {
    if (!item.screenCode) return true;
    const found = userAccessMenus?.find(
      (a) => a.screenCode === item.screenCode && a.canView === 1
    );
    return !!found;
  });

  useBreadcrumb({
    pageTitle: 'Main Menu',
    items: [
      { id: 'main-menu', title: 'Main Menu' },
    ],
  })
    // </AccessGuard>
  // State: null = show parent, 'millsheet-parent' = show children
  const [showChildren, setShowChildren] = useState<null | string>(null);

  // Esc key handler
  const handleEsc = useCallback((e: KeyboardEvent) => {
    if (e.key === "Escape") setShowChildren(null);
  }, []);
  useEffect(() => {
    if (showChildren) {
      window.addEventListener("keydown", handleEsc);
      return () => window.removeEventListener("keydown", handleEsc);
    }
  }, [showChildren, handleEsc]);

  // Find millsheet children for rendering
  const millsheet = filteredMenuItems.find(
    (item) => item.id === "millsheet-parent"
  );

  return (
    <PageContainer title="Main Menu" description="this is main menu">
      <GridMenu
        showChildren={showChildren}
        setShowChildren={setShowChildren}
        millsheet={millsheet}
        filteredMenuItems={filteredMenuItems}
      />
    </PageContainer>
  );
}

export default Dashboard;
