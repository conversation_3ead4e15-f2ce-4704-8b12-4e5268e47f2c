import axiosInstance from "@/lib/axiosInstance";

type ApiResponse<T> = {
  success: boolean;
  code: string;
  message: string;
  params: any[];
  data: T;
};

export type UploadFileResponse = {
  code: string;
  error: any[];
  data: any[];
  message: string;
  success: boolean;
};

export type FileUpload = {
  fileId: string;
  fileName: string;
  fileUrl?: string;
  filePath: string;
  fileSize: number;
  scanVirus?: boolean;
}

export type  UploadFiles = {
  userId: string | null;
  customerId: string | null;
  jobType: string;
  jobName: string;
  files: FileUpload[]; // or a more specific type if `convertFilesList` is not a standard File[]
  organizationCode: string;
};


interface UploadResponse {
  // Define the expected structure of the response here
  success: boolean;
  message?: string;
  data?: any;
  code: string;
  error: any[];
}



async function apiRequest<T>(promise: Promise<any>): Promise<T> {
  const response = await promise;
  const apiResponse: ApiResponse<T> = response.data;
  if (!apiResponse.success) {
    throw new Error(apiResponse.message || "API Error");
  }
 return apiResponse.data;
  
}

export const uploadMillSheet = async (fileUpload: FormData): Promise<UploadFileResponse> => {
  return await(apiRequest<UploadFileResponse>(axiosInstance.post("/mill-sheets/check", fileUpload, { headers: {"Content-Type": 'multipart/form-data'} })))
}

export const uploadERPData = async (fileUpload: FormData): Promise<UploadFileResponse> => {
  return await(apiRequest<UploadFileResponse>(axiosInstance.post("/mill-sheets/check/erp", fileUpload, { headers: {"Content-Type": 'multipart/form-data'} })))
}


export const uploadAllFileMillSheet = async (fileUpload: UploadFiles): Promise<UploadResponse> => {
  const response = await axiosInstance.post<UploadResponse>("/mill-sheets/trigger-container-job-app", fileUpload)
  return response.data;
}


