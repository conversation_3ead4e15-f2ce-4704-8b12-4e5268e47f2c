import React, { useState, useEffect, useRef } from 'react';
import { Bell, X, Check, Info, AlertCircle } from 'lucide-react';
import * as signalR from '@microsoft/signalr'

// Add global type for window.signalR
declare global {
  interface Window {
    signalR: typeof signalR;
  }
}

const NotificationApp = () => {
  const [notifications, setNotifications] = useState<any[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const connectionRef = useRef<signalR.HubConnection | null>(null);
  const userId = '27c1b4dc-0940-40b2-95b2-d527de54148c'; // In real app, get from auth

  useEffect(() => {
    connectToSignalR();
    return () => {
      if (connectionRef.current) {
        connectionRef.current.stop();
      }
    };
  }, []);

  const connectToSignalR = async () => {
    try {
      // Check if SignalR is loaded via CDN
      if (typeof window.signalR === 'undefined') {
        console.error('SignalR library not found. Please add the CDN script.');
        setIsConnected(false);
        return;
      }

      // Get negotiation info from backend
      const negotiateResponse = await fetch(`http://localhost:3001/api/signalr/negotiate?userId=${userId}`);
      const negotiateData = await negotiateResponse.json();

      // Create connection using global signalR
      const connection = new window.signalR.HubConnectionBuilder()
      .withUrl(negotiateData.url, {
        accessTokenFactory: () => negotiateData.accessToken
      })
      .withAutomaticReconnect()
      .build();

      // Setup event handlers
      connection.on('ReceiveNotification', (notification) => {
        console.log('Received notification:', notification);
        setNotifications(prev => [notification, ...prev]);
        setUnreadCount(prev => prev + 1);
        
        // Show browser notification if permission granted
        if (Notification.permission === 'granted') {
          new Notification(notification.message, {
            icon: '/favicon.ico'
          });
        }
      });

      connection.onclose((error) => {
        setIsConnected(false);
        console.log('SignalR connection closed:', error);
      });

      connection.onreconnected(() => {
        setIsConnected(true);
        console.log('SignalR reconnected');
      });

      // Start connection
      await connection.start();
      connectionRef.current = connection;
      setIsConnected(true);
      console.log('SignalR connected successfully');

      // Request notification permission
      if (Notification.permission === 'default') {
        Notification.requestPermission();
      }

    } catch (error) {
      console.error('SignalR connection error:', error);
      setIsConnected(false);
    }
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
    setUnreadCount(0);
  };

  const removeNotification = (notificationId: string) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
    const notification = notifications.find(n => n.id === notificationId);
    if (notification && !notification.read) {
      setUnreadCount(prev => Math.max(0, prev - 1));
    }
  };

  const testNotification = async (type: string) => {
    try {
      const endpoint = type === 'order' ? 'order-completed' : 'new-message';
      await fetch(`http://localhost:3001/api/test/${endpoint}?userId=${userId}`);
    } catch (error) {
      console.error('Test notification error:', error);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return <Check className="text-green-500" size={16} />;
      case 'error': return <AlertCircle className="text-red-500" size={16} />;
      default: return <Info className="text-blue-500" size={16} />;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success': return 'border-l-green-500 bg-green-50';
      case 'error': return 'border-l-red-500 bg-red-50';
      default: return 'border-l-blue-500 bg-blue-50';
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      
      {/* Header */}
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Azure SignalR Demo</h1>
          
          {/* Connection Status */}
          <div className="flex items-center gap-4">
            <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm ${
              isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              {isConnected ? 'Connected' : 'Disconnected'}
            </div>

            {/* Notification Bell */}
            <div className="relative">
              <button
                onClick={() => setShowNotifications(!showNotifications)}
                className="relative p-2 bg-white rounded-full shadow-md hover:shadow-lg transition-shadow"
              >
                <Bell size={24} className="text-gray-700" />
                {unreadCount > 0 && (
                  <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center">
                    {unreadCount > 9 ? '9+' : unreadCount}
                  </div>
                )}
              </button>

              {/* Notifications Dropdown */}
              {showNotifications && (
                <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-xl border z-50 max-h-96 overflow-y-auto">
                  <div className="p-4 border-b flex items-center justify-between">
                    <h3 className="font-semibold text-gray-900">Notifications</h3>
                    {unreadCount > 0 && (
                      <button
                        onClick={markAllAsRead}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        Mark all as read
                      </button>
                    )}
                  </div>

                  <div className="max-h-64 overflow-y-auto">
                    {notifications.length === 0 ? (
                      <div className="p-4 text-center text-gray-500">
                        No notifications yet
                      </div>
                    ) : (
                      notifications.map((notification) => (
                        <div
                          key={notification.id}
                          className={`p-4 border-b border-l-4 relative ${getNotificationColor(notification.type)} ${
                            !notification.read ? 'bg-opacity-100' : 'bg-opacity-50'
                          }`}
                        >
                          <div className="flex items-start gap-3">
                            {getNotificationIcon(notification.type)}
                            <div className="flex-1">
                              <p className={`text-sm ${!notification.read ? 'font-semibold' : ''}`}>
                                {notification.message}
                              </p>
                              <p className="text-xs text-gray-500 mt-1">
                                {new Date(notification.timestamp).toLocaleString()}
                              </p>
                            </div>
                            <div className="flex gap-1">
                              {!notification.read && (
                                <button
                                  onClick={() => markAsRead(notification.id)}
                                  className="text-blue-500 hover:text-blue-700 text-xs"
                                  title="Mark as read"
                                >
                                  ✓
                                </button>
                              )}
                              <button
                                onClick={() => removeNotification(notification.id)}
                                className="text-gray-400 hover:text-red-500"
                                title="Remove"
                              >
                                <X size={14} />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Test Buttons */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Test Notifications</h2>
          <div className="flex gap-4 flex-wrap">
            <button
              onClick={() => testNotification('order')}
              className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
              disabled={!isConnected}
            >
              Test Order Completed
            </button>
            <button
              onClick={() => testNotification('message')}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              disabled={!isConnected}
            >
              Test New Message
            </button>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Setup Instructions</h2>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-3 rounded">
              <p className="font-semibold text-yellow-800 mb-2">⚠️ Important: Add SignalR CDN to your HTML:</p>
              {/* <code className="bg-gray-100 px-2 py-1 rounded block text-xs">
                &lt;script src="https://unpkg.com/@microsoft/signalr@latest/dist/browser/signalr.min.js"&gt;&lt;/script&gt;
              </code> */}
            </div>
            <p>1. Start backend server: <code className="bg-gray-100 px-2 py-1 rounded">node server.js</code></p>
            <p>2. Add the SignalR script tag above to your index.html</p>
            <p>3. User ID: <span className="font-mono bg-blue-100 px-2 py-1 rounded">{userId}</span></p>
            <p>4. Connection Status: <span className={isConnected ? 'text-green-600 font-semibold' : 'text-red-600 font-semibold'}>{isConnected ? '✅ Connected' : '❌ Disconnected'}</span></p>
            <p>5. Click test buttons above to trigger notifications</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationApp;