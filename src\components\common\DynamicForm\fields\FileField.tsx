import React from 'react';
import { Controller } from 'react-hook-form';
import Mu<PERSON><PERSON><PERSON><PERSON><PERSON>ield from '@mui/material/TextField';
import { FieldConfig } from '../DynamicForm.types';

interface Props {
  field: FieldConfig;
  control: any;
  error?: string;
  disabled?: boolean;
  i18n?: (key: string) => string;
}

const FileField: React.FC<Props> = ({ field, control, error, disabled, i18n }) => (
  <Controller
    name={field.name}
    control={control}
    render={({ field: controllerField }) => (
      <MuiTextField
        type="file"
        label={i18n ? i18n(field.label) : field.label}
        error={!!error}
        helperText={error || field.helperText}
        fullWidth
        disabled={disabled || field.disabled}
        sx={field.style}
        className={field.className}
        onChange={(e) => {
          const file = (e.target as HTMLInputElement).files?.[0];
          controllerField.onChange(file);
        }}
      />
    )}
  />
);

export default FileField;
