import React, { RefObject } from "react";
import { Typo<PERSON>, Box, Button } from "@mui/material";
import Image from "next/image";
import { styled } from "@mui/system";

const StyledIconButton = styled(Box)(({}) => ({
  backgroundColor: "#F9FAFB", // light gray background
  borderRadius: "50%", // make it circular
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  margin: " 0 auto",
  height: 64,
  width: 64,
  color: "#9CA3AF",
}));

const UploadIcon = () => (
  <Image
    src="/images/millsheet/upload.svg"
    alt="icon upload"
    width={32}
    height={32}
  />
);

interface UploadAreaDetailProps {
  handleDragOver: (data?: any) => void;
  handleDragLeave: (data?: any) => void;
  fileInputRef?: RefObject<HTMLInputElement | null>;
  dragOver: boolean;
  handleDrop: (data: any) => void | Promise<any>;
  handleFileInput: (data: any) => void | Promise<any>;
  type: string;
}

const UploadArea: React.FC<UploadAreaDetailProps> = ({
  handleDragOver,
  handleDragLeave,
  dragOver,
  handleDrop,
  handleFileInput,
  fileInputRef,
  type,
}) => {
  return (
    <>
      <Box
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        sx={{
          border: dragOver ? "1px dashed #2563EB" : "1px dashed #ccc",
          borderRadius: "12px",
          padding: 4,
          textAlign: "center",
          backgroundColor: "#FFFFFF",
          width: "100%",
          maxWidth: "740px",
          maxHeight: 262,
          margin: "0 auto",
        }}
      >
        <StyledIconButton>
          <UploadIcon />
        </StyledIconButton>
        <Typography
          variant="h3"
          sx={{
            marginTop: 1,
            fontWeight: 600,
            fontStyle: "normal", // 'Semibold' is not a valid value; use 'normal' and adjust weight
            lineHeight: "28px",
            letterSpacing: "0%",
            textAlign: "center",
            verticalAlign: "middle",
          }}
        >
          {type !== "erp-data"
            ? "Drag & Drop your Mill Sheet files here"
            : "Drag & Drop your ERP Data files here"}
        </Typography>
        <Typography
          variant="body1"
          sx={{
            marginBottom: 2,
            fontWeight: 400,
            fontStyle: "normal", // 'Regular' is not a valid CSS value; use 'normal'
            fontSize: "16px",
            lineHeight: "100%", // or '1' if you prefer unitless
            letterSpacing: "0px", // '0%' is not standard; use '0px'
            textAlign: "center",
            verticalAlign: "middle",
          }}
        >
          or click to browse your files
        </Typography>
        <Button
          variant="contained"
          sx={{
            height: "48px",
            backgroundColor: "#2563EB",
            fontWeight: 600,
            fontStyle: "normal", // 'Semibold' is not valid; use 'normal' with weight 600
            fontSize: "16px",
            lineHeight: "24px",
            letterSpacing: "0px", // '0%' is not standard; use '0px'
            textAlign: "center",
            verticalAlign: "middle",
          }}
          component="label"
        >
          Browse Files
          <input
            type="file"
            id="files"
            hidden
            multiple
            accept={type !== "erp-data" ? ".pdf, .xmp, .csv" : ".xlsx"}
            ref={fileInputRef}
            onChange={handleFileInput}
          />
        </Button>
      </Box>
    </>
  );
};

export default UploadArea;
