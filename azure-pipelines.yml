# Node.js with React
# Build a Node.js project that uses React.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

pr: 
  branches: 
    include: 
      - main 
trigger: 
  branches: 
    include: 
      - main 

pool:
  vmImage: ubuntu-latest

jobs: 
- job: build_and_deploy_job 
  displayName: Build and Deploy Job 
  condition: or(eq(variables['Build.Reason'], 'Manual'),or(eq(variables['Build.Reason'], 'PullRequest'),eq(variables['Build.Reason'], 'IndividualCI'))) 
  pool: 
    vmImage: ubuntu-latest 
  variables: 
  - group: Azure-Static-Web-Apps-witty-moss-06e108900-variable-group
  - name: APP_ENV
    value: development
  # - name: NODE_VERSION
  #   value: 20.19.3
  steps: 
  - checkout: self 
    submodules: true 
  - task: NodeTool@0
    inputs:
      versionSpec: '20.x'
    displayName: 'Install Node.js'

  - script: |
      rm -f .env.production
      npm install
      npm run build:dev
      echo "=== Checking built files ==="
      ls -la
    displayName: 'npm install and build for development'
    # condition: eq(variables['Build.SourceBranch'], 'refs/heads/main')

  - task: AzureStaticWebApp@0 
    # env:
      # WEBSITE_NODE_DEFAULT_VERSION: $(NODE_VERSION)
    inputs: 
      azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_WITTY_MOSS_06E108900) 
###### Repository/Build Configurations - These values can be configured to match your app requirements. ###### 
# For more information regarding Static Web App workflow configurations, please visit: https://aka.ms/swaworkflowconfig 
      app_location: "out" # App source code path 
      api_location: "" # Api source code path - optional 
      output_location: "" # Built app content directory - optional 
      skip_app_build: true  # This tells Oryx to skip building
      # app_build_command: 'npm run build:dev'  # or 'npm run build:prod'
###### End of Repository/Build Configurations ###### 
