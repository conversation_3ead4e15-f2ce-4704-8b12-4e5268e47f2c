import React, { useState } from "react";
import { Box, IconButton, InputAdornment, InputLabel, Tooltip } from "@mui/material";
import { Controller } from "react-hook-form";
import MuiTextField from "@mui/material/TextField";
import { FieldConfig } from "../DynamicForm.types";
import MuiErrorCustomStyles from "@/utils/theme/muiErrorStyles";
import { InfoOutlined, Visibility, VisibilityOff } from "@mui/icons-material";

interface Props {
  field: FieldConfig;
  control: any;
  error?: string;
  disabled?: boolean;
  i18n?: (key: string) => string;
}

const TextField: React.FC<Props> = ({ field, control, error, disabled, i18n }) => {
  const [showPassword, setShowPassword] = useState(false);
  const togglePassword = () => setShowPassword((prev) => !prev);
  const isPassword = field.type === "password";
  const prefixIcon = field.prefixIcon;
  const suffixIcon = field.suffixIcon;
  const endAdornment = isPassword ? (
    <InputAdornment position="end">
      <IconButton onClick={togglePassword} edge="end">
        {showPassword ? <VisibilityOff /> : <Visibility />}
      </IconButton>
    </InputAdornment>
  ) : prefixIcon ? (
    <InputAdornment position="end">{prefixIcon}</InputAdornment>
  ) :undefined;

  const startAdornment = suffixIcon ? <InputAdornment position="start">{suffixIcon}</InputAdornment> : undefined;

    const labelText = i18n ? i18n(field.label) : field.label;
  const requiredAsterisk = field.required ? <span style={{ color: "#CA002E", marginLeft: 4 }}>*</span> : null;

  const labelWithTooltip = (
    <span style={{ display: "inline-flex", alignItems: "center", gap: 6 }}>
      <span>{labelText}{requiredAsterisk}</span>
      {field.labelTooltipContent ? (
        <Tooltip
          title={
            typeof field.labelTooltipContent === "string"
              ? (i18n ? i18n(field.labelTooltipContent) : field.labelTooltipContent)
              : field.labelTooltipContent
          }
          {...field.labelTooltipProps}
        >
          <span
            // dùng span để không ảnh hưởng layout InputLabel
            style={{ display: "inline-flex", color: "rgba(0,0,0,0.54)", cursor: "help" }}
            onMouseDown={(e) => e.preventDefault()} // tránh gây blur/focus khi click
          >
            {field.labelTooltipIcon ?? <InfoOutlined fontSize="small" />}
          </span>
        </Tooltip>
      ) : null}
    </span>
  );

  return (
    <Controller
      name={field.name}
      control={control}
      render={({ field: controllerField }) =>
        field.hiddenLabelNormal ? (
          <Box sx={{ position: "relative", width: "100%" }}>
            <Box
              sx={{
                position: "absolute",
                left: 0,
                right: 0,
                top: 2,
                color: "#222222",
                fontFamily: "Roboto",
                fontWeight: 400,
                fontSize: 14,
                lineHeight: 1.5,
                zIndex: 2,
              }}
            >
              {labelWithTooltip}
            </Box>
            <MuiTextField
              {...controllerField}
              value={controllerField.value ?? ""}
              label=""
              placeholder={field.placeholder}
              type={isPassword ? (showPassword ? "text" : "password") : field.type}
              error={!!error}
              helperText={error || field.helperText}
              fullWidth
              disabled={disabled || field.disabled}
              multiline={field.multiline}
              rows={field.rows}
              sx={{
                ...field.style,
                ...MuiErrorCustomStyles,
                mt: 3,
                "& .MuiInputBase-input.Mui-disabled": {
                  WebkitTextFillColor: (field as any).disabledValueColor || "#111827",
                  color: (field as any).disabledValueColor || "#111827",
                },
              }}
              className={field.className}
              slotProps={{
                input: {
                  startAdornment,
                  endAdornment,
                },
              }}
            />
          </Box>
        ) : (
          <MuiTextField
            {...controllerField}
            value={controllerField.value ?? ""}
            label={labelWithTooltip}
            placeholder={field.placeholder}
            type={isPassword ? (showPassword ? "text" : "password") : field.type}
            error={!!error}
            helperText={error || field.helperText}
            fullWidth
            disabled={disabled || field.disabled}
            multiline={field.multiline}
            rows={field.rows}
            sx={{
              ...field.style,
              ...MuiErrorCustomStyles,
              "& .MuiInputBase-input.Mui-disabled": {
                WebkitTextFillColor: (field as any).disabledValueColor || "#111827",
                color: (field as any).disabledValueColor || "#111827",
              },
            }}
            slotProps={{
              inputLabel: {
                sx: {
                  "&.MuiInputLabel-shrink": {
                    fontSize: 18,
                    pr: 1,
                    backgroundColor: "rgba(255, 255, 255, 1)",
                  },
                },
              },
              input: {
                startAdornment,
                endAdornment,
              },
            }}
            className={field.className}
          />
        )
      }
    />
  );
};
export default TextField;
