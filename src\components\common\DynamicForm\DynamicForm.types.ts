import { TooltipProps } from "@mui/material";
import { ReactNode } from "react";
import { RegisterOptions, FieldValues, UseFormReturn } from "react-hook-form";

export type FieldType =
  | "text"
  | "email"
  | "password"
  | "number"
  | "select"
  | "checkbox"
  | "radio"
  | "date"
  | "textarea"
  | "file"
  | "autocomplete"
  | "dateRange"
  | "selectMultiple"
  | "textRange"

export interface FieldOption {
  label: string;
  value: string | number | boolean;
}

export interface FieldConfig {
  name: string;
  label: string;
  type: FieldType;
  options?: FieldOption[];
  placeholder?: string;
  defaultValue?: any;
  validation?: RegisterOptions;
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  customValidation?: (
    value: any,
    values?: FieldValues
  ) => string | boolean | undefined;
  showIf?: (values: FieldValues) => boolean;
  style?: object;
  className?: string;
  disabled?: boolean;
  multiline?: boolean;
  rows?: number;
  helperText?: ReactNode;
  i18nKey?: string;
  hiddenLabelNormal?: boolean;
  prefixIcon?: React.ReactNode;
  suffixIcon?: React.ReactNode;
  disableFuture?: boolean;
  labelTooltipContent?: ReactNode | string;
  labelTooltipProps?: Partial<TooltipProps>;
  labelTooltipIcon?: ReactNode;
  isNumberic?: boolean;
}

export interface DynamicFormProps {
  fields?: FieldConfig[];
  onCancel?: (data?: any) => void;
  onSubmit: (data: any) => void | Promise<void>;
  defaultValues?: Record<string, any>;
  loading?: boolean;
  error?: string | null;
  errors?: {}
  submitLabel?: string;
  formClassName?: string;
  fieldClassName?: string;
  i18n?: (key: string) => string;
  children?: ReactNode;
}
