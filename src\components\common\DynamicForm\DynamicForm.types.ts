import { TooltipProps } from "@mui/material";
import { ReactNode } from "react";
import {
  RegisterOptions,
  FieldValues,
  UseFormReturn,
  Path,
  UseFormSetValue,
  UseFormGetValues,
  UseFormClearErrors,
  UseFormResetField,
  UseFormReset,
} from "react-hook-form";

export type FieldType =
  | "text"
  | "email"
  | "password"
  | "number"
  | "select"
  | "checkbox"
  | "radio"
  | "date"
  | "textarea"
  | "file"
  | "autocomplete"
  | "dateRange"
  | "selectMultiple"
  | "textRange"
  | "textDecimal";
export interface FieldOption {
  label: string;
  value: string | number | boolean;
}

export interface FieldConfig {
  name: string;
  label: string;
  type: FieldType;
  options?: FieldOption[];
  placeholder?: string;
  defaultValue?: any;
  validation?: RegisterOptions;
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  customValidation?: (value: any, values?: FieldValues) => string | boolean | undefined;
  showIf?: (values: FieldValues) => boolean;
  style?: object;
  className?: string;
  disabled?: boolean;
  multiline?: boolean;
  rows?: number;
  helperText?: ReactNode;
  i18nKey?: string;
  hiddenLabelNormal?: boolean;
  prefixIcon?: React.ReactNode;
  suffixIcon?: React.ReactNode;
  disableFuture?: boolean;
  labelTooltipContent?: ReactNode | string;
  labelTooltipProps?: Partial<TooltipProps>;
  labelTooltipIcon?: ReactNode;
  isNumberic?: boolean;
  allowDecimal?: boolean;
  maxLength?: number;
  maxIntegerDigits?: number;
  maxDecimalDigits?: number;
  onValueChange?: (args: {
    name?: string;
    value: any;
    values: any;
    setValue: (name: string, value: any) => void;
    getValues: () => any;
    clearErrors: (name?: string) => void;
    resetField: (name: string, defaultValue?: any) => void;
    reset: (values?: any) => void;
  }) => void;
}

export interface OnValuesChangeArgs<TFieldValues extends FieldValues = FieldValues> {
  name?: Path<TFieldValues>;
  values: TFieldValues;
  prevValues: TFieldValues;
  setValue: UseFormSetValue<TFieldValues>;
  getValues: UseFormGetValues<TFieldValues>;
  clearErrors: UseFormClearErrors<TFieldValues>;
  resetField: UseFormResetField<TFieldValues>;
  reset: UseFormReset<TFieldValues>;
}

export interface DynamicFormProps<TFieldValues extends FieldValues = FieldValues> {
  fields?: FieldConfig[];
  onCancel?: (data?: any) => void;
  onSubmit: (data: any) => void | Promise<void>;
  defaultValues?: Record<string, any>;
  loading?: boolean;
  error?: string | null;
  errors?: {};
  submitLabel?: string;
  formClassName?: string;
  fieldClassName?: string;
  i18n?: (key: string) => string;
  children?: ReactNode;
  onValuesChange?: (args: OnValuesChangeArgs<TFieldValues>) => void;
}
