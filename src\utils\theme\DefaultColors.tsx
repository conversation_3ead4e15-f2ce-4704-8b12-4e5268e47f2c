import { createTheme } from "@mui/material/styles";

const baseLightTheme = createTheme({
  direction: "ltr",
  palette: {
    primary: {
      main: "#CA002E",
      light: "#ECF2FF",
      dark: "#A20025",
    },
    secondary: {
      main: "#49BEFF",
      light: "#E8F7FF",
      dark: "#23afdb",
    },
    success: {
      main: "#13DEB9",
      light: "#E6FFFA",
      dark: "#02b3a9",
      contrastText: "#ffffff",
    },
    info: {
      main: "#539BFF",
      light: "#EBF3FE",
      dark: "#1682d4",
      contrastText: "#ffffff",
    },
    error: {
      main: "#FA896B",
      light: "#FDEDE8",
      dark: "#f3704d",
      contrastText: "#ffffff",
    },
    warning: {
      main: "#FFAE1F",
      light: "#FEF5E5",
      dark: "#ae8e59",
      contrastText: "#ffffff",
    },
    grey: {
      100: "#F5F6FA",
      200: "#ECEFF4",
      300: "#E1E4EA",
      400: "#D1D5DB",
      500: "#BFC4CC",
      600: "#A3A8B1",
    },
    text: {
      primary: "#2A3547",
      secondary: "#5A6A85",
    },
    action: {
      disabledBackground: "rgba(73,82,88,0.12)",
      hoverOpacity: 0.02,
      hover: "#f6f9fc",
    },
    background: {
      paper: '#FAFAFA'
    },
    divider: "#e5eaef",
  },
  shape: {
    borderRadius: '10px'
  },
  typography: {
    fontFamily: "Roboto, Helvetica, Arial, sans-serif",
    h1: {
      fontWeight: "bold",
      fontSize: "32px",
      lineHeight: "120%",
      fontFamily: "Roboto, Helvetica, Arial, sans-serif",
    },
    h2: {
      fontWeight: 600,
      fontSize: "24px",
      lineHeight: "32px",
      fontFamily: "Roboto, Helvetica, Arial, sans-serif",
    },
    h3: {
      fontWeight: 600,
      fontSize: "1.5rem",
      lineHeight: "1.75rem",
      fontFamily: "Roboto, Helvetica, Arial, sans-serif",
    },
    h4: {
      fontWeight: 600,
      fontSize: "1.3125rem",
      lineHeight: "1.6rem",
    },
    h5: {
      fontWeight: 600,
      fontSize: "1.125rem",
      lineHeight: "1.6rem",
    },
    h6: {
      fontWeight: 600,
      fontSize: "1rem",
      lineHeight: "1.2rem",
    },
    button: {
      textTransform: "capitalize",
      fontWeight: 400,
    },
    body1: {
      fontSize: "0.875rem",
      fontWeight: 400,
      lineHeight: "1.334rem",
    },
    body2: {
      fontSize: "0.75rem",
      letterSpacing: "0rem",
      fontWeight: 400,
      lineHeight: "1rem",
    },
    subtitle1: {
      fontSize: "0.875rem",
      fontWeight: 400,
    },
    subtitle2: {
      fontSize: "0.875rem",
      fontWeight: 400,
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        ".MuiPaper-elevation9, .MuiPopover-root .MuiPaper-elevation": {
          boxShadow:
            "rgb(145 158 171 / 30%) 0px 0px 2px 0px, rgb(145 158 171 / 12%) 0px 12px 24px -4px !important",
        },

        '.MuiPaginationItem-root':{
          '&.Mui-selected': {
             backgroundColor: "#ca002e !important"
          },
          '&:hover': {
            backgroundColor: "#ca0432ff !important",
            color: '#fff'
          }
        }
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: "7px",
        },
      },
    },


  },
});

export { baseLightTheme };

