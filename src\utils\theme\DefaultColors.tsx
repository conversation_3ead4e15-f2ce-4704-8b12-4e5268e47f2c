import { createTheme } from "@mui/material/styles";

const baseLightTheme = createTheme({
  direction: "ltr",
  palette: {
    primary: {
      main: "#CA002E",
      light: "#ECF2FF",
      dark: "#A20025",
    },
    secondary: {
      main: "#49BEFF",
      light: "#E8F7FF",
      dark: "#23afdb",
    },
    success: {
      main: "#00c951",
      light: "#e5faee",
      dark: "#02b3a9",
      contrastText: "#c3f3d6",
    },
    info: {
      main: "#00a6f4",
      light: "#e5f6fe",
      dark: "#1682d4",
      contrastText: "#c3eafd",
    },
    error: {
      main: "#fb2c36",
      light: "#fee9ea",
      dark: "#f3704d",
      contrastText: "#fecdcf",
    },
    warning: {
      main: "#fe9a00",
      light: "#fff5e5",
      dark: "#ae8e59",
      contrastText: "#ffe7c3",
    },
    customGrey: {
      main: "#535353",
      light: "#525252",
    },
    grey: {
      100: "#F5F6FA",
      200: "#ECEFF4",
      300: "#E1E4EA",
      400: "#D1D5DB",
      500: "#BFC4CC",
      600: "#A3A8B1",
    },
    text: {
      primary: "#2A3547",
      secondary: "#5A6A85",
    },
    action: {
      disabledBackground: "rgba(73,82,88,0.12)",
      hoverOpacity: 0.02,
      hover: "#f6f9fc",
    },
    background: {
      paper: "#FAFAFA",
    },
    divider: "#e5eaef",
  },
  shape: {
    borderRadius: "10px",
  },
  typography: {
    fontFamily: "Roboto, Helvetica, Arial, sans-serif",
    h1: {
      fontWeight: "bold",
      fontSize: "32px",
      lineHeight: "120%",
      fontFamily: "Roboto, Helvetica, Arial, sans-serif",
    },
    h2: {
      fontWeight: 600,
      fontSize: "24px",
      lineHeight: "32px",
      fontFamily: "Roboto, Helvetica, Arial, sans-serif",
    },
    h3: {
      fontWeight: 400,
      fontSize: "18px",
      lineHeight: "130%",
      fontFamily: "Roboto, Helvetica, Arial, sans-serif",
    },
    h4: {
      fontWeight: 600,
      fontSize: "1.3125rem",
      lineHeight: "1.6rem",
    },
    h5: {
      fontWeight: 600,
      fontSize: "1.125rem",
      lineHeight: "1.6rem",
    },
    h6: {
      fontWeight: 600,
      fontSize: "1rem",
      lineHeight: "1.2rem",
    },
    button: {
      textTransform: "capitalize",
      fontWeight: 400,
    },
    body1: {
      fontSize: "0.875rem",
      fontWeight: 400,
      lineHeight: "1.334rem",
    },
    body2: {
      fontSize: "14px",
      fontWeight: 400,
      lineHeight: "150%",
    },
    subtitle1: {
      fontSize: "0.875rem",
      fontWeight: 400,
    },
    subtitle2: {
      fontSize: "14px",
      fontWeight: 700,
    },
    caption: {
      fontSize: "14px",
      lineHeight: "150%",
    },
    overline: {
      fontSize: "12px",
      lineHeight: "120%",
      fontWeight: 400,
      textTransform: "none",
      // fontStyle: "normal",
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        ".MuiPaper-elevation9, .MuiPopover-root .MuiPaper-elevation": {
          boxShadow:
            "rgb(145 158 171 / 30%) 0px 0px 2px 0px, rgb(145 158 171 / 12%) 0px 12px 24px -4px !important",
        },
        ".MuiButton-containedPrimary.Mui-disabled": {
          backgroundColor: "rgba(163, 168, 177, 1) !important",
          color: "rgba(255, 255, 255, 1) !important",
        },
        ".MuiPaginationItem-root": {
          "&.Mui-selected": {
            backgroundColor: "#ca002e !important",
          },
          "&:hover": {
            backgroundColor: "#ca0432ff !important",
            color: "#fff",
          },
        },
        ".controlNormal": {
          ".Mui-disabled": {
            backgroundColor: "#f3f4f6 !important",
            borderRadius: "10px !important",
          },
          ".Mui-disabled .MuiOutlinedInput-notchedOutline, .Mui-disabled fieldset":
            {
              borderStyle: "dashed !important",
              borderColor: "#bdbdbd !important",
              borderRadius: "10px !important",
            },
          ".Mui-disabled .MuiInputBase-input": {
            color: "#111827 !important",
            WebkitTextFillColor: "#111827 !important",
          },
          ".MuiInputLabel-shrink": {
            fontSize: "18px",
            background: "unset !important",
          },
          ".MuiOutlinedInput-notchedOutline legend": {
            fontSize: "14px",
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: "7px",
        },
      },
    },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          "&.Mui-selected": {
            backgroundColor: "#E1E4EA",
          },
          "&.Mui-selected:hover": {
            backgroundColor: "#ECEFF4",
          },
          "&:hover": {
            backgroundColor: "#ECEFF4",
          },
        },
      },
    },
    MuiAutocomplete: {
      styleOverrides: {
        listbox: {
          "& .MuiAutocomplete-option": {
            "&.Mui-focused": {
              backgroundColor: "#ECEFF4",
            },
            "&:hover": {
              backgroundColor: "#ECEFF4",
            },
            '&[aria-selected="true"]': {
              backgroundColor: "#E1E4EA",
            },
            '&[aria-selected="true"]:hover': {
              backgroundColor: "#ECEFF4",
            },
            '&[aria-selected="true"].Mui-focused': {
              backgroundColor: "#E1E4EA",
            },
            '&[aria-selected="true"].Mui-focused:hover': {
              backgroundColor: "#ECEFF4",
            },
          },
        },
      },
    },
  },
});

export { baseLightTheme };
