"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Box,
  AppBar,
  Toolbar,
  styled,
  Stack,
  IconButton,
  Badge,
  Button,
} from "@mui/material";
import PropTypes from "prop-types";
import Link from "next/link";
import Script from "next/script";
import Image from "next/image";
// components
import Profile from "./Profile";
import { IconBellRinging, IconMenu } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";
import Notification from "./notification";

interface ItemType {
  toggleMobileSidebar: (event: React.MouseEvent<HTMLElement>) => void;
}
const languages = [
  { code: "en", label: "English", flag: "🇬🇧" },
  { code: "th", label: "ไทย", flag: "🇹🇭" },
  { code: "ja", label: "日本語", flag: "🇯🇵" },
  { code: "vi", label: "Tiếng Việt", flag: "🇻🇳" },
];

const AppBarStyled = styled(AppBar)(({ theme }) => ({
  boxShadow: "none",
  background: theme.palette.background.paper,
  justifyContent: "center",
  backdropFilter: "blur(4px)",
  borderTopLeftRadius: 32,
  [theme.breakpoints.up("lg")]: {
    minHeight: "70px",
  },
}));
const ToolbarStyled = styled(Toolbar)(({ theme }) => ({
  width: "100%",
  color: theme.palette.text.secondary,
}));

const Header = ({ toggleMobileSidebar }: ItemType) => {
  const router = useRouter();
  const { i18n } = useTranslation();

  const handleChange = (lng: string) => {
    i18n.changeLanguage(lng);
  };
  // const lgUp = useMediaQuery((theme) => theme.breakpoints.up('lg'));
  // const lgDown = useMediaQuery((theme) => theme.breakpoints.down('lg'));

  return (
    <>
      <Script
        src="https://unpkg.com/@microsoft/signalr@latest/dist/browser/signalr.min.js"
        strategy="afterInteractive"
      />
      <AppBarStyled position="sticky" color="default">
        <ToolbarStyled>
          <IconButton
            color="inherit"
            aria-label="menu"
            onClick={toggleMobileSidebar}
            sx={{
              display: {
                lg: "none",
                xs: "inline",
              },
            }}
          >
            <IconMenu width="20" height="20" />
          </IconButton>

          <Box flexGrow={1} />
          <Box sx={{ display: "flex", gap: "20px" }}>
            <Notification />
            <Stack spacing={1} direction="row" alignItems="center">
              {/* <div>
            {languages.map((lang) => (
              <button
                key={lang.code}
                onClick={() => handleChange(lang.code)}
                disabled={i18n.language === lang.code}
                style={{ marginRight: 8 }}
              >
                {lang.label}
              </button>
            ))}
          </div> */}
              {/* <Button variant="contained" component={Link} href="/authentication/login" disableElevation color="primary" >
            Login
          </Button> */}
              <Profile />
            </Stack>
          </Box>
        </ToolbarStyled>
      </AppBarStyled>
    </>
  );
};

Header.propTypes = {
  sx: PropTypes.object,
};

export default React.memo(Header);
