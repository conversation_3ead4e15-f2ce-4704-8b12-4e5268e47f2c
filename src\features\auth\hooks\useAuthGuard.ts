// src/features/auth/hooks/useAuthGuard.ts
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from './useAuth';
import { UserRole } from '@/constants/auth';

interface UseAuthGuardOptions {
  requireAuth?: boolean;
  allowedRoles?: UserRole[];
  redirectTo?: string;
}

export function useAuthGuard({ requireAuth = true, allowedRoles, redirectTo = '/login' }: UseAuthGuardOptions = {}) {
  const { isAuthenticated, user, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // if (!isLoading) {
    //   if (requireAuth && !isAuthenticated) {
    //     router.replace(redirectTo);
    //   } else if (allowedRoles && user && !allowedRoles.includes(user.role)) {
    //     router.replace('/');
    //   }
    // }
  }, [isAuthenticated, isLoading, user, allowedRoles, requireAuth, redirectTo, router]);

  return { isAuthenticated, user, isLoading };
}
