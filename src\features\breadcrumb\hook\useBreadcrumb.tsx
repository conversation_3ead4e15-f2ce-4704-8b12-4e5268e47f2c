import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { setBreadcrumb } from '@/features/breadcrumb/state/BreadcrumbSlice';

export type BreadcrumbItem = { id: string; title: string; href?: string };
export type BreadcrumbData = { pageTitle?: string; items: BreadcrumbItem[] };

/**
 * set data breadcrumb to store
 * @param data @type { pageTitle?: string; items: BreadcrumbItem[] }
 */
export function useBreadcrumb(data: BreadcrumbData) {
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(setBreadcrumb(data));
  }, [data, dispatch]);
}