import { useMsal } from '@azure/msal-react';
import { useEffect } from 'react';

export function useMsalToken(scopes: string[] = []) {
  const { instance, accounts } = useMsal();

  useEffect(() => {
    if (accounts.length > 0) {
      instance
        .acquireTokenSilent({
          account: accounts[0],
          scopes: scopes.length ? scopes : ['openid'],
        })
        .then((response) => {
          sessionStorage.setItem('accessToken', response.accessToken);
        })
        .catch(() => {
          instance
            .acquireTokenPopup({
              account: accounts[0],
              scopes: scopes.length ? scopes : ['openid'],
            })
            .then((response) => {
              sessionStorage.setItem('accessToken', response.accessToken);
            });
        });
    }
  }, [instance, accounts, scopes]);
}
