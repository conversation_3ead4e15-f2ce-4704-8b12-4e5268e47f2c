import { createSlice } from '@reduxjs/toolkit';


interface ModalState {
  sessionExpired: boolean;
  passwordWillExpireSoon: boolean;
  forceChangePasswordExpired: boolean;
  internalServerError: boolean;
}

const initialState: ModalState = {
  sessionExpired: false,
  passwordWillExpireSoon: false,
  forceChangePasswordExpired: false,
  internalServerError: false,
};


const modalSlice = createSlice({
  name: 'modal',
  initialState,
  reducers: {
    showSessionExpiredModal(state) {
      state.sessionExpired = true;
    },
    hideSessionExpiredModal(state) {
      state.sessionExpired = false;
    },
    showInternalServerErrorModal(state) {
      state.internalServerError = true;
    },
    hideInternalServerErrorModal(state) {
      state.internalServerError = false;
    },
    showPasswordWillExpireSoonModal(state) {
      state.passwordWillExpireSoon = true;
    },
    hidePasswordWillExpireSoonModal(state) {
      state.passwordWillExpireSoon = false;
    },
    showForceChangePasswordExpiredModal(state) {
      state.forceChangePasswordExpired = true;
    },
    hideForceChangePasswordExpiredModal(state) {
      state.forceChangePasswordExpired = false;
    },
  },
});

export const {
  showSessionExpiredModal,
  hideSessionExpiredModal,
  showInternalServerErrorModal,
  hideInternalServerErrorModal,
  showPasswordWillExpireSoonModal,
  hidePasswordWillExpireSoonModal,
  showForceChangePasswordExpiredModal,
  hideForceChangePasswordExpiredModal,
} = modalSlice.actions;
export default modalSlice.reducer;
