import React from "react";
import {
  Typography,
  Box,
  Icon,
  Paper,
  styled,
  LinearProgress,
  Button,
} from "@mui/material";
import Image from "next/image";
import CheckCircleSharpIcon from "@mui/icons-material/CheckCircleSharp";
import DoneSharpIcon from "@mui/icons-material/DoneSharp";
import ClearIcon from "@mui/icons-material/Clear";

const PdfFileIcon = () => (
  <Icon
    sx={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "#ECEFF4",
      borderRadius: "4px",
      width: "32px",
      height: "32px",
    }}
  >
    <Image
      src="/images/millsheet/pdf.svg"
      alt="icon pdf"
      width={24}
      height={24}
    />
  </Icon>
);

const PdfFileIconRed = () => (
  <Icon
    sx={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "#ECEFF4",
      borderRadius: "4px",
      width: "32px",
      height: "32px",
    }}
  >
    <Image
      src="/images/millsheet/pdf-red.svg"
      alt="icon pdf"
      width={24}
      height={24}
    />
  </Icon>
);

const DocumentIcon = () => (
  <Icon
    sx={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "#ECEFF4",
      borderRadius: "4px",
      width: "32px",
      height: "32px",
    }}
  >
    <Image
      src="/images/millsheet/document.svg"
      alt="icon document"
      width={24}
      height={24}
    />
  </Icon>
);
const DocumentIconRed = () => (
  <Icon
    sx={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "#ECEFF4",
      borderRadius: "4px",
      width: "32px",
      height: "32px",
    }}
  >
    <Image
      src="/images/millsheet/document-red.svg"
      alt="icon document"
      width={24}
      height={24}
    />
  </Icon>
);

// const CloseIconMill = () => (
//   <Image
//     src="/images/millsheet/close.svg"
//     alt="icon close"
//     width={24}
//     height={24}
//   />
// );

const RecycleBinIcon = () => (
  <Image
    src="/images/millsheet/recycle.svg"
    alt="icon recycle"
    width={16}
    height={16}
  />
);

const CustomListRow = styled(Box)(({}) => ({
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  width: "100%",
  marginBottom: "5px",
  gap: "10px",
}));

const CustomFileRow = styled(Box)(({}) => ({
  display: "flex",
  flexDirection: "row",
  width: "100%",
  gap: "10px",
  alignItems: "center",
}));

const CustomRow = styled(Box)(({}) => ({
  display: "flex",
  flexDirection: "column",
  width: "730px",
}));

type SheetData = {
  item: File[];
};

interface PaperDetailProps {
  index: number;
  sheetName: string;
  sheetData: SheetData;
  hasErrorFiles: boolean;
  hasError: boolean;
  allCompleted: boolean;
  getErrorMap: (data?: any) => Map<string, Error>;
  errorFiles: any[];
  fileProgress: Record<string, number>;
  handleRemoveFile: (data?: any) => void | Promise<any>;
}

const PaperUpload: React.FC<PaperDetailProps> = ({
  sheetName,
  sheetData,
  hasErrorFiles,
  hasError,
  getErrorMap,
  index,
  allCompleted,
  errorFiles,
  fileProgress,
  handleRemoveFile,
}) => {
  let statusBox = null;
  if (allCompleted) {
    const isCompletePair =
      sheetData.item.length === 2 && !hasErrorFiles && !hasError;

    statusBox = isCompletePair ? (
      <Box
        sx={{
          display: "flex",
          gap: "5px",
          alignItems: "center",
        }}
      >
        <CheckCircleSharpIcon
          sx={{ width: 18, height: 18, color: "#28303F" }}
        />
        Complete Pair
      </Box>
    ) : (
      ""
    );
  }

  return (
    <>
      <Paper
        key={index}
        elevation={2}
        sx={{
          padding: 2,
          border: "1px solid #E5E7EB",
          backgroundColor: "#FFFFFF",
          borderRadius: "8px",
          boxShadow: "0px 1px 2px 0px rgba(0, 0, 0, 0.05)",
        }}
      >
        <CustomListRow>
          <Typography
            variant="subtitle1"
            sx={{
              fontFamily: "Roboto",
              fontWeight: 400,
              fontStyle: "normal",
              fontSize: "16px",
              lineHeight: "100%",
              letterSpacing: 0,
              verticalAlign: "middle",
            }}
            color={hasErrorFiles || hasError ? "#CA002E" : ""}
          >
            {sheetName.replace(".pdf", "")}
          </Typography>
          {/* hanlde for case status box is incomplete or complete */}
          {statusBox}
        </CustomListRow>
        {sheetData.item.map((file, i) => {
          const errorMap = getErrorMap(errorFiles);
          // check error file name for BE respone
          const error = errorMap.get(file.name);
          const hasFileError = !!error;
          const isNotAcceptedFileType =
            !["application/pdf", "text/csv", "application/xmp"].includes(
              file.type
            ) || file.type === "";
          const isTooLarge = file.size > 30 * 1024 * 1024; // 30MB in bytes
          const isMetadata = file.name.toLowerCase().endsWith(".xmp");
          const isOnlyMetadata = sheetData.item.length === 1;
          const isCsv = file.type === "text/csv";

          let message = "";
          if (isNotAcceptedFileType && file.type !== "") {
            message =
              "This file format is not supported. Please check the file format.";
          } else if (isTooLarge) {
            message =
              "File size exceeds the limit of 30MB. Please select a smaller file.";
          } else if (
            (isOnlyMetadata && isMetadata) ||
            (isOnlyMetadata && isCsv)
          ) {
            message = "Missing PDF file with the same name";
          }
          const isPdfIcon = file.type === "application/pdf";
          const progress = fileProgress[file.name] || 0;
          const isCompleted = progress === 100;

          const fileSizeMB = `${(file.size / (1024 * 1024)).toFixed(2)} MB`;
          const displayMessage = hasFileError
            ? error.message
            : message || fileSizeMB;

          return (
            <CustomFileRow key={file.name}>
              {isPdfIcon ? (
                hasFileError || hasError ? (
                  <PdfFileIconRed />
                ) : (
                  <PdfFileIcon />
                )
              ) : hasFileError || hasError ? (
                <DocumentIconRed />
              ) : (
                <DocumentIcon />
              )}

              <CustomRow>
                <Typography
                  variant="body2"
                  sx={{
                    fontFamily: "Roboto",
                    fontWeight: 400,
                    fontStyle: "normal",
                    fontSize: "14px",
                    lineHeight: "150%",
                    letterSpacing: 0,
                    verticalAlign: "middle",
                  }}
                  color={hasFileError || hasError ? "#CA002E" : "text.primary"}
                >
                  {file.name}
                </Typography>

                {!isCompleted && (
                  <LinearProgress
                    variant="determinate"
                    value={progress}
                    sx={{
                      my: 1,
                      width: "629px",
                      height: "8px",
                      borderRadius: "9999px",
                      backgroundColor: "#CFCFCF",
                      "& .MuiLinearProgress-bar1": {
                        backgroundColor: "#2563EB",
                      },
                    }}
                  />
                )}

                {isCompleted && (
                  <Typography
                    color={hasFileError || hasError ? "#CA002E" : "#6B7280"}
                    variant="caption"
                    sx={{
                      fontFamily: "Roboto",
                      fontWeight: 400,
                      fontStyle: "normal",
                      fontSize: "12px",
                      lineHeight: "120%",
                      letterSpacing: 0,
                      verticalAlign: "middle",
                    }}
                  >
                    {displayMessage}
                  </Typography>
                )}
              </CustomRow>

              {isCompleted && (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    gap: "8px",
                  }}
                >
                  <Button
                    sx={{
                      padding: 0,
                      height: "20px",
                      minWidth: "20px",
                    }}
                    disabled
                  >
                    {hasFileError || message !== "" ? (
                      <ClearIcon sx={{ color: "#B91C1C" }} />
                    ) : (
                      <DoneSharpIcon sx={{ color: "#16A34A" }} />
                    )}
                  </Button>
                  <Button
                    sx={{
                      padding: 0,
                      height: "24px",
                      minWidth: "24px",
                    }}
                    onClick={() => handleRemoveFile(file.name)}
                  >
                    <RecycleBinIcon />
                  </Button>
                </Box>
              )}
            </CustomFileRow>
          );
        })}
      </Paper>
    </>
  );
};

export default PaperUpload;
