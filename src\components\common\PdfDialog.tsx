import Button from "@/components/common/button/Button";
import PdfPreviewer from "@/components/common/PdfPreviewer";
import { ConfirmStatus, PreviewPDF } from "@/features/millsheet/types/millSheetTypes";
import { Box, Dialog, DialogContent, Typography } from "@mui/material";
import Image from "next/image";
import { useState } from "react";

interface PdfDialogProps {
  open: boolean;
  onClose: (action: "cancel" | "down" | "confirm") => void;
  numPages: number;
  setNumPages: (n: number) => void;
  infoPdfFile?: PreviewPDF;
  isCustomer: boolean;
}

const PdfDialog = ({ open, onClose, numPages, setNumPages, infoPdfFile, isCustomer = false }: PdfDialogProps) => {
  const [currentPage, setCurrentPage] = useState(1);

  if (!open) return null;
  if (!infoPdfFile) return null;
  return (
    <>
      {open && (
        <Box
          sx={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            height: 48,
            background: "none",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 1400,
          }}
        >
          <Typography variant="subtitle1" sx={{ fontWeight: 400, color: "#fff" }}>
            Viewing {currentPage}/{numPages}
          </Typography>
          <Box
            sx={{
              position: "absolute",
              right: 24,
              display: "flex",
              alignItems: "center",
            }}
          >
            <Button
              sx={{ background: "none", fontWeight: 400, ":hover": { background: "none" } }}
              prefixIcon={<Image src={"../images/icons/download.svg"} alt="download" width={24} height={24}></Image>}
              onClick={() => onClose("down")}
            >
              Download
            </Button>
            <Button
              sx={{ background: "none", fontWeight: 400, ":hover": { background: "none" } }}
              prefixIcon={<Image src={"../images/icons/close.svg"} alt="close" width={24} height={24}></Image>}
              onClick={() => onClose("cancel")}
            ></Button>
          </Box>
        </Box>
      )}
      <Dialog
        open={open}
        onClose={() => onClose("cancel")}
        maxWidth="md"
        fullWidth
        slotProps={{
          backdrop: {
            sx: {
              backgroundColor: "rgba(0, 0, 0, 0.7)",
            },
          },
          paper: {
            style: {
              pointerEvents: "auto",
            },
          },
        }}
      >
        <DialogContent
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: 400,
            background: "#fff",
            padding: 0,
          }}
          sx={{
            ".pdf-preview-file": {
              maxHeight: "calc(100vh - 160px)",
            },
          }}
        >
          <PdfPreviewer
            pdfUrl={infoPdfFile?.fileUrl ?? ""}
            onLoadSuccess={setNumPages}
            onPageChange={setCurrentPage}
          />
        </DialogContent>
      </Dialog>
      {open && isCustomer && (
        <Box
          sx={{
            position: "fixed",
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 1500,
            background: "#222",
            display: "flex",
            alignItems: "center",
            px: 3,
            py: 1,
            boxShadow: "0 -2px 8px rgba(0,0,0,0.2)",
            justifyContent: "center",
            minHeight: 66,
          }}
        >
          {infoPdfFile?.isConfirmed === ConfirmStatus.Confirmed ? (
            <Typography variant="body2" sx={{ ml: 3, color: "#fff" }}>
              This PDF file has been confirmed as received and reviewed by {infoPdfFile?.lastName} {infoPdfFile?.firstName} on {infoPdfFile?.confirmAt}
            </Typography>
          ) : (
            <>
              <Button
                variant="outlined"
                onClick={() => onClose("cancel")}
                sx={{ minWidth: "91px", backgroundColor: "#fff", mr: "16px" }}
                size="medium"
              >
                Cancel
              </Button>
              <Button
                sx={{ minWidth: "91px" }}
                type="submit"
                variant="contained"
                size="medium"
                onClick={() => onClose("confirm")}
              >
                Confirm
              </Button>
              <Typography variant="body2" sx={{ ml: 3, color: "#fff" }}>
                Please confirm you have received and reviewed this PDF in accordance with company procedures.
              </Typography>
            </>
          )}
        </Box>
      )}
    </>
  );
};

export default PdfDialog;