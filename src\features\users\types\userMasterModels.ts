export interface UserMasterInfo {
  clientId: string;
  countryCode: string;
  countryName: string;
  departmentCode: string;
  departmentName: string;
  firstName: string;
  id: number;
  lastLoginDateTime: string;
  lastName: string;
  lockStatus: number;
  userId: string;
  organizationCode: string;
  organizationName: string;
  roleCode: string;
  roleName: string;
}
export interface Sort {
  sortColumn?: string;
  sortDirection?: "asc" | "desc";
}

export interface Pagination {
  pageNumber?: number;
  pageSize?: number;
}

export interface ConditionSearch {
  operatorName?: string | null;
  userId?: string | null;
  countryCode?: string | null;
  role?: string | null;
  department?: string | null;
  lastLogin?: string | null;
  lockStatus?: 0 | 1 | null;
  pageNumber?: number | null;
  pageSize?: number | null;
  organizationCode?: string | null;
}

export interface UserMasterSearchRequest {
  condition?: ConditionSearch;
  sort?: Sort[];
  pagination?: Pagination;
  lockStatus?: number | null;
}
