import Button from "@/components/common/button/Button";
import { signOutMsal } from "@/services/msalService";
import { useMsal } from "@azure/msal-react";
import LogoutIcon from "@mui/icons-material/Logout";
import { Box, Drawer, Tooltip, useMediaQuery } from "@mui/material";
import React, { useEffect, useState } from "react";
import SidebarItems from "./SidebarItems";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { DATA_IDS } from "@/constants/routes";

interface ItemType {
  isMobileSidebarOpen: boolean;
  onSidebarClose: (event: React.MouseEvent<HTMLElement>) => void;
  isSidebarOpen: boolean;
}

const MSidebar = ({
  isMobileSidebarOpen,
  onSidebarClose,
  isSidebarOpen,
}: ItemType) => {
  const lgUp = useMediaQuery((theme: any) => theme.breakpoints.up("lg"));
  const { instance, accounts } = useMsal();
  // Collapsed state
  const [collapsed, setCollapsed] = useState(false);
  const sidebarWidth = collapsed ? "110px" : "280px";
  const location = usePathname()?.split("/")[1] ?? "";

  // Custom CSS for short scrollbar
  const scrollbarStyles = {
    backgroundColor: "#ECEFF4",
    borderRight: "none",
    "&::-webkit-scrollbar": {
      width: "7px",
    },
    "&::-webkit-scrollbar-thumb": {
      backgroundColor: "#ECEFF4",
      borderRadius: "15px",
    },
  };

  //observe mounting behaviour of icon millsheet list

  useEffect(() => {
    const observer = new MutationObserver((mutationsList, observerInstance) => {
      const targetElement: any = document.querySelector(
        `img[data-id="${DATA_IDS.MILL_SHEET}"]`
      );
      const childrenElement: any = document.querySelector(
        `div[data-id="${DATA_IDS.MILL_SHEET}"]`
      );
      if (
        targetElement &&
        location === DATA_IDS.MILL_SHEET &&
        !collapsed &&
        !childrenElement
      ) {
        targetElement.click();
        observerInstance.disconnect();
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => {
      observer.disconnect();
    };
  }, [location, collapsed]);

  if (lgUp) {
    return (
      <Box
        sx={{
          width: sidebarWidth,
          flexShrink: 0,
        }}
      >
        {/* Sidebar for desktop */}
        <Drawer
          anchor="left"
          open={isSidebarOpen}
          variant="permanent"
          slotProps={{
            paper: {
              sx: {
                boxSizing: "border-box",
                ...scrollbarStyles,
                width: sidebarWidth,
                transition: "width 0.2s",
                overflowX: "hidden",
              },
            },
          }}
        >
          <Box
            sx={{ height: "100%", display: "flex", flexDirection: "column" }}
          >
            {/* Sidebar Items - Logo and collapse icon integrated */}
            <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
              <SidebarItems
                collapsed={collapsed}
                onToggleCollapse={() => setCollapsed((prev) => !prev)}
              />
            </Box>

            <Tooltip title={collapsed ? "Logout" : ""} placement="right">
              <Button
                sx={{
                  ".MuiButton-startIcon": {
                    marginRight: collapsed ? 0 : "12px",
                  },
                  minWidth: "auto",
                  padding: collapsed ? "13px 9px 13px 0" : "10px 12px",
                  justifyContent: collapsed ? "center" : "start",
                  backgroundColor: "transparent",
                  color: "rgb(43,43,43)",
                  boxShadow: "unset",
                  "&:hover": { backgroundColor: "#e1e4ea", boxShadow: "unset" },
                  margin: collapsed ? "0 20px 50px 20px" : "0 30px 50px 30px",
                  fontWeight: 400,
                  borderRadius: "0 10px 10px 0",
                }}
                prefixIcon={
                  <Image
                    src="/images/sidebar/Log-out.svg"
                    alt="Logout"
                    width={20}
                    height={20}
                  />
                }
                onClick={() => signOutMsal(instance, accounts[0])}
              >
                {collapsed ? "" : "Logout"}
              </Button>
            </Tooltip>
          </Box>
        </Drawer>
      </Box>
    );
  }

  return (
    <Drawer
      anchor="left"
      open={isMobileSidebarOpen}
      onClose={onSidebarClose}
      variant="temporary"
      slotProps={{
        paper: {
          sx: {
            boxShadow: (theme) => theme.shadows[8],
            ...scrollbarStyles,
            width: sidebarWidth,
            transition: "width 0.2s",
            overflowX: "hidden",
          },
        },
      }}
    >
      <Box sx={{ display: "flex", flexDirection: "column", height: "100%" }}>
        {/* Sidebar Items - Logo and collapse icon integrated */}
        <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
          <SidebarItems
            collapsed={collapsed}
            onToggleCollapse={() => setCollapsed((prev) => !prev)}
          />
        </Box>
        <Tooltip
          slotProps={{
            popper: {
              modifiers: [
                {
                  name: "offset",
                  options: {
                    offset: [0, -6],
                  },
                },
              ],
            },
          }}
          title={collapsed ? "Logout" : ""}
          placement="right"
        >
          <Button
            sx={{
              ".MuiButton-startIcon": { marginRight: collapsed ? 0 : "12px" },
              minWidth: "auto",
              padding: collapsed ? "13px 9px 13px 0" : "13px 20px",
              justifyContent: collapsed ? "center" : "start",
              backgroundColor: "transparent",
              color: "rgb(43,43,43)",
              boxShadow: "unset",
              "&:hover": { backgroundColor: "#e1e4ea", boxShadow: "unset" },
              margin: collapsed ? "0 20px 50px 20px" : "0 30px 50px 30px",
              fontWeight: 400,
              borderRadius: "0 10px 10px 0",
            }}
            prefixIcon={
              <Image
                src="/images/sidebar/Log-out.svg"
                alt="Logout"
                width={20}
                height={20}
              />
            }
            onClick={() => signOutMsal(instance, accounts[0])}
          >
            {collapsed ? "" : "Logout"}
          </Button>
        </Tooltip>
      </Box>
    </Drawer>
  );
};

export default MSidebar;
