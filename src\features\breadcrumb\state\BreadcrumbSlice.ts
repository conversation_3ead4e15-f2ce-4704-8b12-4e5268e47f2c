import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type Crumb = { id: string; title: string; href?: string };

type BreadcrumbState = {
  items: Crumb[];
  pageTitle?: string;
};

const initialState: BreadcrumbState = {
  items: [],
  pageTitle: undefined,
};

const breadcrumbSlice = createSlice({
  name: 'breadcrumb',
  initialState,
  reducers: {
    setBreadcrumb(state, action: PayloadAction<BreadcrumbState>) {
      state.items = action.payload.items;
      state.pageTitle = action.payload.pageTitle;
    },
    clearBreadcrumb(state) {
      state.items = [];
      state.pageTitle = undefined;
    },
  },
});

export const { setBreadcrumb, clearBreadcrumb } = breadcrumbSlice.actions;
export default breadcrumbSlice.reducer;