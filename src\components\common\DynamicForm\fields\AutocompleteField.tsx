import React from "react";
import Autocomplete from "@mui/material/Autocomplete";
import TextField from "@mui/material/TextField";
import { Controller } from "react-hook-form";
import { FieldConfig, FieldOption } from "../DynamicForm.types";
import { Box } from "@mui/material";
import MuiErrorCustomStyles from "@/utils/theme/muiErrorStyles";

interface Props {
  field: FieldConfig;
  control: any;
  error?: string;
  disabled?: boolean;
  i18n?: (key: string) => string;
}

const AutocompleteField: React.FC<Props> = ({
  field,
  control,
  error,
  disabled,
  i18n,
}) => (
  <Controller
    name={field.name}
    control={control}
    render={({ field: controllerField }) =>
      field.hiddenLabelNormal ? (
        <Box style={{ position: "relative", width: "100%" }}>
          <Box
            style={{
              position: "absolute",
              left: 0,
              right: 0,
              top: 2,
              color: "#222222",
              fontFamily: "Roboto",
              fontWeight: 400,
              fontSize: 14,
              lineHeight: 1.5,
              zIndex: 2,
            }}
          >
            {i18n ? i18n(field.label) : field.label}
          </Box>
          <Autocomplete
            {...controllerField}
            options={field.options || []}
            getOptionLabel={(option: any) =>
              i18n ? i18n(option?.label || "") : option?.label || ""
            }
            isOptionEqualToValue={(option: any, value: any) =>
              option.value === value.value
            }
            disabled={disabled || field.disabled}
            onBlur={controllerField.onBlur}
            className="hiddenLabelNormal"
            renderInput={(params) => (
              <TextField
                {...params}
                label=""
                placeholder={field.placeholder}
                error={false}
                helperText={undefined}
                fullWidth
                slotProps={{
                  htmlInput: {
                    ...params.inputProps,
                    maxLength: field.maxLength,
                  },
                }}
                sx={{
                  ...field.style,
                  ...MuiErrorCustomStyles,
                  mt: 3,
                  "& .MuiInputBase-input.Mui-disabled": {
                    WebkitTextFillColor:
                      (field as any).disabledValueColor || "#111827",
                    color: (field as any).disabledValueColor || "#111827",
                  },
                }}
                className={field.className}
              />
            )}
            onChange={(_, newValue) => {
              controllerField.onChange(newValue ? newValue.value : undefined);
            }}
            value={
              field.options?.find(
                (option: FieldOption) => option.value === controllerField.value
              ) || null
            }
          />
        </Box>
      ) : (
        <Autocomplete
          {...controllerField}
          options={field.options || []}
          getOptionLabel={(option: any) =>
            i18n ? i18n(option?.label || "") : option?.label || ""
          }
          isOptionEqualToValue={(option: any, value: any) =>
            option.value === value.value
          }
          disabled={disabled || field.disabled}
          renderInput={(params) => (
            <TextField
              {...params}
              label={
                field.required && !field.disabled ? (
                  <span>
                    {i18n ? i18n(field.label) : field.label}
                    <span style={{ color: "#CA002E", marginLeft: 4 }}>*</span>
                  </span>
                ) : i18n ? (
                  i18n(field.label)
                ) : (
                  field.label
                )
              }
              placeholder={field.placeholder}
              error={!!error}
              helperText={error || field.helperText}
              fullWidth
              sx={{
                ...field.style,
                ...MuiErrorCustomStyles,
                "& .MuiInputBase-input.Mui-disabled": {
                  WebkitTextFillColor:
                    (field as any).disabledValueColor || "#111827",
                  color: (field as any).disabledValueColor || "#111827",
                },
              }}
              className={`controlNormal ${field.className ?? ""}`}
              slotProps={{
                htmlInput: { ...params.inputProps, maxLength: field.maxLength },
                inputLabel: {
                  sx: {
                    "&.MuiInputLabel-shrink": {
                      fontSize: 18,
                      backgroundColor: "rgba(255, 255, 255, 1)",
                    },
                  },
                },
              }}
            />
          )}
          onChange={(_, newValue) => {
            controllerField.onChange(newValue ? newValue.value : null);
          }}
          onBlur={controllerField.onBlur}
          value={
            field.options?.find(
              (option: FieldOption) => option.value === controllerField.value
            ) || null
          }
        />
      )
    }
  />
);

export default AutocompleteField;
