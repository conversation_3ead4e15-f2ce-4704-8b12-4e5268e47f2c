
import { bulkDownloadMillSheet, confirmMillSheet, customerList, downloadMillSheet, previewPDF, searchMillSheet, supplierList } from "@/features/millsheet/api/millSheetApi"
import { MillSheetBulkDownloadParams, MillSheetDownloadParams, MillSheetSearchParams } from "@/features/millsheet/types/millSheetTypes"

export const getMillSheetList  = (dataSearch: MillSheetSearchParams) => {
    return searchMillSheet(dataSearch)
}

export const getCustomerList = () => {
    return customerList();
}

export const getSuplierList = () => {
    return supplierList();
}

export const downloadMillSheetFile = (payload: MillSheetDownloadParams) => {
    return downloadMillSheet(payload)
}

export const bulkDownloadMillSheetFile = (payload: MillSheetBulkDownloadParams[]) => {
    return bulkDownloadMillSheet(payload)
}

export const getPreviewPdfFile = (millSheetId: number) => {
    return previewPDF(millSheetId)
}

export const confirmMillSheetById = (millSheetId: number) => {
    return confirmMillSheet(millSheetId)
}