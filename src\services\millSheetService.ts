
import { bulkDownloadMillSheet, confirmMillSheet, customerList, deleteMillSheets, downloadMillSheet, getMillSheetEdit, previewPDF, putMillSheetEdit, searchMillSheet, supplierList } from "@/features/millsheet/api/millSheetApi"
import { MillSheetBulkDownloadParams, MillSheetConfirmParams, MillSheetDownloadParams, MillSheetPreviewFileParams, MillSheetRecord, MillSheetSearchParams, MillSheetStatusType } from "@/features/millsheet/types/millSheetTypes"

export const getMillSheetList  = (dataSearch: MillSheetSearchParams) => {
    return searchMillSheet(dataSearch)
}

export const getCustomerList = (countryCode: string) => {
    return customerList(countryCode);
}

export const getSuplierList = () => {
    return supplierList();
}

export const downloadMillSheetFile = (payload: MillSheetDownloadParams) => {
    return downloadMillSheet(payload)
}

export const bulkDownloadMillSheetFile = (payload: MillSheetBulkDownloadParams[]) => {
    return bulkDownloadMillSheet(payload)
}

export const getPreviewPdfFile = (payload: MillSheetPreviewFileParams) => {
    return previewPDF(payload)
}

export const confirmMillSheetById = (payload: MillSheetConfirmParams) => {
    return confirmMillSheet(payload)
}

export const deleteMillSheetByIds = (payload: {id: number; status: MillSheetStatusType}[]) => {
    return deleteMillSheets(payload)
}

export const getMillSheetById  = (payload: {id: number| string; status: MillSheetStatusType}) => {
    return getMillSheetEdit(payload)
}

export const updateMillSheet  = (payload: MillSheetRecord) => {
    return putMillSheetEdit(payload)
}