import axiosInstance from "@/lib/axiosInstance"

export interface NotificationSearchParams {
  condition?: Record<string, any>;
  sort?: any[];
  pagination?: {
    pageSize: number;
    pageNumber: number;
  };
}

export const searchNotifications = (params: NotificationSearchParams) => {
  return axiosInstance.post('/notification/search', params);
};

export const updateNotificationReadStatus = (notificationId: number) => {
  return axiosInstance.put(`/notification/${notificationId}`);
};

export const countUnreadNotifications = () => {
  return axiosInstance.get('/notification/count-unread');
};

export const markAllNotificationsRead = () => {
  return axiosInstance.put('/notification');
};
