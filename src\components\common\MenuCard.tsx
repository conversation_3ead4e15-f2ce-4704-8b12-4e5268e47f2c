import React, { useState } from "react";
import Card from "@mui/material/Card";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import Link from "next/link";

interface MenuItem {
  id: string;
  title: string;
  icon: React.ReactNode;
  color: string;
  bgGradient: string;
  href?: string;
  screenCode?: string;
  children?: MenuItem[];
}

const MenuCard = ({ item }: { item: MenuItem }) => {
  const [open, setOpen] = useState(false);

  if (item.children && item.children.length > 0) {
    // Mill Sheet parent
    return (
      <Box sx={{ minWidth: 240, maxWidth: 270 }}>
        <Card
          sx={{
            position: "relative",
            overflow: "visible",
            borderRadius: "16px",
            padding: "25px",
            boxShadow:
              "0px 2px 4px -2px rgba(0,0,0,0.1), 0px 4px 6px -1px rgba(0,0,0,0.1)",
            transition: "transform 0.3s, box-shadow 0.3s",
            cursor: "pointer",
            minHeight: 148,
            minWidth: 240,
            maxWidth: 270,
            height: 148,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            textAlign: "center",
            border: "1px solid rgba(229,231,235,1)",
            background: "linear-gradient(90deg, #fff 60%, #ffe5eb 100%)",
            "&:hover": {
              border: "none",
              transform: "scale(1.03) translateY(-4px)",
              borderBottom: "4px solid #CA002E",
              boxShadow:
                "0px 2px 4px -2px rgba(0,0,0,0.1), 0px 4px 6px -1px rgba(0,0,0,0.1)",
            },
          }}
          onClick={() => setOpen(true)}
        >
          <Box
            sx={{
              width: 64,
              height: 64,
              backgroundColor: "#CA002E",
              borderRadius: "12px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              mb: 2,
              transition: "transform 0.3s",
              padding: "8px 8px 4px 8px",
            }}
          >
            <Box sx={{ color: "#fff" }}>{item.icon}</Box>
          </Box>
          <Typography
            variant="subtitle1"
            fontWeight={600}
            color="grey.800"
            fontSize={14}
            gutterBottom
          >
            {item.title}
          </Typography>
          <IconButton
            sx={{
              position: "absolute",
              right: 12,
              top: 12,
              color: "#CA002E",
            }}
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              setOpen(true);
            }}
            aria-label="Expand"
          >
            <ExpandMoreIcon />
          </IconButton>
        </Card>
      </Box>
    );
  }

  // Default card (User List)
  const cardContent = (
    <Card
      sx={{
        position: "relative",
        overflow: "visible",
        borderRadius: "16px",
        padding: "25px",
        boxShadow:
          "0px 2px 4px -2px rgba(0, 0, 0, 0.1), 0px 4px 6px -1px rgba(0, 0, 0, 0.1)",
        transition: "transform 0.3s, box-shadow 0.3s",
        cursor: "pointer",
        minHeight: 148,
        minWidth: 240,
        maxWidth: 240,
        height: 148,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        textAlign: "center",
        border: "1px solid rgba(229, 231, 235, 1)",
        "&:hover": {
          border: "none",
          transform: "scale(1.03) translateY(-4px)",
          borderBottom: "4px solid #CA002E",
          boxShadow:
            "0px 2px 4px -2px rgba(0, 0, 0, 0.1), 0px 4px 6px -1px rgba(0, 0, 0, 0.1)",
        },
      }}
    >
      <Box
        sx={{
          width: 64,
          height: 64,
          backgroundColor: "#CA002E",
          borderRadius: "12px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          mb: 2,
          transition: "transform 0.3s",
          padding: "8px 8px 4px 8px",
        }}
      >
        <Box sx={{ color: "#fff" }}>{item.icon}</Box>
      </Box>
      <Typography
        variant="subtitle1"
        fontWeight={600}
        color="grey.800"
        fontSize={14}
        gutterBottom
      >
        {item.title}
      </Typography>
    </Card>
  );

  return item.href ? (
    <Link href={item.href} style={{ textDecoration: "none" }}>
      {cardContent}
    </Link>
  ) : (
    cardContent
  );
};

export default MenuCard;
