trigger: none
pr: none
schedules: []

pool:
  vmImage: ubuntu-latest

variables: 
  - group: prod-static-app-variables
  - group: prod-static-app-variables-keyvault
  - name: APP_ENV
    value: production

stages:
  - stage: BuildSource
    displayName: 'Build with npm'
    jobs: 
    - job: build_job 
      displayName: Build Job 

      steps: 
      - checkout: self 
        submodules: true 
      - task: NodeTool@0
        inputs:
          versionSpec: $(NODE_VERSION)
        displayName: 'Install Node.js'

      - task: AzureCLI@2
        inputs:
          azureSubscription: 'dev-azure-pipeline-connection'
          scriptType: 'bash'
          scriptLocation: 'inlineScript'
          inlineScript: |
            set -e
            echo "=== Key Vault Direct Access ==="
            KEY_VAULT_NAME="$(KEYVAULT_NAME)"
            
            echo "Accessing Key Vault: $KEY_VAULT_NAME"

            # Get all secret names from Key Vault
            echo "=== Getting all secrets from Key Vault ==="
            az keyvault secret list --vault-name "$KEY_VAULT_NAME" --query "[].name" --output tsv > secret_names.txt
            
            echo "Found $(cat secret_names.txt | wc -l) secrets"
            echo "Secret names:"
            cat secret_names.txt | head -10
            
            echo "=== Creating .env.production ==="
            echo "" > .env.production

            # Process each secret
            while read -r secret_name; do
              if [ -n "$secret_name" ]; then
                echo "Processing: $secret_name"
                
                # Get the secret value
                secret_value=$(az keyvault secret show --vault-name "$KEY_VAULT_NAME" --name "$secret_name" --query "value" --output tsv)
                
                # Convert name: kebab-case to SNAKE_CASE
                env_var_name=$(echo "$secret_name" | sed 's/-/_/g' | tr '[:lower:]' '[:upper:]')
                
                echo "✓ $secret_name → $env_var_name"
                echo "$env_var_name=$secret_value" >> .env.production
              fi
            done < secret_names.txt

            echo "=== Building ==="
            npm install
            npm run build
            
            echo "=== Cleanup ==="
            rm -f .env.production secret_names.txt
            echo "=== Checking built files ==="
            ls -la

  - stage: DeployStaticWebApp
    displayName: 'Deploy to Azure Static Web App'
    dependsOn: BuildSource
    condition: succeeded()
    jobs:

      - job: deploy_job 
        displayName: Deploy Job 
        steps:
        - task: AzureStaticWebApp@0
          inputs: 
            azure_static_web_apps_api_token: $(STATIC-WEB-APP-API-TOKEN-DEV) 
            app_location: $(MY_APP_LOCATION) # App source code path 
            api_location: $(MY_API_LOCATION) # Api source code path - optional 
            skip_app_build: true  # This tells Oryx to skip building
