'use client';
import { Typography } from '@mui/material';
import PageContainer from '@/app/(DashboardLayout)/components/container/PageContainer';
import DashboardCard from '@/app/(DashboardLayout)/components/shared/DashboardCard';
// Thêm import useTranslation
import { useTranslation } from 'react-i18next';

const MillSheetList = () => {
  const { t } = useTranslation('common'); // 'common' là namespace

  return (
    <PageContainer title="Sample Page" description="this is Sample page">
      <DashboardCard title="Sample Page">
        <Typography>{t('samplePage')}</Typography>
      </DashboardCard>
    </PageContainer>
  );
};

export default MillSheetList;