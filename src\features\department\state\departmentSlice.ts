import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import * as departmentService from "@/services/departmentService";
import { Department, DepartmentState } from "../types/department";

const initialState: DepartmentState = {
  list: [],
  error: null,
  isLoading: true,
  tempList: [],
};
const nameState = "department";

export const department = createAsyncThunk(`${nameState}/list`, async (organizationCode: string) => {
  try {
    const res = await departmentService.getDepartmentList(organizationCode);
    return res;
  } catch (err: any) {}
});

export const getDepartmentListByOrganizationCode = createAsyncThunk(`${nameState}/tempList`, async (organizationCode: string) => {
  try {
    const res = await departmentService.getDepartmentList(organizationCode);
    return res;
  } catch (err: any) {}
});

const departmentSlice = createSlice({
  name: nameState,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(department.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(department.fulfilled, (state, action: any) => {
        state.isLoading = false;
        state.list = action.payload;
      })
      .addCase(department.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as any;
      })
      .addCase(getDepartmentListByOrganizationCode.fulfilled, (state, action: any) => {
        state.isLoading = false;
        state.tempList = action.payload;
      });
  },
});

export default departmentSlice.reducer;
