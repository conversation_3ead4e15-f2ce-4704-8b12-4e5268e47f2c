import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import * as departmentService from "@/services/departmentService";
import { Department, DepartmentState } from "../types/department";

const initialState: DepartmentState = {
  list: [],
  error: null,
  isLoading: true,
};
const nameState = "department";

export const department = createAsyncThunk(`${nameState}/list`, async () => {
  try {
    const res = await departmentService.getDepartmentList();
    return res;
  } catch (err: any) {
    console.log(err);
  }
});

const departmentSlice = createSlice({
  name: nameState,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(department.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(department.fulfilled, (state, action: any) => {
        state.isLoading = false;
        state.list = action.payload;
      })
      .addCase(department.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as any;
      });
  },
});

export default departmentSlice.reducer;
