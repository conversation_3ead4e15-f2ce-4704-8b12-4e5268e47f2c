'use client'
import React from "react";
import { useSelector } from "react-redux";
import Breadcrumbs from "@mui/material/Breadcrumbs";
import Typography from "@mui/material/Typography";
import Link from "@mui/material/Link";
import NextLink from "next/link";
import { Box } from "@mui/material";
import { SxProps, Theme } from "@mui/material/styles";
import Image from "next/image";
import { selectBreadcrumb } from "@/features/breadcrumb/state/BreadcrumbSelector";
import { useRouter } from "next/navigation";

export interface BreadcrumbProps {
  sx?: SxProps<Theme>;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ sx }) => {
  const { items: pathItems, pageTitle } = useSelector(selectBreadcrumb);
  const heading = pageTitle ?? (pathItems.length ? pathItems[pathItems.length - 1].title : "");
  const router = useRouter();

  return (
    <Box
      sx={{
        ...sx,
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        ".MuiBreadcrumbs-separator": { margin: "0 4px" },
        marginBottom: "20px",
      }}
      aria-label="breadcrumb"
    >
      <Box>
        <Typography variant="h2" fontWeight="bold">
          {heading}
        </Typography>
        <Breadcrumbs
          separator={<Typography sx={{ color: "text.secondary", fontSize: 14, mx: 0.5 }}>{">"}</Typography>}
          aria-label="breadcrumb trail"
          sx={{ color: "text.secondary", fontSize: 14 }}
        >
          {pathItems.length > 0 && (
            <Link
              href="/"
              component={NextLink}
              sx={{ display: "flex", alignItems: "center", color: "text.secondary" }}
              aria-label="Home"
              onClick={(e) => {
                e.preventDefault();
                router.push("/");
              }}
            >
              <Image src="/images/home/<USER>" alt="home breadcrumb" height={20} width={20} />
            </Link>
          )}

          {pathItems.map((item, idx) => {
            const isLast = idx === pathItems.length - 1;
            if (isLast) {
              return (
                <Typography key={item.id} sx={{ color: "text.secondary", fontSize: 14 }} aria-current="page">
                  {item.title}
                </Typography>
              );
            }
            return item.href ? (
              <Link
                key={item.id}
                href={item.href}
                component={NextLink}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  color: "text.secondary",
                  textDecoration: "none",
                  "&:hover": { textDecoration: "underline" },
                }}
                aria-label={item.title}
                onClick={(e) => {
                  e.preventDefault();
                  if (item.href) {
                    router.push(item.href);
                  }
                }}
              >
                {item.title}
              </Link>
            ) : (
              <Typography key={item.id} sx={{ color: "text.secondary", fontSize: 14 }}>
                {item.title}
              </Typography>
            );
          })}
        </Breadcrumbs>
      </Box>
    </Box>
  );
};

export default Breadcrumb;