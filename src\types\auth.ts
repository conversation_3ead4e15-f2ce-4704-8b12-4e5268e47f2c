// src/types/auth.ts
// Auth-related TypeScript interfaces

import { UserRole } from "../constants/auth";
export interface AccessMenu {
  roleCode: string;
  screenCode: string;
  screenName: string;
  canView: number;
  canCreate: number;
  canUpdate: number;
  canDelete: number;
  canImport: number;
  canExport: number;
}

export interface NotificationInfo {
  hubUrl: string;
  token: string;
}

export type RoleCode = typeof ROLE_CODE[keyof typeof ROLE_CODE];

export interface User {
  id: number;
  userId: string;
  clientId: string;
  lastName: string;
  firstName: string;
  organizationCode: string;
  organizationName: string;
  departmentCode: string;
  departmentName: string;
  countryCode: string;
  countryName: string;
  roleCode: RoleCode
  roleName: string;
  accessMenus: AccessMenu[];
  notification: NotificationInfo;
  pwdExpireDurationDays: number;
  lastPasswordUpdate: string;
  logoutTime: number;
  lockStatus: number;
  lastLoginDateTime: string;
  imageUrl: string;
  passwordExpiringSoon: boolean;
  daysUntilPasswordExpiry: number;
  passwordExpired: boolean;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  name: string;
}

export interface RegisterResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

export interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isLoading: boolean;
  error: string | null;
}

export interface JWTPayload {
  sub: string;
  email: string;
  role: UserRole;
  exp: number;
}

export const USER_ROLES = {
  ADMIN: "ADM",
  MANAGER: "MGR",
  USER: "USER"
}

export const ROLE_CODE = {
  M_ADMIN: "A00",
  M_MANAGER: "M01",
  M_STAFF: "S01",
  C_ADMIN: "A01",
  C_MANAGER: "M02",
  C_STAFF: "S02",
} as const;