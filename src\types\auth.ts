// src/types/auth.ts
// Auth-related TypeScript interfaces

import { UserRole } from "../constants/auth";
export interface AccessMenu {
  roleCode: string;
  screenCode: string;
  screenName: string;
  canView: number;
  canCreate: number;
  canUpdate: number;
  canDelete: number;
  canImport: number;
  canExport: number;
}

export interface NotificationInfo {
  hubUrl: string;
  token: string;
}

export type RoleCode = typeof ROLE_CODE[keyof typeof ROLE_CODE];

export const ORG_TYPES = {
  INTERNAL: "INTERNAL",
  EXTERNAL: "EXTERNAL",
}

export type OrgType = typeof ORG_TYPES[keyof typeof ORG_TYPES];

export interface User {
  id: number;
  userId: string;
  clientId: string;
  lastName: string;
  firstName: string;
  organizationCode: string;
  organizationName: string;
  departmentCode: string;
  departmentName: string;
  countryCode: string;
  countryName: string;
  roleCode: RoleCode
  roleName: string;
  accessMenus: AccessMenu[];
  notification: NotificationInfo;
  pwdExpireDurationDays: number;
  lastPasswordUpdate: string;
  logoutTime: number;
  lockStatus: number;
  lastLoginDateTime: string;
  imageUrl: string;
  passwordExpiringSoon: boolean;
  daysUntilPasswordExpiry: number;
  passwordExpired: boolean;
  organizationGroupType: OrgType
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  name: string;
}

export interface RegisterResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

export interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isLoading: boolean;
  error: string | null;
}

export interface JWTPayload {
  sub: string;
  email: string;
  role: UserRole;
  exp: number;
}

export const USER_ROLES = {
  ADMIN: "ADM",
  MANAGER: "MGR",
  USER: "USER"
}

export const ROLE_CODE_GROUPS = {
  MANUFACTURER: {
    ADMIN: "A00",
    MANAGER: "M01",
    STAFF: "S01",
  },
  CUSTOMER: {
    ADMIN: "A01",
    MANAGER: "M02",
    STAFF: "S02",
  },
} as const;

export const ROLE_CODE = {
  M_ADMIN: ROLE_CODE_GROUPS.MANUFACTURER.ADMIN,
  M_MANAGER: ROLE_CODE_GROUPS.MANUFACTURER.MANAGER,
  M_STAFF: ROLE_CODE_GROUPS.MANUFACTURER.STAFF,
  C_ADMIN: ROLE_CODE_GROUPS.CUSTOMER.ADMIN,
  C_MANAGER: ROLE_CODE_GROUPS.CUSTOMER.MANAGER,
  C_STAFF: ROLE_CODE_GROUPS.CUSTOMER.STAFF,
} as const;

export type ManufacturerRoleCode = typeof ROLE_CODE_GROUPS.MANUFACTURER[keyof typeof ROLE_CODE_GROUPS.MANUFACTURER];
export type CustomerRoleCode = typeof ROLE_CODE_GROUPS.CUSTOMER[keyof typeof ROLE_CODE_GROUPS.CUSTOMER];

export const ROLE_CATEGORY_BY_CODE: Record<RoleCode, "MANUFACTURER" | "CUSTOMER"> = {
  [ROLE_CODE.M_ADMIN]: "MANUFACTURER",
  [ROLE_CODE.M_MANAGER]: "MANUFACTURER",
  [ROLE_CODE.M_STAFF]: "MANUFACTURER",
  [ROLE_CODE.C_ADMIN]: "CUSTOMER",
  [ROLE_CODE.C_MANAGER]: "CUSTOMER",
  [ROLE_CODE.C_STAFF]: "CUSTOMER",
};

export function isManufacturerRole(code: RoleCode): boolean {
  return ROLE_CATEGORY_BY_CODE[code] === "MANUFACTURER";
}

export function isCustomerRole(code: RoleCode): boolean {
  return ROLE_CATEGORY_BY_CODE[code] === "CUSTOMER";
}