"use client";

import { usePathname } from "next/navigation";
import { useMemo } from "react";
import { useSelector } from "react-redux";
import { selectUserAccessMenus } from "@/features/users/state/usersSelectors";
import { AllMenuItems } from "@/app/(DashboardLayout)/layout/sidebar/MenuItems";

type Menu = { href?: string; screenCode?: string; children?: Menu[] };

export interface UserAccessMenu {
  screenCode: string;
  canView: 0 | 1;
  canCreate?: 0 | 1;
  canUpdate?: 0 | 1;
  canDelete?: 0 | 1;
}

export enum ViewPermission {
  Denied = 0,
  Granted = 1,
}

// Flatten menu tree (pre-order)
function flattenMenus(items: Menu[]): Menu[] {
  return items.flatMap((i) => [i, ...(i.children ? flattenMenus(i.children) : [])]);
}

// Build once per module load (normalize href to absolute)
const FLAT_MENUS: Menu[] = flattenMenus((AllMenuItems as Menu[]) || [])
  .filter((m) => m.href)
  .map((m) => ({
    ...m,
    href: m.href!.startsWith("/") ? m.href : `/${m.href}`,
  }));

// Longest-prefix match (linear scan, no sorting)
function findScreenCodeByPath(path: string): string | undefined {
  let bestHref = "";
  let bestCode: string | undefined;
  for (const m of FLAT_MENUS) {
    const href = m.href as string;
    if (path === href || path.startsWith(href + "/")) {
      if (href.length > bestHref.length) {
        bestHref = href;
        bestCode = m.screenCode;
      }
    }
  }
  return bestCode;
}

// Permissions resolver
// Requirement: route access is controlled ONLY by canView.
// canCreate/canUpdate/canDelete are used later inside the page (actions).
function getPermsForScreenCode(access: UserAccessMenu[] | undefined, sc?: string) {
  if (!sc) {
    // Route not mapped -> treat as public view; actions off
    return { hasAccess: true, canCreate: false, canUpdate: false, canDelete: false };
  }
  if (access == null) {
    // Not ready yet
    return { hasAccess: false, canCreate: false, canUpdate: false, canDelete: false };
  }
  const entry = access.find((a) => a.screenCode === sc);
  const hasAccess = !!entry && entry.canView === ViewPermission.Granted;

  // Actions are just for in-page controls; do not affect route access.
  const canCreate = !!entry && entry.canCreate === ViewPermission.Granted;
  const canUpdate = !!entry && entry.canUpdate === ViewPermission.Granted;
  const canDelete = !!entry && entry.canDelete === ViewPermission.Granted;

  return { hasAccess, canCreate, canUpdate, canDelete };
}

export function useScreenAccessDecision() {
  const pathname = usePathname() ?? "/";
  const userAccessMenus = useSelector(selectUserAccessMenus) as UserAccessMenu[] | undefined;

  // Current route -> screenCode
  const screenCode = useMemo(() => findScreenCodeByPath(pathname), [pathname]);

  const isLoaded =
    userAccessMenus !== undefined &&
    userAccessMenus !== null &&
    !(Array.isArray(userAccessMenus) && userAccessMenus.length === 0);

  // Current route permissions
  const perms = useMemo(() => getPermsForScreenCode(userAccessMenus, screenCode), [userAccessMenus, screenCode]);

  const { hasAccess, canCreate, canUpdate, canDelete } = perms;

  const canDecide = !screenCode || isLoaded;

  const has = (action: "view" | "create" | "update" | "delete") =>
    action === "view" ? hasAccess : action === "create" ? canCreate : action === "update" ? canUpdate : canDelete;

  const canViewByPath = (path: string) => {
    const sc = findScreenCodeByPath(path);
    return getPermsForScreenCode(userAccessMenus, sc).hasAccess;
  };

  return {
    screenCode,
    hasAccess,
    canDecide,
    canCreate,
    canUpdate,
    canDelete,
    has,
    canViewByPath,
  };
}
